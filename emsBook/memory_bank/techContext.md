# 技术上下文

**项目名称**：EMS 预约管理系统

**描述**：一个使用 MobX 状态管理的微信预约管理小程序，旨在提供高效的预约流程管理和资源调度功能。

本文档基于[项目简介](./projectbrief.md)，详细描述项目的技术环境和选择。

## 目的

本文档旨在提供项目的技术上下文，包括技术栈选择、开发环境和技术约束。它为开发团队提供技术指导和参考。

## 内容

### 技术栈概述

- **前端框架**：微信小程序原生框架 (Skyline 渲染引擎)
- **状态管理**：MobX（使用 mobx-miniprogram-lite 实现，已完成状态结构重构，区分核心/领域/UI 状态）
- **计算属性**：miniprogram-computed（版本 4.4.0）
- **样式**：SCSS（通过微信开发者工具的 sass 编译插件支持）
- **数据交互**：基于 Promise 的 wx.request 封装
- **组件框架**：glass-easel（微信小程序新一代组件系统），包含自定义组件库（如 ems-calendar, ems-card, ems-social-interaction 等）
- **渲染模式**：Skyline（高性能渲染引擎）

### 开发环境和工具

- **IDE**：微信开发者工具
- **包管理**：npm
- **版本控制**：Git
- **编译配置**：启用 SWC、增强编译、ES6 转 ES5、代码压缩
- **渲染配置**：启用 Skyline 渲染、Worklet 编译
- **小程序基础库**：2.32.3

### 第三方库和依赖

- **mobx-miniprogram-lite**：轻量级小程序 MobX 实现，用于响应式状态管理
- **miniprogram-computed**：提供计算属性功能，用于派生数据
- **mobx**：响应式状态管理库的核心，由 mobx-miniprogram-lite 引用
- **fast-deep-equal**：深度比较对象是否相等
- **rfdc**：快速深度克隆对象

### API 和集成点

- **微信登录 API**：用于用户认证，包括获取用户信息、角色和绑定账号
- **微信支付 API**：用于支付功能，包括支付请求、结果处理和退款流程（与发票状态同步）
- **云服务 API**：与后端服务交互的 RESTful API，基础 URL 为本地开发环境
- **文件上传 API**：用于图片等资源上传，通过 wxUpload 方法封装
- **微信订阅消息**：支持订单、退款、评论等多种消息模板
- **微信分享能力**：支持普通分享和自定义海报分享
- **地图服务 API**：用于地图定位和查找附近服务点
- **发票状态管理 (invoiceState)**：用于管理发票信息，并与退款流程集成
- **社交互动 API**：用于处理点赞、收藏、评论、分享等操作

### 技术约束和限制

- 小程序框架的固有限制（如包大小限制、API 使用限制）
- 微信开放能力的使用条件和审核要求
- 状态管理的性能考量（已通过重构和优化部分缓解）
- 跨平台兼容性考虑
- Skyline 渲染引擎的特定限制和优化要求（持续探索中）
- 小程序组件通信方式的局限性
- 微信支付流程的严格规范要求
- 社交分享功能的平台限制

### 部署和运维策略

- 小程序版本发布流程，包括版本更新检测和强制更新机制
- 灰度发布策略
- 监控和日志收集
- 错误跟踪和性能优化
- 多环境配置管理（开发环境与生产环境）
- 小程序更新机制的用户体验优化
- 组件懒加载策略（通过 lazyCodeLoading 配置）

### 技术风险和缓解措施

- 小程序框架更新带来的兼容性问题，通过版本检测和适配缓解
- 状态管理复杂度增加的风险，通过模块化状态设计（已完成重构）和严格的更新模式缓解
- API 变更的影响，通过统一的 API 封装层隔离变更
- 性能瓶颈和优化策略，包括：
  - 社交互动组件的性能优化（防抖处理、批量更新状态、并行请求、预加载评论）
  - 使用 Skyline 渲染引擎提升渲染性能
  - 组件懒加载减少初始加载时间
  - 状态粒度优化避免不必要的重渲染
  - 状态管理结构重构提升可维护性

## 关联文档

本文档与以下文档相关联：

- [项目简介](./projectbrief.md)（上游文档）
- [系统模式](./systemPatterns.md)（相关文档）
- [活动上下文](./activeContext.md)（下游文档）
