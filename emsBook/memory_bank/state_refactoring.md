# 状态管理结构重构方案

## 目标

重构状态结构，提高模块化，解决当前状态管理的复杂性问题。通过更清晰的状态分层和依赖关系，提高代码的可维护性、可测试性和性能。

## 当前状态

通过分析当前的状态管理结构，我们发现以下问题：

1. **状态分散**：各个模块的状态分散在不同文件中，缺乏统一的组织结构
2. **依赖关系不明确**：模块间的依赖关系不清晰，导致状态更新时可能产生连锁反应
3. **缺乏状态分层**：没有明确区分本地状态、UI 状态和业务状态
4. **状态更新机制不一致**：不同模块使用不同的状态更新模式
5. **缺乏性能优化**：没有充分利用 MobX 提供的性能优化特性

## 重构方案

### 1. 状态模块化结构

重新组织状态管理结构，采用以下层次：

```
store/
  ├── index.js                # 根状态，导出所有状态模块
  ├── core/                   # 核心状态（全局共享）
  │   ├── app.js              # 应用全局状态
  │   ├── ui.js               # UI全局状态
  │   └── auth.js             # 认证状态
  ├── domain/                 # 领域状态（业务模块）
  │   ├── user/               # 用户相关状态
  │   │   ├── index.js        # 用户状态导出
  │   │   ├── profile.js      # 用户资料状态
  │   │   └── preferences.js  # 用户偏好状态
  │   ├── order/              # 订单相关状态
  │   │   ├── index.js        # 订单状态导出
  │   │   ├── list.js         # 订单列表状态
  │   │   └── detail.js       # 订单详情状态
  │   ├── payment/            # 支付相关状态
  │   │   ├── index.js        # 支付状态导出
  │   │   ├── history.js      # 支付历史状态
  │   │   └── transaction.js  # 交易状态
  │   └── social/             # 社交相关状态
  │       ├── index.js        # 社交状态导出
  │       ├── interaction.js  # 互动状态（点赞、收藏）
  │       ├── comments.js     # 评论状态
  │       └── sharing.js      # 分享状态
  └── utils/                  # 状态工具函数
      ├── persistence.js      # 状态持久化
      ├── middleware.js       # 状态中间件
      └── helpers.js          # 辅助函数
```

### 2. 状态分层设计

将状态分为三个层次：

1. **核心状态（Core State）**：全局共享的应用状态，如认证信息、全局 UI 状态等
2. **领域状态（Domain State）**：特定业务领域的状态，如用户、订单、支付、社交等
3. **UI 状态（UI State）**：与特定 UI 组件相关的临时状态，通常不需要全局共享

### 3. 状态模块接口规范

每个状态模块应遵循以下接口规范：

```javascript
// 示例：domain/social/interaction.js
import {
  observable,
  computed,
  action,
  runInAction,
} from "mobx-miniprogram-lite";
import { api } from "../../../utils/api";

// 状态定义
export const interactionState = observable({
  // 状态数据（使用有意义的名称）
  likedItems: [],
  favoritedItems: [],
  isLoading: false,
  lastError: null,

  // 计算属性（使用get前缀）
  get hasLikedItems() {
    return this.likedItems.length > 0;
  },

  get hasFavoritedItems() {
    return this.favoritedItems.length > 0;
  },

  // 操作方法（使用动词开头）
  async fetchLikedItems() {
    this.isLoading = true;
    this.lastError = null;

    try {
      const res = await api();
      runInAction(() => {
        this.likedItems = res.data.list || [];
      });
      return res.data.list;
    } catch (error) {
      runInAction(() => {
        this.lastError = error;
      });
      return [];
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  },

  // 其他方法...
});
```

### 4. 状态依赖管理

明确状态模块之间的依赖关系，避免循环依赖：

```javascript
// store/index.js - 根状态导出
import { appState } from "./core/app";
import { authState } from "./core/auth";
import { uiState } from "./core/ui";

import { userState } from "./domain/user";
import { orderState } from "./domain/order";
import { paymentState } from "./domain/payment";
import { socialState } from "./domain/social";

// 导出所有状态模块
export const rootState = {
  // 核心状态
  app: appState,
  auth: authState,
  ui: uiState,

  // 领域状态
  user: userState,
  order: orderState,
  payment: paymentState,
  social: socialState,
};
```

### 5. 性能优化策略

利用 MobX 提供的性能优化特性：

1. **精细化观察**：只观察真正需要的状态
2. **计算属性**：使用计算属性代替直接计算
3. **批量更新**：使用 runInAction 批量更新状态
4. **防抖处理**：对频繁触发的状态更新进行防抖处理
5. **懒加载状态**：按需加载状态模块

```javascript
// 示例：批量更新和防抖处理
import { observable, runInAction } from "mobx-miniprogram-lite";
import { debounce } from "../../../utils/helpers";

export const commentsState = observable({
  comments: [],
  isLoading: false,

  // 批量更新示例
  updateMultipleComments(newComments) {
    runInAction(() => {
      this.comments = [...this.comments, ...newComments];
      this.isLoading = false;
      this.lastUpdated = new Date();
    });
  },

  // 防抖处理示例
  debouncedFetchComments: debounce(async function (postId) {
    this.isLoading = true;
    try {
      const res = await wxRequest({}, `social/comments/${postId}`);
      this.updateMultipleComments(res.data.list || []);
    } catch (error) {
      runInAction(() => {
        this.isLoading = false;
        this.lastError = error;
      });
    }
  }, 300),
});
```

### 6. 状态持久化

实现统一的状态持久化机制：

```javascript
// store/utils/persistence.js
export const persistState = (key, state) => {
  wx.setStorage({
    key,
    data: JSON.stringify(state),
  });
};

export const loadPersistedState = (key, defaultState = {}) => {
  try {
    const data = wx.getStorageSync(key);
    return data ? JSON.parse(data) : defaultState;
  } catch (error) {
    console.error(`Failed to load persisted state for ${key}:`, error);
    return defaultState;
  }
};

// 使用示例
import { observable, runInAction } from "mobx-miniprogram-lite";
import { persistState, loadPersistedState } from "../../utils/persistence";

const STORAGE_KEY = "user_profile";

export const profileState = observable({
  // 从持久化存储加载初始状态
  ...loadPersistedState(STORAGE_KEY, {
    nickname: "",
    avatar: "",
    // 其他默认值
  }),

  // 更新并持久化
  updateProfile(newProfile) {
    runInAction(() => {
      Object.assign(this, newProfile);
      persistState(STORAGE_KEY, this);
    });
  },
});
```

## 实施步骤

1. **分析当前状态**：全面分析现有状态管理结构和依赖关系
2. **设计新结构**：根据上述方案设计新的状态管理结构
3. **创建核心状态**：实现核心状态模块（app、auth、ui）
4. **重构领域状态**：按业务领域重构状态模块
5. **更新组件引用**：更新组件中的状态引用
6. **测试验证**：全面测试重构后的状态管理
7. **性能优化**：应用性能优化策略
8. **文档更新**：更新状态管理相关文档

## 预期收益

1. **提高可维护性**：清晰的状态结构和依赖关系，使代码更易于理解和维护
2. **提高可测试性**：模块化的状态更容易进行单元测试
3. **提高性能**：优化的状态更新机制，减少不必要的重渲染
4. **提高开发效率**：统一的状态管理模式，减少开发者的认知负担
5. **提高可扩展性**：模块化的状态结构，使添加新功能更加容易

## 风险和缓解措施

1. **重构风险**：可能引入新的 bug
   - 缓解：增加测试覆盖率，逐步重构，而不是一次性重构所有状态
2. **学习曲线**：团队需要适应新的状态管理模式
   - 缓解：提供详细文档和示例，进行团队培训
3. **性能影响**：重构可能暂时影响性能
   - 缓解：进行性能基准测试，确保重构后性能不下降

## 结论

通过重构状态结构，提高模块化，我们可以解决当前状态管理的复杂性问题，提高代码质量和开发效率。这一重构将为未来的功能开发和维护奠定坚实的基础。
