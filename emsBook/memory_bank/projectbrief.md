# 项目简介

本文档是项目的起点，提供项目的基本信息和概述。

## 目的

本文档旨在提供项目的高级概述，包括项目目标、范围和主要功能。它是其他文档的基础，包括产品上下文、系统模式和技术上下文。

## 内容

### 项目名称和描述

**项目名称**：EMS 预约管理系统

**描述**：一个使用 MobX 状态管理的微信预约管理小程序，旨在提供高效的预约流程管理和资源调度功能。

### 项目目标和愿景

- 为用户提供一个直观、高效的预约管理平台
- 简化预约流程和资源管理
- 建立用户社区，促进资源共享
- 通过数据分析优化预约体验

### 主要功能概述

- 用户账户管理和个人资料
- 资源浏览、搜索和分类
- 预约和资源管理（包括订单管理、预约码）
- 日历视图的预约系统
- 地图定位相关服务
- 社交互动功能（点赞、收藏、评论、分享、评价）
- 支付集成（包括支付历史、退款处理，与发票状态同步）
- 发票管理（已完成重构）
- 基于 MobX 的状态管理

### 目标用户群体

- 需要预约服务的终端用户
- 资源管理和调度人员
- 机构管理员
- 业务运营团队

### 项目时间线和里程碑（当前状态）

- **已完成**:
  - 基础架构和核心功能（用户、资源、预约、日历、地图、支付、订单、评价、社交互动）
  - MobX 状态管理集成与重构
  - **已完成**: 发票模块状态管理重构
  - 社交互动组件开发与优化
  - 微信分享与海报生成
- **进行中**:
  - 完善退款流程和发票状态同步
  - 整体用户体验优化
  - 全面测试与性能调优
- **后续计划**:
  - 发布准备与上线
  - 持续迭代与功能增强

### 关键利益相关者

- 开发团队
- 产品经理
- 设计师
- 测试团队
- 目标用户代表
- 合作机构/资源方

## 关联文档

本文档与以下文档相关联：

- [产品上下文](./productContext.md)
- [系统模式](./systemPatterns.md)
- [技术上下文](./techContext.md)
