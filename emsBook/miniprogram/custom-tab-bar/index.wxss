.tab-bar {
  position: absolute;
  bottom: env(safe-area-inset-bottom);
  left: 60rpx;
  right: 60rpx;
  height: 120rpx;
  /* background: white; */
  display: flex;
  flex-direction: row;
  pointer-events: auto;
  border-radius: 20rpx;
  box-shadow: 0px 0px 6px -6px rgba(0, 0, 0, 0.12);
  padding: 20rpx;
  backdrop-filter: blur(30px);
  overflow: hidden;
}
.tab-bar::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #ffffffbb;
  filter: blur(30px);
  margin: -30px;
  z-index: -1;
}

.tab-bar-item {
  flex: 0.5;
  text-align: center;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  background: transparent;
  border-radius: 16rpx;
  margin: 0 16rpx;
  height: 100%;
  overflow: hidden;
  box-shadow: 0 0 0 rgba(0, 0, 0, 0);
}

.tab-bar-item .icon {
  width: 60rpx;
  height: 60rpx;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  /* transform: translateX(-150px);
  filter: drop-shadow(150px 0px black);
  transform: translateZ(0); */
}

.tab-bar-item .text {
  font-size: 32rpx;
  margin-left: 32rpx;
  line-height: 100%;
  font-weight: 500;
  white-space: nowrap;
  flex-wrap: nowrap;
  opacity: 0;
  transform: translateX(-10rpx);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  width: 0;
  overflow: hidden;
}

.tab-bar-item.active {
  flex: 1;
  /* border: solid 1px #e35048; */
  background-color: #ffffff;
  /* box-shadow: inset 10rpx -10rpx 30rpx #bebebe, inset -10rpx 10rpx 30rpx #ffffff; */
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.tab-bar-item.active .icon {
  transform: scale(1.1);
}

.tab-bar-item.active .text {
  opacity: 1;
  transform: translateX(0);
  width: auto;
  overflow: visible;
}
