const app = getApp();
const { sysInfo, themeData } = app.globalData;

Component({
  data: {
    selected: 0,
    color: themeData.unSelectedColor,
    selectedColor: themeData.selectedColor,
    backgroundColor: "#ffffff",
    list: [
      {
        pagePath: "/pages/home/<USER>",
        iconPath: "/static/tabbar/home.png",
        selectedIconPath: "/static/tabbar/homeActive.png",
        text: "首页",
      },
      {
        pagePath: "/pages/map/index",
        iconPath: "/static/tabbar/map.png",
        selectedIconPath: "/static/tabbar/mapActive.png",
        text: "地图",
      },
      {
        pagePath: "/pages/category/index",
        iconPath: "/static/tabbar/shop.png",
        selectedIconPath: "/static/tabbar/shopActive.png",
        text: "点单",
      },
      {
        pagePath: "/pages/ucenter/ucenter",
        iconPath: "/static/tabbar/profile.png",
        selectedIconPath: "/static/tabbar/profileActive.png",
        text: "个人",
      },
    ],
    safeArea: sysInfo.windowHeight - sysInfo.safeArea.bottom,
  },
  lifetimes: {
    attached() { },
  },
  methods: {
    switchTab(e) {
      const { index } = e.currentTarget.dataset;
      const url = this.data.list[index].pagePath;
      wx.switchTab({ url });
      this.setData({
        selected: index,
      });
    },
  },
});
