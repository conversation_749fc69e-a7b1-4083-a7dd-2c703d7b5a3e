<!-- miniprogram/custom-tab-bar/index.wxml -->
<view class="tab-bar" style="bottom: {{safeArea|| 20}}px;">
  <view wx:for="{{list}}" wx:key="index" class="tab-bar-item {{selected===index&&'active'}}" data-index="{{index}}" bindtap="switchTab">
    <image class="icon" lazyload src="{{selected === index ? item.selectedIconPath : item.iconPath}}" mode="widthFix"></image>
    <view class="text" style="color: {{selected === index ? selectedColor : color}};">
      {{item.text}}
    </view>
  </view>
</view>