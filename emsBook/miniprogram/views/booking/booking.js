// views/booking/booking.js
import { connectPage } from "mobx-miniprogram-lite";
import { orderStore } from "../../store/order/index";
import { userInfoStore } from "../../store/user/index";
import { paymentStore } from "../../store/payment/index";
import { navTo } from "../../utils/common/tools";

connectPage({
  store: {
    orderStore,
    userInfoStore,
    paymentStore,
  },
  data: {
    date: "",
    id: "",
    bookDetail: {},
    timeSlots: [
      { id: 1, time: "09:00 - 11:00", available: true },
      { id: 2, time: "11:00 - 13:00", available: true },
      { id: 3, time: "13:00 - 15:00", available: false },
      { id: 4, time: "15:00 - 17:00", available: true },
      { id: 5, time: "17:00 - 19:00", available: true },
    ],
    selectedTimeSlot: null,
    remarks: "",
    submitting: false,
  },

  onLoad(options) {
    this.setData({
      id: options.id,
      date: JSON.parse(options.date).date,
    });

    // 模拟获取图书详情
    this.setData({
      bookDetail: {
        id: this.data.id,
        name: "小王子",
        author: "安托万·德·圣-埃克苏佩里",
        coverUrl: "/static/images/logo.jpg",
        price: 5.0,
        location: "中央图书馆",
        address: "北京市海淀区中关村南大街5号",
      },
    });
  },

  selectTimeSlot(e) {
    const { id } = e.currentTarget.dataset;
    const { timeSlots } = this.data;
    const slot = timeSlots.find((slot) => slot.id === id);

    if (!slot.available) {
      wx.showToast({
        title: "该时段已被预约",
        icon: "none",
      });
      return;
    }

    this.setData({
      selectedTimeSlot: id,
    });
  },

  inputRemarks(e) {
    this.setData({
      remarks: e.detail.value,
    });
  },

  async submitOrder() {
    if (!this.data.selectedTimeSlot) {
      wx.showToast({
        title: "请选择预约时段",
        icon: "none",
      });
      return;
    }

    if (!userInfoStore.hasLogin) {
      wx.showModal({
        title: "提示",
        content: "请先登录后再预约",
        confirmText: "去登录",
        success: (res) => {
          if (res.confirm) {
            navTo("/pages/ucenter/ucenter");
          }
        },
      });
      return;
    }

    this.setData({ submitting: true });

    const selectedSlot = this.data.timeSlots.find(
      (slot) => slot.id === this.data.selectedTimeSlot
    );

    const orderData = {
      bookId: this.data.id,
      bookName: this.data.bookDetail.name,
      coverUrl: this.data.bookDetail.coverUrl,
      orderDate: this.data.date,
      timeSlot: selectedSlot.time,
      remarks: this.data.remarks,
      price: this.data.bookDetail.price,
      location: this.data.bookDetail.location,
      returnTime: this.calculateReturnTime(this.data.date),
    };

    try {
      // 1. 创建订单
      const orderResult = await orderStore.createOrder(orderData);

      if (!orderResult) {
        throw new Error("创建订单失败");
      }

      // 2. 发起支付
      const paymentData = {
        orderId: orderResult.id,
        amount: orderData.price,
        description: `预约《${orderData.bookName}》`,
      };

      const paymentResult = await paymentStore.requestWxPayment(paymentData);

      // 3. 处理支付结果
      if (paymentResult.success) {
        // 支付成功，跳转到支付成功页面
        wx.redirectTo({
          url: `/views/payment/result/index?paymentId=${paymentResult.paymentId}&orderId=${orderResult.id}&status=success`,
        });
      } else {
        // 支付失败或取消，跳转到支付结果页面
        const status = paymentResult.canceled ? "cancel" : "fail";
        wx.redirectTo({
          url: `/views/payment/result/index?orderId=${orderResult.id}&status=${status}&message=${paymentResult.message}`,
        });
      }
    } catch (error) {
      console.error("预约失败:", error);
      wx.showToast({
        title: error.message || "预约失败，请重试",
        icon: "none",
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  calculateReturnTime(date) {
    // 简单计算，默认借阅期为15天
    const returnDate = new Date(date);
    returnDate.setDate(returnDate.getDate() + 15);
    return returnDate.toISOString().split("T")[0];
  },

  viewLocation() {
    wx.navigateTo({
      url: `/pages/map/detail?id=${this.data.id}&name=${this.data.bookDetail.name}&address=${this.data.bookDetail.address}`,
    });
  },
});
