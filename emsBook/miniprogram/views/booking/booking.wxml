<list-wrap isEmpty="{{false}}">
  <ems-navbar slot='navbar' title="预约订单详情"></ems-navbar>
  <list-view>
    <view class="book-info">
      <image class="book-cover" src="{{bookDetail.coverUrl}}" mode="aspectFill"></image>
      <view class="book-details">
        <view class="book-name">{{bookDetail.name}}</view>
        <view class="book-author">{{bookDetail.author}}</view>
        <view class="book-location">{{bookDetail.location}}</view>
      </view>
    </view>
    
    <view class="section-title">预约日期</view>
    <view class="date-display">{{date}}</view>
    
    <view class="section-title">预约时段</view>
    <view class="time-slots">
      <view 
        wx:for="{{timeSlots}}" 
        wx:key="id"
        class="time-slot {{item.available ? '' : 'disabled'}} {{selectedTimeSlot === item.id ? 'selected' : ''}}"
        data-id="{{item.id}}"
        bindtap="selectTimeSlot"
      >
        {{item.time}}
      </view>
    </view>
    
    <view class="section-title">备注信息</view>
    <textarea class="remarks" placeholder="请输入备注信息（选填）" bindinput="inputRemarks" value="{{remarks}}"></textarea>
    
    <view class="location-section">
      <view class="section-title">图书馆位置</view>
      <view class="location-info" bindtap="viewLocation">
        <view class="location-name">{{bookDetail.location}}</view>
        <view class="location-address">{{bookDetail.address}}</view>
        <view class="view-map">查看地图 ></view>
      </view>
    </view>
    
    <view class="qrcode-section">
      <ems-qrcode text="{{date}}"></ems-qrcode>
    </view>
    
    <button class="submit-btn" bindtap="submitOrder" loading="{{submitting}}">立即支付</button>
  </list-view>
</list-wrap>