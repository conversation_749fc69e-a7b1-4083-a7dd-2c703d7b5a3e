/* views/booking/booking.wxss */

.book-info {
  display: flex;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.book-cover {
  width: 180rpx;
  height: 240rpx;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.book-details {
  flex: 1;
  margin-left: 30rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.book-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.book-author {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.book-location {
  font-size: 28rpx;
  color: #07c160;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin: 30rpx 0 20rpx;
}

.date-display {
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-radius: 8rpx;
  font-size: 30rpx;
  color: #333;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.time-slots {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.time-slot {
  width: calc(50% - 10rpx);
  padding: 20rpx 0;
  text-align: center;
  background-color: #fff;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.time-slot.selected {
  background-color: #07c160;
  color: #fff;
}

.time-slot.disabled {
  background-color: #f5f5f5;
  color: #999;
}

.remarks {
  width: 100%;
  height: 200rpx;
  background-color: #fff;
  border-radius: 8rpx;
  padding: 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.location-section {
  margin-bottom: 30rpx;
}

.location-info {
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.location-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.location-address {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.view-map {
  font-size: 28rpx;
  color: #07c160;
  text-align: right;
}

.qrcode-section {
  display: flex;
  justify-content: center;
  margin: 40rpx 0;
}

.submit-btn {
  background-color: #07c160;
  color: #fff;
  border-radius: 10rpx;
  font-size: 32rpx;
  padding: 20rpx 0;
  width: 100%;
  margin-top: 40rpx;
  margin-bottom: 60rpx;
}

.submit-btn:active {
  opacity: 0.8;
}
