import { navTo } from "../../utils/common/tools";

// const app = getApp();

Page({
  data: {
    id: "",
    detail: {},
    weekDays: [],
  },
  onLoad: function (options) {
    this.data.detail.id = options.id;
  },
  onReady() { },
  getChildComponent() {
    const child = this.selectComponent("#calendar");
    this.data.detail.date = this.data.detail.date || child.data.selectedDate;
  },
  onPickDateChange(event) {
    console.log("parent onPickDateChange", event);
  },
  onControl(event) {
    console.log("parent onControl", event);
  },
  onPickDay(event) {
    console.log("parent onPickDay", event);
    this.data.detail = event.detail;
  },
  onRangePick(event) {
    console.log("parent onRangePick", event);
  },
  onSubmit(e) {
    this.getChildComponent();
    navTo(
      `/components/ems-profileEdit/index?profile=${JSON.stringify(
        this.data.detail
      )}`,
      "wx://modal-navigation"
    );
  },
  confirm(e) {
    // this.getChildComponent();
    // navTo(
    //   `/views/booking/booking?id=${this.data.id}&date=${JSON.stringify(
    //     this.data.detail
    //   )}`,
    //   "wx://modal-navigation"
    // );
  },
});
