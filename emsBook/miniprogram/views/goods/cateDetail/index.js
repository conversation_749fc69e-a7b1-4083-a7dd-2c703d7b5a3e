// views/goods/cateDetail/index.js
import { connectComponent } from "mobx-miniprogram-lite";
import { rootState } from "../../../store/index";

connectComponent({
  store: {
    product: rootState.product.detailState,
  },
  /**
   * 组件的属性列表
   */
  properties: {
    id: {
      type: Number,
      value: 0,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    likeIcon: "\u{e61d}",
    cartIcon: "\u{e612}",
    isLike: true,
  },

  /**
   * 组件的方法列表
   */
  lifetimes: {
    attached() {
      this.store.product.fetchProductDetail(this.data.id);

    },
  },
  methods: {},
});
