$bgColor: #f4ece3;
$actionColor: #ebba6c;
.bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: -1;
  height: calc(40vh + env(safe-area-inset-top));
  background: linear-gradient(
    to right,
    $bgColor 0,
    $bgColor 80%,
    #ffffff 80%,
    #ffffff 100%
  );
}
.info {
  height: calc(40vh - 44px - 20rpx);
  width: 100%;
  display: flex;
  flex-direction: row;
  .left {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text {
      margin-bottom: 48rpx;
      margin-left: 20rpx;
    }
    .brand {
      font-size: 22rpx;
      color: gray;
    }
    .goodsName {
      font-size: 34rpx;
      font-weight: 700;
    }
    .spec {
      font-size: 26rpx;
      color: #fff;
      background-color: $actionColor;
      padding: 16rpx;
      border-radius: 8rpx;
    }
    .price {
      font-size: 28rpx;
    }
    .addCart {
      background-color: red;
      color: #fff;
      font-size: 30rpx;
      position: absolute;
      width: 200rpx;
      bottom: -16rpx;
      left: calc(50% - 250rpx);
      padding: 16rpx;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      margin: 0;
      // &::after {
      //   border: none;
      //   box-shadow: none;
      // }
      text {
        margin: 0;
        margin-left: 20rpx;
      }
    }
  }
  .right {
    height: 100%;
    width: 150rpx;
    display: flex;
    flex-direction: column;

    .likeIcon {
      position: absolute;
      left: 48rpx;
    }
    .bannerImg {
      width: 500rpx;
      position: absolute;
      bottom: -120rpx;
      right: -20rpx;
      .img {
        width: 100%;
      }
    }
  }
}
.overview {
  padding-top: 90rpx;
  height: 400rpx;
  display: flex;
  flex-direction: column;

  .header {
    height: 60rpx;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    & > text:first-child {
      color: $actionColor;
      font-weight: 900;
      margin-right: 20rpx;
    }
    & > text:last-child {
      font-size: 32rpx;
      font-weight: 600;
    }
  }
  .content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;
    .item {
      width: calc(50vw - 30rpx);
      height: 120rpx;
      border-radius: 12rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 20rpx;
      margin-bottom: 20rpx;
      &_image {
        height: 100%;
        width: 80rpx;
        margin-right: 20rpx;
      }

      &_content {
        display: flex;
        flex-direction: column;

        &__title {
          font-size: 32rpx;
          font-weight: 600;
          margin-bottom: 10rpx;
        }

        &__subTitle {
          font-size: 24rpx;
          color: darkgrey;
        }
      }
    }
  }
}

.desc {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-top: 60rpx;
  .header {
    height: 60rpx;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    & > text:first-child {
      color: $actionColor;
      font-weight: 900;
      margin-right: 20rpx;
    }
    & > text:last-child {
      font-size: 32rpx;
      font-weight: 600;
    }
  }
  .content {
  }
}
