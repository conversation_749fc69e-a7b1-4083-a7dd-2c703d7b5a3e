<view class="bg"></view>
<list-wrap isLoading="{{product.loading}}" isEmpty="{{!product.productDetail && !product.loading}}">
  <navbar slot='navbar'></navbar>
  <list-view>
    <view class="info">
      <view class="left">
        <text class="brand">{{product.productDetail.name}}</text>
        <text class="goodsName">{{product.productDetail.description}}</text>
        <text class="spec">{{product.productDetail.attributes[0]+product.productDetail.attributes[1]}}</text>
        <text class="price">{{product.productDetail.market_price}}</text>
        <button class="addCart">
          <text>添加</text>
          <text class="iconfont">{{cartIcon}}</text>
        </button>
      </view>
      <view class="right">
        <text class="iconfont likeIcon" style="color: {{isLike?'red':'gray'}};">{{likeIcon}}</text>
        <share-element rect-tween-type="fastOutSlowIn" key="item-{{product.productDetail.id}}" shuttle-on-push="to" transition-on-gesture class="bannerImg">
          <image class="img" src="{{product.productDetail.cover_image_url}}" mode="widthFix" />
        </share-element>
      </view>
    </view>
    <view class="overview">
      <view class="header">
        <text>—</text>
        <text>商品概述</text>
      </view>
      <view class="content">
        <view class="item" wx:for="{{4}}">
          <image class="item_image" src="/static/images/pokemon_location.png" mode="widthFix" />
          <view class="item_content">
            <text class="item_content__title">1100-1600m</text>
            <text class="item_content__subTitle">Altitude</text>
          </view>
        </view>
      </view>
    </view>
    <view class="desc">
      <view class="header">
        <text>—</text>
        <text>商品详情</text>
      </view>
      <!-- <view class="content"></view> -->
      <!-- <rich-text nodes="{{product.productDetail.rich_description}}" /> -->
    </view>
  </list-view>
</list-wrap>