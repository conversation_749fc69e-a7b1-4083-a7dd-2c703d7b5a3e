<swiper class="banner" autoplay circular style="height:calc(100vh - {{sheetHeight*minSize}}px + 40px);" indicator-dots indicator-type="jumpWithOffset" indicator-alignment="{{[-1,-1]}}" indicator-offset='{{indicatorOffset}}' indicator-color="rgba(255,255,255,0.5)" indicator-active-color="{{themeData.selectedColor}}">
  <swiper-item wx:for="{{5}}">
    <image class="swiperImg" src="{{headImg}}" mode="scaleToFill" />
  </swiper-item>
</swiper>
<view class="search-wrp" style="padding-top: {{menuRect.bottom + menuRect.height}}px;">
  <view class="search">
    <image class="location" src="/static/images/pokemon_location.png" />
    <view class="input-wrp">
      <input adjust-position="{{false}}" class="input" type="text" placeholder="Search here" />
    </view>
    <image class="headImg" src="{{headImg}}" mode="aspectFill" />
  </view>
</view>
<draggable-sheet class="sheet" style="height: {{sheetHeight}}px;" initial-child-size="{{minSize}}" min-child-size="{{minSize}}" max-child-size="{{maxSize}}" snap="{{false}}" snap-sizes="{{[]}}" worklet:onsizeupdate="onSizeUpdate">
  <!-- Update data binding to access store properties -->
  <list-wrap bgColor="transparent" padding="0" noSafeBottom associativeContainer="draggable-sheet" isEmpty="{{listState.shops.length==0}}" loadingStatus="{{listState.isLoading}}" hasMore="{{listState.hasMore}}" bindreachBottom='reachBottom'>
    <view slot='header'>
      <view class="bar">
        <view class="indicator" />
      </view>
      <view class="row title">
        <text>门店列表</text>
        <view class="outer-circle center">
          <view class="inner-circle" />
        </view>
      </view>
    </view>
    <list-view>
      <!-- Update data binding for the loop -->
      <block wx:for="{{listState.shops}}" wx:key="id">
        <!-- Pass item data to the component, ensure ems-card expects 'item' -->
        <ems-card bgColor='#f5f5f5' item="{{item}}" index="{{index}}" bindtap="goToDetail" data-id="{{item.id}}"></ems-card>
        <view class="spacer" />
      </block>

    </list-view>
  </list-wrap>
</draggable-sheet>