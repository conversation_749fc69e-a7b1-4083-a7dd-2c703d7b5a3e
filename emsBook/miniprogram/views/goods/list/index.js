import { installRouteBuilder } from "./route";
import { connectComponent } from "mobx-miniprogram-lite";
import { rootState } from "../../../store/index";
const themeBehavior = require("../../../behavior/theme");
const { shared, Easing } = wx.worklet;

const lerpColor = (begin, end, t) => {
  "worklet";
  const r = begin.r + (end.r - begin.r) * t;
  const g = begin.g + (end.g - begin.g) * t;
  const b = begin.b + (end.b - begin.b) * t;
  const a = begin.a + (end.a - begin.a) * t;
  return `rgba(${r}, ${g}, ${b}, ${a})`;
};
const app = getApp();
const { windowHeight } = app.globalData.sysInfo;
const menuRect = app.globalData.menuRect;
const sheetHeight = windowHeight - (menuRect.bottom + menuRect.height + 60);

const Curves = {
  easeInCubic: Easing.cubicBezier(0.55, 0.055, 0.675, 0.19),
};

const headImg =
  "https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fsafe-img.xhscdn.com%2Fbw1%2Fd064be90-6b8c-4a6d-9721-837206fbb4a7%3FimageView2%2F2%2Fw%2F1080%2Fformat%2Fjpg&refer=http%3A%2F%2Fsafe-img.xhscdn.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1696759051&t=b90e0bf861e41c30214fc739ae66343f";

connectComponent({
  store: {
    ...rootState.shop,
    auth: rootState.auth,
  },
  behaviors: [themeBehavior],
  properties: {},
  data: {
    headImg,
    menuRect,
    sheetHeight,
    minSize: 0.6,
    maxSize: 1,
    indicatorOffset: [20, menuRect.bottom - menuRect.height],
    listQuery: {
      page_num: 1,
      page_size: 10,
    },
  },
  methods: {
    loadMore() {
      this.store.listState.fetchShopList(this.data.listQuery);
    },
    onSizeUpdate(e) {
      "worklet";
      const distance = sheetHeight - e.pixels;
      this.progress.value = distance >= 20 ? 1 : distance / 20;
    },
    reachBottom: function () {
      if (this.store.listState.isLoading || !this.store.listState.hasMore)
        return;
      this.setData({
        "listQuery.page": this.data.listQuery.page + 1,
      });
      this.loadMore();
    },

    goToDetail(e) {
      const { id } = e.currentTarget.dataset;
      wx.navigateTo({
        url: `/views/goods/detail/index?id=${id}`, // Adjust path if necessary
      });
    },
  },
  lifetimes: {
    created() {
      installRouteBuilder();
      if (this.store.auth.isAuthenticated) {
        this.loadMore();
      }
    },
    attached() {
      const progress = shared(1);
      this.progress = progress;

      this.applyAnimatedStyle(".inner-circle", () => {
        "worklet";
        let t = progress.value;
        const scale = 1 - 0.2 * t;
        return {
          transform: `scale(${scale})`,
        };
      });

      this.applyAnimatedStyle(".outer-circle", () => {
        "worklet";
        let t = progress.value;
        return {
          opacity: t,
        };
      });

      this.applyAnimatedStyle(".search", () => {
        "worklet";
        const t = progress.value;
        const beginColor = {
          r: 241,
          g: 233,
          b: 233,
          a: 1,
        };

        const endColor = {
          r: 255,
          g: 255,
          b: 255,
          a: 1,
        };

        const spreadRadius = -6 + 8 * t;
        const bgColor = lerpColor(beginColor, endColor, t);

        return {
          backgroundColor: `${bgColor}`,
          boxShadow: `0px 0px 6px ${spreadRadius}px rgba(0, 0, 0, 0.12)`,
        };
      });

      this.applyAnimatedStyle(".banner", () => {
        "worklet";
        const t = progress.value;
        return {
          backgroundColor: `rgb(245,245,245, ${t})`,
          opacity: `${t}`,
        };
      });
      this.applyAnimatedStyle(".search-wrp", () => {
        "worklet";
        const t = progress.value;
        return {
          backgroundColor: `rgb(245,245,245, ${1 - t})`,
          opacity: `${1 - t}`,
        };
      });

      this.applyAnimatedStyle(".bar", () => {
        "worklet";
        const t = progress.value;
        return {
          borderTopRightRadius: 25 * t + "px",
          borderTopLeftRadius: 25 * t + "px",
        };
      });
      this.applyAnimatedStyle(".indicator", () => {
        "worklet";
        const t = progress.value;
        return {
          opacity: t,
        };
      });
    },
    detached() {},
  },
  onPullDownRefresh() {
    this.onRefresh();
  },

  onRefresh() {
    this.setData({
      "listQuery.page_num": 1,
    });
    this.store.listState.clearShops();
    this.loadMore().then(() => {
      wx.stopPullDownRefresh();
    });
  },
});
