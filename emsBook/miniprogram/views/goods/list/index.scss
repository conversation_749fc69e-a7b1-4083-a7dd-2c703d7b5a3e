.banner {
  position: absolute;
  width: 100vw;
  background: transparent;
  opacity: 1;
}
.swiperImg {
  width: 100%;
  height: 100%;
}

.search-wrp {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  /* height: 30vh; */
  padding: 0 25px;
  display: flex;
  padding-bottom: 40px;
  opacity: 0;
}

.search {
  box-sizing: border-box;
  border-radius: 30px;
  width: 100%;
  height: 60px;
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: rgb(241, 233, 233);
  box-shadow: 0px 0px 6px -6px rgba(0, 0, 0, 0.12);
  padding: 0 20px;
}
.input-wrp {
  height: 100%;
  flex: 1;
  color: grey;
  font-size: 16px;
  padding: 30rpx 0;
}
.input {
  height: 100%;
  line-height: 100%;
}

.location {
  width: 30px;
  height: 30px;
  margin-right: 20px;
  box-sizing: border-box;
}

.headImg {
  width: 30px;
  height: 30px;
  border-radius: 50%;
}
.spacer {
  background-color: #f5f5f5;
  height: 10px;
}
.sheet {
  width: 100%;
  position: absolute;
  bottom: 0;
  background: transparent;
  z-index: 99;
}
.scrollable {
  height: 100%;
  overflow: hidden;
  border-top-left-radius: 25px;
  border-top-right-radius: 25px;
  background-color: rgb(241, 233, 233);
  /* padding-bottom: calc(60px + env(safe-area-inset-bottom)); */
}

.bar {
  height: 40px;
  box-sizing: border-box;
  background-color: #f5f5f5;
  border-top-left-radius: 25px;
  border-top-right-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  /* box-shadow: 0px -12px 5px 7px rgba(0, 0, 0, 0.12); */
}

.indicator {
  background-color: #bebaba;
  border-radius: 3px;
  height: 4px;
  width: 40px;
}

.title {
  position: relative;
  height: 60px;
  box-sizing: border-box;
  padding: 0px 25px 20px;
  font-size: 24px;
  background-color: rgb(245, 245, 245);
}

.inner-circle {
  width: 60px;
  height: 60px;
  transform: scale(1);
  border-radius: 50%;
  background-color: rgb(114, 155, 156);
}

.outer-circle {
  position: absolute;
  top: -10px;
  right: 20vw;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: black;
}


