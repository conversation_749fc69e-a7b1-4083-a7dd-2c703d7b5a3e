import { connectComponent } from "mobx-miniprogram-lite";
import { rootState } from "../../../store/index";
const themeBehavior = require("../../../behavior/theme");
const { navTo } = require("../../../utils/common/tools");

const componentOptions = {
  store: {
    ...rootState.product,
    auth: rootState.auth,
  },
  behaviors: [themeBehavior],
  properties: {},
  data: {
    selected: 0,
    cartIcon: "\u{e612}",
    searchIcon: "\u{e620}",
  },
  methods: {
    selectTab(e) {
      const { index, id } = e.currentTarget.dataset;
      this.setData({
        selected: index,
      });
      this.store.listState.fetchProductList({ categoryId: id });
    },
    toCart(e) {
      navTo("/views/shopcart/index", "wx://bottom-sheet");
    },
    loadData() {
      this.store.categoryState.fetchCategoryList().then((res) => {
        if (res.items && res.items.length > 0) {
          const firstCategoryId = res.items[0].id;
          this.store.listState.fetchProductList({ categoryId: firstCategoryId });
        }
      });
    },
  },
  lifetimes: {
    attached() {
      this.loadData();
    },
  },
};

connectComponent(componentOptions);
