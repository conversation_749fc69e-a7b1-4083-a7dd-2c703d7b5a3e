<view class="container">
  <view class="left">
    <scroll-view class="scroll" type="list" show-scrollbar="{{false}}" bounces="{{false}}" scroll-with-animation scroll-into-view="category-{{selected}}" scroll-y scroll-into-view-alignment="center">
      <view id="category-{{index}}" class="cateItem {{selected==index&&'active'}}" wx:for="{{categoryState.categories}}" catch:tap="selectTab" data-index="{{index}}" data-id="{{item.id}}">
        <text>{{item.name}}</text>
      </view>
    </scroll-view>
    <view class="action">
      <text class="iconfont" catch:tap="toCart">{{cartIcon}}</text>
      <text class="iconfont">{{searchIcon}}</text>
    </view>
  </view>
  <view class="right">
    <list-wrap noSafeBottom isEmpty='{{!listState.hasProducts}}' loadingStatus="{{store.listState.isLoading}}">
      <view slot='navbar' class="navbar">头盔</view>
      <view class="banner" slot='extend'>
        <swiper class="swiper">
          <swiper-item>
            <image class="img" src="http://res.yqbaijiu.com/20240115/ea3e2e9b1b68428a95f474caf73d295a.jpg" mode="widthFix" />
          </swiper-item>
        </swiper>
      </view>
      <list-view>
        <ems-catelist-item wx:for="{{listState.products}}" item="{{item}}"></ems-catelist-item>
      </list-view>
    </list-wrap>
  </view>
</view>