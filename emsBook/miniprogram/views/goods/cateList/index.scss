$unSelectColor: #f4ece3;
$selectColor: #ecba6d;
$fontColor: rgb(44, 35, 31);
.container {
  height: 100vh;
  display: flex;
  flex-direction: row;
  .left {
    height: 100%;
    width: 120rpx;
    display: flex;
    flex-direction: column;
    background-color: $unSelectColor;
    .action {
      display: flex;
      flex-direction: column;
      padding-bottom: calc(60px + env(safe-area-inset-bottom));
      text {
        height: 120rpx;
        line-height: 100rpx;
        text-align: center;
        vertical-align: middle;
        font-size: 40rpx;
      }
    }
    .scroll {
      flex: 1;
      display: flex;
      flex-direction: column;
      .cateItem {
        min-height: 180rpx;
        background-color: $unSelectColor;
        transition: all 0.3s;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: $fontColor;

        text {
          transform: rotateZ(-90deg);
          transition: transform 0.3s;
        }
        &:first-child {
          min-height: calc(180rpx + env(safe-area-inset-top));
        }
      }
      .active {
        background-color: $selectColor;
        min-height: calc(180rpx + env(safe-area-inset-top));
        color: #ffffff;

        text {
          transform: rotateZ(0deg);
        }
      }
    }
  }
  .right {
    flex: 1;
    height: 100%;
    padding-top: env(safe-area-inset-top);
    .navbar {
      height: 88rpx;
      display: flex;
      align-items: center;
      padding: 0 20rpx;
      font-size: 34rpx;
      font-weight: 600;
    }
    .banner {
      width: 100%;
      padding: 20rpx;
      .swiper {
        width: 100%;
        .img {
          width: 100%;
        }
      }
    }
  }
}
