<pan-gesture-handler worklet:ongesture="handlePanGesture">
  <view id="fake-host">
    <view id="page">
      <view class="navigation-bar">
        <view class="navigation-bar-content">
          <navigator class="icon" open-type="navigateBack">
            <image src="https://images.vrm.cn/2019/08/29/left-arrow.png" style="width: 100%;" mode="widthFix"></image>
          </navigator>
          <image class="navigation-bar-avatar" src="https://res.wx.qq.com/op_res/BqgN85sXxTbk1kynEEihrwQPXmcn86nOyioRnPAfkrbZteUWsKfEpgoYZ1pk-3TMTc_qXFSElIgkvILR-zzh1Q" />
          <view class="navigation-bar-title">{{shopcampaignDetail.name}}</view>
          <view class="navigation-bar-follow" catch:tap="preOrder" data-id="{{id}}">预约</view>
        </view>
      </view>
      <share-element key="se-key{{index}}" shuttle-on-push="from" transition-on-gesture="{{true}}" rect-tween-type="cubic-bezier(0.4, 0.0, 0.2, 1.0)" style="width: 100%; height: 0px;">
        <view></view>
      </share-element>
      <scroll-view scroll-y style="flex: 1; width: 100%; overflow: hidden;" type="list" show-scrollbar="{{false}}">
        <view>
          <!-- swiper 还可以做个手势协商，此处省略，即在当前是第一张图时，右滑触动页面返回交互 -->
          <swiper id="swiper" style="width: 100%; height: {{swiperHeight}}px;transition: all 0.2s ease-out;" layout-type="transformer" transformer-type="threeD" indicator-dots indicator-type="expand">
            <swiper-item>
              <image class="detail-image" mode="widthFix" src="{{url}}" />
            </swiper-item>
            <swiper-item wx:for="{{imageList}}">
              <image class="detail-image" mode="widthFix" src="{{item}}" />
            </swiper-item>
          </swiper>
        </view>
        <view class="detail-content">
          <view class="detail-title">{{shopcampaignDetail.description}}</view>
        </view>
      </scroll-view>
      <view class="footer">
        <view class="footer-content">
          <input class="footer-input" placeholder="说点什么..." />
          <span>
            <text class="footer-icon">❤️</text>
            317
          </span>
          <span>
            <text class="footer-icon">⭐️</text>
            723
          </span>
        </view>
      </view>
    </view>
  </view>
</pan-gesture-handler>