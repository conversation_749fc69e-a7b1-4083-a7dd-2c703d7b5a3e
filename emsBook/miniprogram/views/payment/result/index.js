// views/payment/result/index.js
import { connectPage } from "mobx-miniprogram-lite";
import { paymentStore } from "../../../store/payment/index";
import { orderStore } from "../../../store/order/index";
import { navTo } from "../../../utils/common/tools";

connectPage({
  store: {
    paymentStore,
    orderStore,
  },
  data: {
    paymentId: "",
    orderId: "",
    paymentStatus: "", // success, fail, cancel
    paymentMessage: "",
    paymentDetail: {},
    loading: true,
  },

  onLoad(options) {
    const { paymentId, orderId, status, message } = options;

    this.setData({
      paymentId,
      orderId,
      paymentStatus: status || "success",
      paymentMessage: message || "",
    });

    if (paymentId) {
      this.getPaymentDetail(paymentId);
    }
  },

  async getPaymentDetail(paymentId) {
    try {
      const detail = await paymentStore.getPaymentDetail(paymentId);
      if (detail) {
        this.setData({
          paymentDetail: detail,
          loading: false,
        });
      }
    } catch (error) {
      console.error("获取支付详情失败:", error);
      this.setData({
        loading: false,
      });
    }
  },

  // 查看订单详情
  viewOrder() {
    const { orderId } = this.data;
    if (orderId) {
      wx.navigateTo({
        url: `/views/orderList/orderList?id=${orderId}`,
      });
    } else {
      wx.showToast({
        title: "订单信息不存在",
        icon: "none",
      });
    }
  },

  // 返回首页
  goHome() {
    wx.switchTab({
      url: "/pages/home/<USER>",
    });
  },

  // 重新支付
  async repay() {
    const { paymentDetail, orderId } = this.data;

    if (!paymentDetail || !paymentDetail.id) {
      wx.showToast({
        title: "支付信息不存在，请重试",
        icon: "none",
      });
      return;
    }

    try {
      // 重新发起支付
      const paymentData = {
        orderId: orderId,
        amount: paymentDetail.amount,
        description: paymentDetail.description,
      };

      const result = await paymentStore.requestWxPayment(paymentData);

      if (result.success) {
        // 支付成功，跳转到支付成功页面
        wx.redirectTo({
          url: `/views/payment/result/index?paymentId=${result.paymentId}&orderId=${orderId}&status=success`,
        });
      } else {
        // 支付失败，更新当前页面状态
        this.setData({
          paymentStatus: result.canceled ? "cancel" : "fail",
          paymentMessage: result.message,
        });
      }
    } catch (error) {
      console.error("重新支付失败:", error);
      wx.showToast({
        title: "重新支付失败，请稍后再试",
        icon: "none",
      });
    }
  },
});
