/* views/payment/result/index.scss */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

.payment-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 60rpx;
  padding: 40rpx;
  background-color: #fff;
  border-radius: 16rpx;
  margin-left: 30rpx;
  margin-right: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.result-icon {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40rpx;
  font-size: 80rpx;
  color: #fff;
}

.result-icon.success {
  background-color: #07c160;
}

.result-icon.fail {
  background-color: #fa5151;
}

.result-icon.cancel {
  background-color: #ffc300;
}

.result-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.result-message {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  text-align: center;
}

.payment-detail {
  width: 100%;
  margin-top: 40rpx;
  margin-bottom: 40rpx;
  border-top: 1px solid #eee;
  padding-top: 30rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  font-size: 28rpx;
}

.detail-item .label {
  color: #666;
}

.detail-item .value {
  color: #333;
  font-weight: 500;
}

.loading {
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-top: 40rpx;
}

.btn {
  margin-bottom: 20rpx;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  border-radius: 44rpx;
}

.btn.primary {
  background-color: #0075ff;
  color: #fff;
}

.btn.secondary {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}