<!-- views/payment/result/index.wxml -->
<view class="container">
  <ems-navbar slot='navbar' title="支付结果"></ems-navbar>
  
  <view class="payment-result">
    <!-- 支付结果图标 -->
    <view class="result-icon {{paymentStatus}}">
      <view wx:if="{{paymentStatus === 'success'}}" class="icon-success">✓</view>
      <view wx:elif="{{paymentStatus === 'fail'}}" class="icon-fail">✕</view>
      <view wx:elif="{{paymentStatus === 'cancel'}}" class="icon-cancel">!</view>
    </view>
    
    <!-- 支付结果标题 -->
    <view class="result-title">
      <text wx:if="{{paymentStatus === 'success'}}">支付成功</text>
      <text wx:elif="{{paymentStatus === 'fail'}}">支付失败</text>
      <text wx:elif="{{paymentStatus === 'cancel'}}">支付已取消</text>
    </view>
    
    <!-- 支付结果消息 -->
    <view class="result-message" wx:if="{{paymentMessage}}">
      {{paymentMessage}}
    </view>
    
    <!-- 支付详情 -->
    <view class="payment-detail" wx:if="{{!loading && paymentDetail.id}}">
      <view class="detail-item">
        <text class="label">订单金额</text>
        <text class="value">¥ {{paymentDetail.amount || '0.00'}}</text>
      </view>
      <view class="detail-item">
        <text class="label">支付时间</text>
        <text class="value">{{paymentDetail.payTime || '未完成'}}</text>
      </view>
      <view class="detail-item">
        <text class="label">订单描述</text>
        <text class="value">{{paymentDetail.description || '无'}}</text>
      </view>
      <view class="detail-item">
        <text class="label">交易单号</text>
        <text class="value">{{paymentDetail.id || '无'}}</text>
      </view>
    </view>
    
    <!-- 加载中 -->
    <view class="loading" wx:if="{{loading}}">
      <text>加载中...</text>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <block wx:if="{{paymentStatus === 'success'}}">
        <button class="btn primary" bindtap="viewOrder">查看订单</button>
        <button class="btn secondary" bindtap="goHome">返回首页</button>
      </block>
      <block wx:elif="{{paymentStatus === 'fail' || paymentStatus === 'cancel'}}">
        <button class="btn primary" bindtap="repay">重新支付</button>
        <button class="btn secondary" bindtap="goHome">返回首页</button>
      </block>
    </view>
  </view>
</view>