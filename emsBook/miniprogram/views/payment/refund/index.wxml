<!-- views/payment/refund/index.wxml -->
<view class="container">
  <ems-navbar slot='navbar' title="申请退款"></ems-navbar>
  
  <view class="refund-container" wx:if="{{!loading}}">
    <!-- 支付信息 -->
    <view class="payment-info">
      <view class="info-title">支付信息</view>
      <view class="info-item">
        <text class="label">订单金额</text>
        <text class="value">¥ {{paymentDetail.amount || '0.00'}}</text>
      </view>
      <view class="info-item">
        <text class="label">支付时间</text>
        <text class="value">{{paymentDetail.payTime || '未知'}}</text>
      </view>
      <view class="info-item">
        <text class="label">订单描述</text>
        <text class="value">{{paymentDetail.description || '无'}}</text>
      </view>
      <view class="info-item">
        <text class="label">交易单号</text>
        <text class="value">{{paymentDetail.id || '无'}}</text>
      </view>
    </view>
    
    <!-- 退款原因 -->
    <view class="reason-section">
      <view class="section-title">退款原因</view>
      <view class="reason-list">
        <view 
          wx:for="{{reasonOptions}}" 
          wx:key="id" 
          class="reason-item {{item.selected ? 'selected' : ''}}" 
          bindtap="selectReason" 
          data-index="{{index}}"
        >
          <view class="reason-radio">
            <view class="radio-inner" wx:if="{{item.selected}}"></view>
          </view>
          <text class="reason-text">{{item.text}}</text>
        </view>
      </view>
    </view>
    
    <!-- 其他原因输入框 -->
    <view class="other-reason" wx:if="{{reasonOptions[4].selected}}">
      <textarea 
        placeholder="请输入具体原因..." 
        bindinput="inputOtherReason"
        value="{{otherReason}}"
        maxlength="200"
      ></textarea>
      <view class="word-count">{{otherReason.length}}/200</view>
    </view>
    
    <!-- 退款说明 -->
    <view class="refund-notice">
      <view class="notice-title">退款说明</view>
      <view class="notice-item">1. 退款申请提交后，将在1-3个工作日内处理</view>
      <view class="notice-item">2. 退款金额将原路返回至支付账户</view>
      <view class="notice-item">3. 如有疑问，请联系客服</view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button 
        class="cancel-btn" 
        bindtap="cancelRefund"
        disabled="{{submitting}}"
      >
        取消
      </button>
      <button 
        class="submit-btn" 
        bindtap="submitRefund" 
        loading="{{submitting}}" 
        disabled="{{submitting}}"
      >
        提交申请
      </button>
    </view>
  </view>
  
  <!-- 加载中 -->
  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>
</view>