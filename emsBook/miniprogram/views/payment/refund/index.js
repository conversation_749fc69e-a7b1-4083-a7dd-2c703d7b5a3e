// views/payment/refund/index.js
import { connectPage } from "mobx-miniprogram-lite";
import { paymentStore } from "../../../store/payment/index";
import { orderStore } from "../../../store/order/index"; // Keep for displaying order details
// Remove unused imports related to direct status handling
// import {
//   handleRefundStatusChange,
//   REFUND_STATUS,
// } from "../../../utils/orderService";

connectPage({
  store: {
    paymentStore,
    orderStore,
  },
  data: {
    paymentId: "",
    orderId: "",
    paymentDetail: {},
    orderDetail: {},
    refundReason: "",
    reasonOptions: [
      { id: 1, text: "计划有变，不需要了", selected: false },
      { id: 2, text: "对服务不满意", selected: false },
      { id: 3, text: "找到了更合适的选择", selected: false },
      { id: 4, text: "操作错误，重新下单", selected: false },
      { id: 5, text: "其他原因", selected: false },
    ],
    otherReason: "",
    submitting: false,
    loading: true,
  },

  onLoad(options) {
    const { paymentId, orderId } = options;

    this.setData({
      paymentId,
      orderId,
    });

    if (paymentId) {
      this.getPaymentDetail(paymentId);
    }

    if (orderId) {
      this.getOrderDetail(orderId);
    }
  },

  async getPaymentDetail(paymentId) {
    try {
      const detail = await paymentStore.getPaymentDetail(paymentId);
      if (detail) {
        this.setData({
          paymentDetail: detail,
          loading: false,
        });
      }
    } catch (error) {
      console.error("获取支付详情失败:", error);
      this.setData({
        loading: false,
      });
      wx.showToast({
        title: "获取支付详情失败",
        icon: "none",
      });
    }
  },

  async getOrderDetail(orderId) {
    try {
      const detail = await orderStore.getOrderDetail(orderId);
      if (detail) {
        this.setData({
          orderDetail: detail,
        });
      }
    } catch (error) {
      console.error("获取订单详情失败:", error);
    }
  },

  // 选择退款原因
  selectReason(e) {
    const index = e.currentTarget.dataset.index;
    const reasonOptions = this.data.reasonOptions.map((item, i) => {
      return {
        ...item,
        selected: i === index,
      };
    });

    const selectedReason = reasonOptions[index].text;

    this.setData({
      reasonOptions,
      refundReason: selectedReason,
    });
  },

  // 输入其他原因
  inputOtherReason(e) {
    this.setData({
      otherReason: e.detail.value,
    });
  },

  // 提交退款申请
  async submitRefund() {
    const {
      paymentId,
      refundReason,
      otherReason,
      reasonOptions,
      orderDetail,
      orderId,
    } = this.data;

    // 验证是否选择了退款原因
    if (!refundReason) {
      wx.showToast({
        title: "请选择退款原因",
        icon: "none",
      });
      return;
    }

    // 如果选择了"其他原因"但没有填写具体原因
    const isOtherSelected = reasonOptions.find(
      (item) => item.selected && item.text === "其他原因"
    );
    if (isOtherSelected && !otherReason.trim()) {
      wx.showToast({
        title: "请填写具体原因",
        icon: "none",
      });
      return;
    }

    this.setData({ submitting: true });

    try {
      // 构建退款数据
      const refundData = {
        paymentId,
        reason: isOtherSelected ? otherReason : refundReason,
        amount: this.data.paymentDetail.amount,
        orderInfo: {
          id: this.data.orderId,
          bookName: orderDetail?.bookName || "未知商品",
          orderTime: orderDetail?.orderTime || new Date().toLocaleString(),
        },
      };

      // 提交退款申请 - 传递 orderId
      const result = await paymentStore.refundPayment(
        paymentId,
        orderId,
        refundData.reason
      ); // Pass orderId and only the reason string

      if (result) {
        // Status update is now handled within paymentStore.refundPayment

        wx.showToast({
          title: "退款申请提交成功",
          icon: "success",
        });

        // 导航到退款结果页面 - 状态可以根据实际情况调整，这里用 'success' 或 'processing'
        setTimeout(() => {
          wx.navigateTo({
            url: `/views/payment/refund/result/index?paymentId=${paymentId}&orderId=${orderId}&status=success&reason=${encodeURIComponent(
              refundData.reason
            )}`,
          });
        }, 1000);
      } else {
        wx.showToast({
          title: "退款申请提交失败，请重试",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("提交退款申请失败:", error);

      // 根据错误类型显示不同的错误信息
      let errorMessage = "提交退款申请失败，请重试";
      if (error.errMsg) {
        if (error.errMsg.includes("timeout")) {
          errorMessage = "网络超时，请重试";
        } else if (error.errMsg.includes("fail")) {
          errorMessage = "网络错误，请检查网络连接";
        }
      }

      wx.showToast({
        title: errorMessage,
        icon: "none",
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  // 取消退款
  cancelRefund() {
    wx.navigateBack();
  },
});
