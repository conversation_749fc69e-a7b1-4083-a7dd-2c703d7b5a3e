<!-- views/payment/refund/result/index.wxml -->
<view class="container">
    <ems-navbar slot='navbar' title="退款结果"></ems-navbar>
    <view class="refund-result">
        <!-- 退款结果图标 -->
        <view class="result-icon {{refundStatus}}">
            <view wx:if="{{refundStatus === 'success'}}" class="icon-success">✓</view>
            <view wx:elif="{{refundStatus === 'processing'}}" class="icon-processing">⟳</view>
            <view wx:elif="{{refundStatus === 'fail'}}" class="icon-fail">✕</view>
        </view>
        <!-- 退款结果标题 -->
        <view class="result-title">
            <text wx:if="{{refundStatus === 'success'}}">退款申请成功</text>
            <text wx:elif="{{refundStatus === 'processing'}}">退款处理中</text>
            <text wx:elif="{{refundStatus === 'fail'}}">退款申请失败</text>
        </view>
        <!-- 退款结果消息 -->
        <view class="result-message" wx:if="{{refundMessage}}">{{refundMessage}}</view>
        <!-- 退款详情 -->
        <view class="refund-detail" wx:if="{{!loading && refundDetail.id}}">
            <view class="detail-item">
                <text class="label">退款金额</text>
                <text class="value">¥ {{refundDetail.amount || '0.00'}}</text>
            </view>
            <view class="detail-item">
                <text class="label">申请时间</text>
                <text class="value">{{refundDetail.refundTime || '未知'}}</text>
            </view>
            <view class="detail-item">
                <text class="label">退款原因</text>
                <text class="value">{{refundDetail.reason || '无'}}</text>
            </view>
            <view class="detail-item">
                <text class="label">交易单号</text>
                <text class="value">{{refundDetail.id || '无'}}</text>
            </view>
            <view class="detail-item" wx:if="{{refundDetail.estimateTime}}">
                <text class="label">预计到账时间</text>
                <text class="value">{{refundDetail.estimateTime}}</text>
            </view>
        </view>
        <!-- 退款说明 -->
        <view class="refund-notice" wx:if="{{refundStatus === 'processing'}}">
            <view class="notice-title">退款说明</view>
            <view class="notice-item">1. 您的退款申请已提交，正在处理中</view>
            <view class="notice-item">2. 退款将在1-3个工作日内处理完成</view>
            <view class="notice-item">3. 退款金额将原路返回至支付账户</view>
            <view class="notice-item">4. 如有疑问，请联系客服</view>
        </view>
        <!-- 加载中 -->
        <view class="loading" wx:if="{{loading}}">
            <text>加载中...</text>
        </view>
        <!-- 操作按钮 -->
        <view class="action-buttons">
            <button class="btn primary" bindtap="viewOrder">查看订单</button>
            <button class="btn secondary" bindtap="goToOrderList">订单列表</button>
            <button class="btn plain" bindtap="goHome">返回首页</button>
        </view>
    </view>
</view>