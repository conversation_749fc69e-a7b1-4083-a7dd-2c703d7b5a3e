/* views/payment/refund/result/index.scss */

.container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.refund-result {
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 结果图标样式 */
.result-icon {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 30rpx;
  font-size: 80rpx;
  color: #fff;
}

.result-icon.success {
  background-color: #07c160;
}

.result-icon.processing {
  background-color: #1989fa;
}

.result-icon.fail {
  background-color: #ee0a24;
}

.icon-success, .icon-fail, .icon-processing {
  font-weight: bold;
}

.icon-processing {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 结果标题样式 */
.result-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

/* 结果消息样式 */
.result-message {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
  padding: 0 30rpx;
}

/* 退款详情样式 */
.refund-detail {
  width: 100%;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  font-size: 28rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.detail-item:last-child {
  border-bottom: none;
}

.label {
  color: #666;
}

.value {
  color: #333;
  font-weight: 500;
}

/* 退款说明样式 */
.refund-notice {
  width: 100%;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.notice-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 15rpx;
}

.notice-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 10rpx;
}

/* 操作按钮样式 */
.action-buttons {
  width: 100%;
  margin-top: 40rpx;
  display: flex;
  flex-direction: column;
}

.btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.btn.primary {
  background-color: #07c160;
  color: #fff;
}

.btn.secondary {
  background-color: #f8f8f8;
  color: #333;
  border: 1rpx solid #ddd;
}

.btn.plain {
  background-color: transparent;
  color: #666;
  border: 1rpx solid #eee;
}

/* 加载中样式 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  color: #999;
  font-size: 28rpx;
}