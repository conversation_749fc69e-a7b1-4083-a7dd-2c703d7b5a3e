// views/payment/refund/result/index.js
import { connectPage } from "mobx-miniprogram-lite";
import { paymentStore } from "../../../../store/payment/index";
// Import invoiceState instead of orderStore
import { invoiceState } from "../../../../store/domain/invoice";
import { navTo } from "../../../../utils/common/tools";
// Remove unused import
// import { handleRefundStatusChange } from "../../../../utils/orderService";

connectPage({
  store: {
    paymentStore,
    // Use invoiceState in the store mapping if needed, or remove if not directly used in template
    invoiceState, // Assuming it might be used in the template or other methods
  },
  data: {
    paymentId: "",
    orderId: "",
    refundId: "",
    refundStatus: "", // success, processing, fail
    refundMessage: "",
    refundDetail: {},
    loading: true,
  },

  onLoad(options) {
    const { paymentId, orderId, refundId, status, message, reason } = options;

    this.setData({
      paymentId,
      orderId,
      refundId: refundId || paymentId,
      refundStatus: status || "processing",
      refundMessage: message || "",
    });

    if (paymentId) {
      this.getRefundDetail(paymentId, reason);
    }

    // 同步更新订单状态
    if (orderId && status) {
      this.syncOrderStatus(orderId, status, { reason: reason });
    }

    // 页面卸载时清除定时器
    this.onUnload = () => {
      if (this.statusCheckTimer) {
        clearTimeout(this.statusCheckTimer);
        this.statusCheckTimer = null;
      }
    };
  },

  async getRefundDetail(paymentId, urlReason) {
    try {
      const detail = await paymentStore.getPaymentDetail(paymentId);
      if (detail) {
        // 处理退款原因，优先使用URL参数中的原因
        let reason = "用户申请退款";

        if (urlReason) {
          reason = decodeURIComponent(urlReason);
        } else if (detail.reason) {
          reason = detail.reason;
        }

        // 更新退款详情
        this.setData({
          refundDetail: {
            ...detail,
            refundTime:
              detail.refundTime ||
              detail.updateTime ||
              new Date().toLocaleString(),
            estimateTime: this.calculateEstimateTime(),
            reason: reason,
            // 添加退款状态的中文描述
            statusText:
              detail.refundStatus === "success"
                ? "退款成功"
                : detail.refundStatus === "processing"
                ? "处理中"
                : detail.refundStatus === "fail"
                ? "退款失败"
                : "未知状态",
          },
          loading: false,
        });

        // 如果API返回的状态与URL参数不同，以API为准
        if (
          detail.refundStatus &&
          detail.refundStatus !== this.data.refundStatus
        ) {
          this.setData({
            refundStatus: detail.refundStatus,
          });

          // 同步更新订单状态
          if (this.data.orderId) {
            this.syncOrderStatus(this.data.orderId, detail.refundStatus, {
              reason: reason,
            });
          }
        }

        // 如果状态是处理中，设置定时器定期检查退款状态
        if (this.data.refundStatus === "processing") {
          this.startRefundStatusCheck();
        }
      }
    } catch (error) {
      console.error("获取退款详情失败:", error);
      this.setData({
        loading: false,
        refundMessage: "获取退款详情失败，请稍后刷新页面",
      });

      // 显示错误提示
      wx.showToast({
        title: "获取退款详情失败",
        icon: "none",
      });
    }
  },

  // 开始定期检查退款状态
  startRefundStatusCheck() {
    // 清除可能存在的旧定时器
    if (this.statusCheckTimer) {
      clearTimeout(this.statusCheckTimer);
    }

    // 设置新定时器，每30秒检查一次
    this.statusCheckTimer = setTimeout(() => {
      this.checkRefundStatus();
    }, 30000); // 30秒
  },

  // 检查退款状态
  async checkRefundStatus() {
    const { paymentId, orderId } = this.data;
    if (!paymentId) return;

    // 记录重试次数
    this.retryCount = this.retryCount || 0;

    try {
      const detail = await paymentStore.getPaymentDetail(paymentId);
      // 重置重试计数
      this.retryCount = 0;

      if (detail && detail.refundStatus) {
        // 如果状态有变化
        if (detail.refundStatus !== this.data.refundStatus) {
          // 更新页面状态
          this.setData({
            refundStatus: detail.refundStatus,
            refundDetail: {
              ...this.data.refundDetail,
              ...detail,
              updateTime: detail.updateTime || new Date().toLocaleString(),
            },
          });

          // 根据状态显示不同的提示信息
          let statusMessage = "";
          if (detail.refundStatus === "success") {
            statusMessage = "退款已成功，资金将原路返回到您的支付账户";
          } else if (detail.refundStatus === "fail") {
            statusMessage = detail.failReason || "退款申请失败，请联系客服";
          }

          if (statusMessage) {
            this.setData({ refundMessage: statusMessage });
          }

          // 同步更新订单状态
          if (orderId) {
            this.syncOrderStatus(orderId, detail.refundStatus);
          }

          // 如果退款完成或失败，停止检查并显示提示
          if (
            detail.refundStatus === "success" ||
            detail.refundStatus === "fail"
          ) {
            if (this.statusCheckTimer) {
              clearTimeout(this.statusCheckTimer);
              this.statusCheckTimer = null;
            }

            // 显示状态变更提示
            wx.showToast({
              title:
                detail.refundStatus === "success"
                  ? "退款已成功"
                  : "退款处理失败",
              icon: detail.refundStatus === "success" ? "success" : "none",
              duration: 2000,
            });
          }
        }
      }

      // 如果状态仍是处理中，继续检查
      if (this.data.refundStatus === "processing") {
        this.startRefundStatusCheck();
      }
    } catch (error) {
      console.error("检查退款状态失败:", error);

      // 增加重试计数
      this.retryCount++;

      // 如果重试次数超过限制，延长检查间隔
      const retryDelay = this.retryCount > 3 ? 60000 : 30000; // 超过3次失败后，改为1分钟检查一次

      // 出错时也继续检查，但使用延长的间隔
      if (this.statusCheckTimer) {
        clearTimeout(this.statusCheckTimer);
      }

      this.statusCheckTimer = setTimeout(() => {
        this.checkRefundStatus();
      }, retryDelay);

      // 如果网络错误，显示提示
      if (error.errMsg && error.errMsg.includes("request:fail")) {
        wx.showToast({
          title: "网络异常，稍后重试",
          icon: "none",
        });
      }
    }
  },

  // 计算预计到账时间（1-3个工作日）
  calculateEstimateTime() {
    const now = new Date();
    const threeDaysLater = new Date(now);
    threeDaysLater.setDate(now.getDate() + 3);

    return `${now.toLocaleDateString()} - ${threeDaysLater.toLocaleDateString()}`;
  },

  // 查看退款详情
  viewRefundDetail() {
    const { paymentId } = this.data;
    if (paymentId) {
      wx.navigateTo({
        url: `/views/payment/refund/index?paymentId=${paymentId}`,
      });
    } else {
      wx.showToast({
        title: "退款信息不存在",
        icon: "none",
      });
    }
  },

  // 查看订单详情
  viewOrder() {
    const { orderId } = this.data;
    if (orderId) {
      wx.navigateTo({
        url: `/views/orderList/orderList?id=${orderId}`,
      });
    } else {
      wx.showToast({
        title: "订单信息不存在",
        icon: "none",
      });
    }
  },

  // 返回订单列表
  goToOrderList() {
    wx.switchTab({
      url: "/pages/ucenter/index",
      success: () => {
        // 跳转到我的订单页面
        setTimeout(() => {
          wx.navigateTo({
            url: "/views/orderList/orderList",
          });
        }, 300);
      },
    });
  },

  // 返回首页
  goHome() {
    wx.switchTab({
      url: "/pages/home/<USER>",
    });
  },

  // 同步发票状态与退款状态
  async syncOrderStatus(invoiceId, refundStatus, extraData = {}) {
    // Renamed orderId to invoiceId for clarity
    if (!invoiceId || !refundStatus) return;

    // Map refund status to invoice status ('refunded' for success, potentially keep original for fail/processing)
    const targetInvoiceStatus = refundStatus === "success" ? "refunded" : null;

    // Only update invoice status if refund is successful
    if (targetInvoiceStatus) {
      try {
        // Directly update invoice detail and list states
        invoiceState.detail.updateInvoiceStatus(invoiceId, targetInvoiceStatus);
        invoiceState.list.updateInvoiceStatusInList(
          invoiceId,
          targetInvoiceStatus
        );
        console.log(
          `Synced invoice ${invoiceId} status to ${targetInvoiceStatus}`
        );

        // 如果是退款成功，刷新页面数据以显示最新状态
        // 延迟一下再刷新，确保状态更新已在UI反映
        setTimeout(() => {
          this.getRefundDetail(
            this.data.paymentId,
            this.data.refundDetail.reason
          ); // Pass reason back if needed
        }, 500); // Shorter delay might be sufficient
      } catch (error) {
        console.error(`同步发票 ${invoiceId} 状态失败:`, error);
        // 显示错误提示
        wx.showToast({
          title: "同步发票状态失败",
          icon: "none",
        });
      }
    } else {
      console.log(
        `Refund status (${refundStatus}) does not require invoice status change.`
      );
      // Optionally refresh details even if status doesn't change to 'refunded'
      if (refundStatus === "fail" || refundStatus === "processing") {
        setTimeout(() => {
          this.getRefundDetail(
            this.data.paymentId,
            this.data.refundDetail.reason
          );
        }, 500);
      }
    }
  },
});
