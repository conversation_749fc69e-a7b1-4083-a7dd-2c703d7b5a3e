<!-- views/orderList/review/index.wxml -->
<view class="container">
  <ems-navbar slot='navbar' title="订单评价"></ems-navbar>
  
  <view class="review-container" wx:if="{{!loading}}">
    <!-- 订单信息 -->
    <view class="order-info">
      <view class="book-info">
        <image class="book-cover" src="{{orderDetail.coverUrl || '/static/images/logo.jpg'}}" mode="aspectFill"></image>
        <view class="book-detail">
          <view class="book-name">{{orderDetail.bookName || '未知书籍'}}</view>
          <view class="order-time">订单时间：{{orderDetail.orderTime || '未知'}}</view>
          <view class="order-price">订单金额：¥{{orderDetail.price || '0.00'}}</view>
        </view>
      </view>
    </view>
    
    <!-- 评分 -->
    <view class="rating-section">
      <view class="section-title">评分</view>
      <view class="rating-stars">
        <view 
          class="star {{rating >= 1 ? 'active' : ''}}" 
          bindtap="setRating" 
          data-rating="1"
        >★</view>
        <view 
          class="star {{rating >= 2 ? 'active' : ''}}" 
          bindtap="setRating" 
          data-rating="2"
        >★</view>
        <view 
          class="star {{rating >= 3 ? 'active' : ''}}" 
          bindtap="setRating" 
          data-rating="3"
        >★</view>
        <view 
          class="star {{rating >= 4 ? 'active' : ''}}" 
          bindtap="setRating" 
          data-rating="4"
        >★</view>
        <view 
          class="star {{rating >= 5 ? 'active' : ''}}" 
          bindtap="setRating" 
          data-rating="5"
        >★</view>
      </view>
    </view>
    
    <!-- 评价内容 -->
    <view class="content-section">
      <view class="section-title">评价内容</view>
      <textarea 
        class="review-content" 
        placeholder="请输入您的评价内容，分享您的使用体验..." 
        bindinput="inputContent"
        value="{{content}}"
        maxlength="500"
      ></textarea>
      <view class="word-count">{{content.length}}/500</view>
    </view>
    
    <!-- 标签选择 -->
    <view class="tags-section">
      <view class="section-title">添加标签</view>
      <view class="tags-list">
        <view 
          wx:for="{{tags}}" 
          wx:key="id" 
          class="tag-item {{item.selected ? 'selected' : ''}}" 
          bindtap="toggleTag" 
          data-index="{{index}}"
        >
          {{item.name}}
        </view>
      </view>
    </view>
    
    <!-- 图片上传 -->
    <view class="images-section">
      <view class="section-title">上传图片</view>
      <view class="image-upload-list">
        <view class="image-list">
          <view 
            wx:for="{{images}}" 
            wx:key="index" 
            class="image-item"
          >
            <image src="{{item}}" mode="aspectFill"></image>
            <view class="delete-btn" bindtap="deleteImage" data-index="{{index}}">×</view>
          </view>
          
          <view class="upload-btn" bindtap="chooseImage" wx:if="{{images.length < 3}}">
            <view class="plus">+</view>
            <view class="text">添加图片</view>
          </view>
        </view>
        <view class="tip">最多上传3张图片</view>
      </view>
    </view>
    
    <!-- 匿名评价 -->
    <view class="anonymous-section">
      <view class="anonymous-toggle" bindtap="toggleAnonymous">
        <view class="checkbox {{anonymous ? 'checked' : ''}}"></view>
        <text>匿名评价</text>
      </view>
    </view>
    
    <!-- 提交按钮 -->
    <view class="submit-section">
      <button 
        class="submit-btn" 
        bindtap="submitReview" 
        loading="{{submitting}}" 
        disabled="{{submitting}}"
      >
        提交评价
      </button>
    </view>
  </view>
  
  <!-- 加载中 -->
  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>
</view>