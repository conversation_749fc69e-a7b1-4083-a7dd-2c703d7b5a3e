// views/orderList/review/index.js
import { connectPage } from "mobx-miniprogram-lite";
import { orderStore } from "../../../store/order/index";

connectPage({
  store: {
    orderStore,
  },
  data: {
    orderId: "",
    orderDetail: {},
    rating: 0,
    content: "",
    tags: [
      { id: 1, name: "服务好", selected: false },
      { id: 2, name: "环境好", selected: false },
      { id: 3, name: "书籍新", selected: false },
      { id: 4, name: "位置便利", selected: false },
      { id: 5, name: "价格合理", selected: false },
      { id: 6, name: "体验佳", selected: false },
    ],
    images: [],
    anonymous: false,
    submitting: false,
    loading: true,
  },

  onLoad(options) {
    const { orderId } = options;
    this.setData({ orderId });

    if (orderId) {
      this.getOrderDetail(orderId);
    }
  },

  async getOrderDetail(orderId) {
    try {
      const detail = await orderStore.getOrderDetail(orderId);
      if (detail) {
        this.setData({
          orderDetail: detail,
          loading: false,
        });
      }
    } catch (error) {
      console.error("获取订单详情失败:", error);
      this.setData({ loading: false });
      wx.showToast({
        title: "获取订单详情失败",
        icon: "none",
      });
    }
  },

  // 设置评分
  setRating(e) {
    const rating = e.currentTarget.dataset.rating;
    this.setData({ rating });
  },

  // 输入评价内容
  inputContent(e) {
    this.setData({ content: e.detail.value });
  },

  // 选择标签
  toggleTag(e) {
    const index = e.currentTarget.dataset.index;
    const tags = this.data.tags;
    tags[index].selected = !tags[index].selected;
    this.setData({ tags });
  },

  // 上传图片
  chooseImage() {
    const { images } = this.data;
    if (images.length >= 3) {
      wx.showToast({
        title: "最多上传3张图片",
        icon: "none",
      });
      return;
    }

    wx.chooseImage({
      count: 3 - images.length,
      sizeType: ["compressed"],
      sourceType: ["album", "camera"],
      success: (res) => {
        this.setData({
          images: [...images, ...res.tempFilePaths],
        });
      },
    });
  },

  // 删除图片
  deleteImage(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.images;
    images.splice(index, 1);
    this.setData({ images });
  },

  // 切换匿名评价
  toggleAnonymous() {
    this.setData({ anonymous: !this.data.anonymous });
  },

  // 提交评价
  async submitReview() {
    const { orderId, rating, content, tags, images, anonymous } = this.data;

    // 验证评分
    if (rating === 0) {
      wx.showToast({
        title: "请选择评分",
        icon: "none",
      });
      return;
    }

    // 验证评价内容
    if (!content.trim()) {
      wx.showToast({
        title: "请输入评价内容",
        icon: "none",
      });
      return;
    }

    this.setData({ submitting: true });

    try {
      // 获取选中的标签
      const selectedTags = tags
        .filter((tag) => tag.selected)
        .map((tag) => tag.name);

      // 构建评价数据
      const reviewData = {
        orderId,
        rating,
        content,
        tags: selectedTags,
        anonymous,
        images: [], // 上传后的图片URL会存储在这里
      };

      // 上传图片（如果有）
      if (images.length > 0) {
        const uploadPromises = images.map((tempFilePath) => {
          return new Promise((resolve, reject) => {
            wx.uploadFile({
              url: "https://api.example.com/upload", // 替换为实际的上传接口
              filePath: tempFilePath,
              name: "file",
              success: (res) => {
                const data = JSON.parse(res.data);
                resolve(data.url);
              },
              fail: (err) => reject(err),
            });
          });
        });

        const imageUrls = await Promise.all(uploadPromises);
        reviewData.images = imageUrls;
      }

      // 提交评价
      const result = await orderStore.submitReview(reviewData);

      if (result) {
        wx.showToast({
          title: "评价提交成功",
          icon: "success",
        });

        // 获取页面实例，用于触发事件
        const pages = getCurrentPages();
        const prevPage = pages[pages.length - 2]; // 上一个页面

        // 如果上一页是订单详情页，触发评价完成事件
        if (prevPage && prevPage.route.includes("orderDetail")) {
          const eventChannel = this.getOpenerEventChannel();
          eventChannel.emit("reviewCompleted", { success: true });
        }

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error("提交评价失败:", error);
      wx.showToast({
        title: "提交评价失败，请重试",
        icon: "none",
      });
    } finally {
      this.setData({ submitting: false });
    }
  },
});
