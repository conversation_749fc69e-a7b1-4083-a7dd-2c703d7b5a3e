/* views/orderList/review/index.scss */
.container {
  padding-bottom: 40rpx;
}

.review-container {
  padding: 30rpx;
}

/* 订单信息 */
.order-info {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.book-info {
  display: flex;
  align-items: center;
}

.book-cover {
  width: 120rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}

.book-detail {
  flex: 1;
}

.book-name {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
  color: #333;
}

.order-time, .order-price {
  font-size: 26rpx;
  color: #666;
  margin-top: 8rpx;
}

/* 评分部分 */
.rating-section, .content-section, .tags-section, .images-section, .anonymous-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
  color: #333;
}

.rating-stars {
  display: flex;
  justify-content: center;
  margin: 20rpx 0;
}

.star {
  font-size: 60rpx;
  color: #ddd;
  margin: 0 10rpx;
  transition: color 0.3s;
}

.star.active {
  color: #ffb400;
}

/* 评价内容 */
.review-content {
  width: 100%;
  height: 240rpx;
  padding: 20rpx;
  box-sizing: border-box;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  font-size: 28rpx;
  line-height: 1.6;
}

.word-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 标签选择 */
.tags-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.tag-item {
  padding: 12rpx 24rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #666;
  margin: 10rpx;
  transition: all 0.3s;
}

.tag-item.selected {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1rpx solid #91d5ff;
}

/* 图片上传 */
.image-list {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10rpx;
}

.image-item {
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
  border-radius: 8rpx;
  overflow: hidden;
}

.image-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom-left-radius: 8rpx;
}

.upload-btn {
  width: 160rpx;
  height: 160rpx;
  border: 1rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.plus {
  font-size: 48rpx;
  line-height: 1;
  margin-bottom: 10rpx;
}

.text {
  font-size: 24rpx;
}

.tip {
  font-size: 24rpx;
  color: #999;
}

/* 匿名评价 */
.anonymous-toggle {
  display: flex;
  align-items: center;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 1rpx solid #ddd;
  border-radius: 6rpx;
  margin-right: 16rpx;
  position: relative;
}

.checkbox.checked {
  background-color: #1890ff;
  border-color: #1890ff;
}

.checkbox.checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20rpx;
  height: 10rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: translate(-50%, -60%) rotate(-45deg);
}

/* 提交按钮 */
.submit-section {
  margin-top: 40rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background-color: #1890ff;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
}

.submit-btn[disabled] {
  background-color: #ccc;
  color: #fff;
}

/* 加载中 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
  color: #999;
  font-size: 28rpx;
}