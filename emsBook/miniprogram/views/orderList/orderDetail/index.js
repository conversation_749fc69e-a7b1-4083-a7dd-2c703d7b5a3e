// views/orderList/orderDetail/index.js
import { connectPage } from "mobx-miniprogram-lite";
import { orderStore } from "../../../store/order/index";
import { paymentStore } from "../../../store/payment/index";
import { navTo } from "../../../utils/common/tools";
import { ORDER_STATUS } from "../../../utils/orderService";

connectPage({
  store: {
    orderStore,
    paymentStore,
  },
  data: {
    orderId: "",
    orderDetail: {},
    loading: true,
  },

  onLoad(options) {
    const { id } = options;
    if (id) {
      this.setData({ orderId: id });
      this.getOrderDetail(id);
    } else {
      wx.showToast({
        title: "订单ID不存在",
        icon: "none",
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  async getOrderDetail(id) {
    try {
      const detail = await orderStore.getOrderDetail(id);
      if (detail) {
        this.setData({
          orderDetail: detail,
          loading: false,
        });
      }
    } catch (error) {
      console.error("获取订单详情失败:", error);
      this.setData({ loading: false });
      wx.showToast({
        title: "获取订单详情失败",
        icon: "none",
      });
    }
  },

  // 查看位置
  viewLocation() {
    const { orderDetail } = this.data;
    if (orderDetail && orderDetail.latitude && orderDetail.longitude) {
      wx.navigateTo({
        url: `/pages/map/detail?id=${orderDetail.id}&name=${orderDetail.bookName}&address=${orderDetail.address}`,
      });
    } else {
      wx.showToast({
        title: "位置信息不存在",
        icon: "none",
      });
    }
  },

  // 查看预约码
  viewQrcode() {
    const { orderId } = this.data;
    navTo(
      `/components/ems-qrcode/index?title=预约码&text=${orderId}`,
      "wx://bottom-sheet"
    );
  },

  // 取消订单
  async cancelOrder() {
    const { orderId } = this.data;
    wx.showModal({
      title: "提示",
      content: "确定要取消该订单吗？",
      success: async (res) => {
        if (res.confirm) {
          try {
            const result = await orderStore.cancelOrder(orderId);
            if (result) {
              wx.showToast({
                title: "订单已取消",
                icon: "success",
              });
              // 刷新订单详情
              this.getOrderDetail(orderId);
            }
          } catch (error) {
            console.error("取消订单失败:", error);
            wx.showToast({
              title: "取消订单失败",
              icon: "none",
            });
          }
        }
      },
    });
  },

  // 去评价
  goToReview() {
    const { orderId } = this.data;
    wx.navigateTo({
      url: `/views/orderList/review/index?orderId=${orderId}`,
      events: {
        // 监听评价完成事件
        reviewCompleted: () => {
          // 刷新订单详情，更新评价状态
          this.getOrderDetail(orderId);
        },
      },
    });
  },

  // 分享订单
  shareOrder() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ["shareAppMessage", "shareTimeline"],
    });
  },

  // 返回订单列表
  goBack() {
    wx.navigateBack();
  },

  // 申请退款
  applyRefund() {
    const { orderId, orderDetail } = this.data;

    if (!orderDetail.paymentId) {
      wx.showToast({
        title: "支付信息不存在，无法申请退款",
        icon: "none",
      });
      return;
    }

    wx.showModal({
      title: "申请退款",
      content: "确定要申请退款吗？",
      success: (res) => {
        if (res.confirm) {
          // 跳转到退款申请页面
          wx.navigateTo({
            url: `/views/payment/refund/index?paymentId=${orderDetail.paymentId}&orderId=${orderId}`,
            events: {
              // 监听退款完成事件
              refundCompleted: () => {
                // 刷新订单详情
                this.getOrderDetail(orderId);
              },
            },
          });
        }
      },
    });
  },
});
