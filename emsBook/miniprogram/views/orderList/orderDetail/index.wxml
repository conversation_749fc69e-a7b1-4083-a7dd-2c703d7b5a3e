<!-- views/orderList/orderDetail/index.wxml -->
<view class="container">
  <ems-navbar slot='navbar' title="订单详情"></ems-navbar>
  
  <view class="order-detail" wx:if="{{!loading}}">
    <!-- 订单基本信息 -->
    <view class="card order-info">
      <view class="book-info">
        <image class="book-cover" src="{{orderDetail.coverUrl || '/static/images/logo.jpg'}}" mode="aspectFill"></image>
        <view class="book-detail">
          <view class="book-name">{{orderDetail.bookName || '未知书籍'}}</view>
          <view class="order-time">订单时间：{{orderDetail.orderTime || '未知'}}</view>
          <view class="order-price">订单金额：¥{{orderDetail.price || '0.00'}}</view>
        </view>
      </view>
      <view class="status-tag {{orderDetail.status}}">{{orderDetail.status === 'pending' ? '待支付' : 
        orderDetail.status === 'paid' ? '已支付' : 
        orderDetail.status === 'completed' ? '已完成' : 
        orderDetail.status === 'cancelled' ? '已取消' : 
        orderDetail.status === 'refunding' ? '退款中' : 
        orderDetail.status === 'refunded' ? '已退款' : '未知状态'}}</view>
    </view>
    
    <!-- 预约信息 -->
    <view class="card booking-info">
      <view class="section-title">预约信息</view>
      <view class="info-item">
        <text class="label">预约日期</text>
        <text class="value">{{orderDetail.orderDate || '未知'}}</text>
      </view>
      <view class="info-item">
        <text class="label">预约时段</text>
        <text class="value">{{orderDetail.timeSlot || '未知'}}</text>
      </view>
      <view class="info-item">
        <text class="label">归还日期</text>
        <text class="value">{{orderDetail.returnTime || '未知'}}</text>
      </view>
      <view class="info-item">
        <text class="label">预约地点</text>
        <text class="value">{{orderDetail.location || '未知'}}</text>
      </view>
      <view class="info-item" wx:if="{{orderDetail.remarks}}">
        <text class="label">备注</text>
        <text class="value">{{orderDetail.remarks}}</text>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <!-- 查看位置 -->
      <view class="action-btn" bindtap="viewLocation">
        <image src="/static/icons/location.png" mode="widthFix"></image>
        <text>查看位置</text>
      </view>
      
      <!-- 预约码 -->
      <view class="action-btn" bindtap="viewQrcode">
        <image src="/static/icons/qrcode.png" mode="widthFix"></image>
        <text>预约码</text>
      </view>
      
      <!-- 评价按钮 - 仅在订单已完成且未评价时显示 -->
      <view class="action-btn" bindtap="goToReview" 
            wx:if="{{orderDetail.status === 'completed' && !orderDetail.hasReview}}">
        <image src="/static/icons/star.png" mode="widthFix"></image>
        <text>评价</text>
      </view>
      
      <!-- 分享 -->
      <view class="action-btn" bindtap="shareOrder">
        <image src="/static/icons/share.png" mode="widthFix"></image>
        <text>分享</text>
      </view>
    </view>
    
    <!-- 底部按钮 -->
    <view class="bottom-buttons">
      <button class="btn secondary" bindtap="goBack">返回列表</button>
      
      <!-- 取消订单按钮 - 仅在待支付或已支付状态显示 -->
      <button class="btn primary" bindtap="cancelOrder" 
              wx:if="{{orderDetail.status === 'pending' || orderDetail.status === 'paid'}}">取消订单</button>
      
      <!-- 申请退款按钮 - 仅在已支付状态显示 -->
      <button class="btn primary" bindtap="applyRefund" 
              wx:if="{{orderDetail.status === 'paid' && orderDetail.paymentId}}">申请退款</button>
    </view>
  </view>
  
  <!-- 加载中 -->
  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>
</view>