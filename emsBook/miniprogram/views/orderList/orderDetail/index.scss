/* views/orderList/orderDetail/index.scss */
.container {
  padding-bottom: 40rpx;
}

.order-detail {
  padding: 30rpx;
}

/* 卡片样式 */
.card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 订单基本信息 */
.order-info {
  position: relative;
}

.book-info {
  display: flex;
  align-items: center;
}

.book-cover {
  width: 120rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}

.book-detail {
  flex: 1;
}

.book-name {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
  color: #333;
}

.order-time, .order-price {
  font-size: 26rpx;
  color: #666;
  margin-top: 8rpx;
}

.status-tag {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #fff;
}

.status-tag.pending {
  background-color: #faad14;
}

.status-tag.paid {
  background-color: #1890ff;
}

.status-tag.completed {
  background-color: #52c41a;
}

.status-tag.cancelled {
  background-color: #999;
}

.status-tag.refunding {
  background-color: #722ed1;
}

.status-tag.refunded {
  background-color: #13c2c2;
}

/* 预约信息 */
.section-title {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
  color: #333;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  line-height: 1.5;
}

.label {
  color: #666;
  flex: 0 0 160rpx;
}

.value {
  color: #333;
  flex: 1;
  text-align: right;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: space-around;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 20rpx;
}

.action-btn image {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.action-btn text {
  font-size: 24rpx;
  color: #666;
}

/* 底部按钮 */
.bottom-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 30rpx;
  margin: 0 15rpx;
}

.btn.primary {
  background-color: #1890ff;
  color: #fff;
}

.btn.secondary {
  background-color: #f5f5f5;
  color: #666;
  border: 1rpx solid #ddd;
}

/* 加载中 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
  color: #999;
  font-size: 28rpx;
}