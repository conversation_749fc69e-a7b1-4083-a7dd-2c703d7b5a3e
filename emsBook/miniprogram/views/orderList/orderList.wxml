<list-wrap isEmpty="{{false}}" noFooter>
  <ems-navbar slot='navbar' title="预约订单列表"></ems-navbar>
  <ems-tab></ems-tab>
  <list-view>
    <ems-list-item wx:for="{{viewList}}" item="{{item}}" index="{{index}}" style="margin-top: 20rpx;" bindtap="viewOrderDetail" data-orderid="{{item.id}}">
      <view slot='rightContent' class="action-buttons">
        <view class="qrcode" catch:tap="qrcode" data-orderid="{{item.id}}">
          <image src="/static/icons/qrcode.png" mode="widthFix" style="width: 60rpx;"></image>
          <text>预约码</text>
        </view>
        <view class="review-btn" catch:tap="goToReview" data-orderid="{{item.id}}" wx:if="{{item.status === 'completed' && !item.hasReview}}">
          <image src="/static/icons/star.png" mode="widthFix" style="width: 60rpx;"></image>
          <text>评价</text>
        </view>
        <view class="share-btn" catch:tap="shareOrder" data-orderid="{{item.id}}">
          <image src="/static/icons/share.png" mode="widthFix" style="width: 60rpx;"></image>
          <text>分享</text>
        </view>
      </view>
    </ems-list-item>
  </list-view>
</list-wrap>