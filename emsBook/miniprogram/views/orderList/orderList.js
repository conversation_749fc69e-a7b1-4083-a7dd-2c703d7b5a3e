import { getInvoiceList } from "../../utils/api";
import { navTo } from "../../utils/common/tools";
import { connectPage } from "mobx-miniprogram-lite";
import { rootState } from "../../store/index";

// views/orderList/orderList.js
connectPage({
  store: {
    ...rootState.invoice,
  },
  data: {
    viewList: [
      {
        id: 1,
        avatar:
          "http://res.yqbaijiu.com/20230802/ccb62eeee63c4e43ba16312b53f8092b.jpg",
        doctor: "大脸",
        orderTime: "2024-1-23",
        address: "放大法法师",
      },
      {
        id: 2,
        avatar:
          "http://res.yqbaijiu.com/20230802/ccb62eeee63c4e43ba16312b53f8092b.jpg",
        doctor: "大脸",
        orderTime: "2024-1-23",
        address: "放大法法师",
      },
      {
        id: 3,
        avatar:
          "http://res.yqbaijiu.com/20230802/ccb62eeee63c4e43ba16312b53f8092b.jpg",
        doctor: "大脸",
        orderTime: "2024-1-23",
        address: "放大法法师",
      },
      {
        id: 4,
        avatar:
          "http://res.yqbaijiu.com/20230802/ccb62eeee63c4e43ba16312b53f8092b.jpg",
        doctor: "大脸",
        orderTime: "2024-1-23",
        address: "放大法法师",
      },
    ],
  },

  qrcode(e) {
    const { orderid } = e.currentTarget.dataset;
    navTo(
      `/components/ems-qrcode/index?title=预约码&text=${orderid}`,
      "wx://bottom-sheet"
    );
  },

  // 跳转到评价页面
  goToReview(e) {
    const { orderid } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/views/orderList/review/index?orderId=${orderid}`,
    });
  },

  // 分享订单
  shareOrder(e) {
    const { orderid } = e.currentTarget.dataset;
    wx.showShareMenu({
      withShareTicket: true,
      menus: ["shareAppMessage", "shareTimeline"],
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.getOrderList();
  },

  // 获取订单列表
  async getOrderList() {
    const invoiceStore = this.store.listState
    try {
      await invoiceStore.getInvoiceList();
      // 处理订单列表，确保每个订单都有正确的状态和评价标志
      const processedList = invoiceStore.invoiceList.map((order) => {
        // 确保订单有status字段，如果没有则默认为pending
        if (!order.status) {
          order.status = "pending";
        }
        // 确保订单有hasReview字段，如果没有则默认为false
        if (order.hasReview === undefined) {
          order.hasReview = false;
        }
        return {
          id: order.bookId,
          avatar: order.coverUrl,
          name: order.bookName,
          orderTime: order.orderTime,
          description: order.description ?? ''

        };
      });

      this.setData({
        viewList: processedList,
      });
    } catch (error) {
      console.error("获取订单列表失败:", error);
    }
  },

  // 查看订单详情
  viewOrderDetail(e) {
    const { orderid } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/views/orderList/orderDetail/index?id=${orderid}`,
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    getInvoiceList();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() { },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() { },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() { },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() { },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() { },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() { },
});
