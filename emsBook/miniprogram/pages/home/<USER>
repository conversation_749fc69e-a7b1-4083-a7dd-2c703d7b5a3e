import { rootState } from "../../store/index";
import { connectPage } from "mobx-miniprogram-lite";
const app = getApp()
const { menuRect, themeData } = app.globalData
connectPage({
  store: {
    appConfig: rootState.app,
  },
  data: {
    themeData,
    indicatorOffset: [20, menuRect.bottom - menuRect.height],
    sections: 4,
    bgItems: [{
      bg: 'http://res.yqbaijiu.com/20230802/ccb62eeee63c4e43ba16312b53f8092b.jpg',
      title: '手工精制'

    }, {
      bg: 'http://res.yqbaijiu.com/20230802/eb7fa65eb1ee4b60bcc991306effe154.jpg',
      title: '匠心独运'
    }, {
      bg: 'http://res.yqbaijiu.com/20230705/5b8feb65f6f146bf89ae2ae24a47c538.jpg',
      title: '经久不衰'
    }, {
      bg: 'http://res.yqbaijiu.com/20230713/41f21044ef0a4d35838abde3e719ab26.jpg',
      title: '传承经典'
    }]
  },

  onChange(e) {
    "worklet"
    // this.current.value = e.detail.current
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {



  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() { },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() { },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() { },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() { },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() { },
});
