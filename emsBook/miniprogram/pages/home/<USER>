<swiper circular class="swiper" layout-type="transformer" transformer-type="threeD" indicator-dots indicator-type="expand" indicator-alignment="{{[-1,-1]}}" indicator-offset='{{indicatorOffset}}' indicator-color="rgba(255,255,255,0.5)" indicator-active-color="{{themeData.selectedColor}}" bindchange='onChange'>
  <swiper-item class="swiper-item" wx:for="{{sections}}" wx:for-index="swiperIndex">
    <!-- <ems-parallax-scroll style="height: 100vh;" items="{{bgItems}}"></ems-parallax-scroll> -->
    <ems-parallax-list style="height: 100vh;" items="{{bgItems}}"></ems-parallax-list>
  </swiper-item>
</swiper>