// pages/social/index.js
import { connectPage } from "mobx-miniprogram-lite";
import { socialStore } from "../../store/social/index";
import { userInfoStore } from "../../store/user/index";
import { getShareParams, recordShare } from "../../utils/share";

connectPage({
  store: {
    socialStore,
    userInfoStore,
  },
  data: {
    title: "社交分享",
    activeTab: 0,
    tabs: [
      { title: "我的点赞", key: "likes" },
      { title: "我的收藏", key: "favorites" },
      { title: "我的评论", key: "comments" },
      { title: "我的分享", key: "shares" },
    ],
    shareItems: [],
    isLoading: false,
    isEmpty: false,
  },

  onLoad() {
    this.loadTabData(0);
  },

  onShow() {
    // 每次页面显示时刷新数据
    this.loadTabData(this.data.activeTab);
  },

  // 切换标签页
  handleTabChange(e) {
    const index = e.detail.index;
    this.setData({ activeTab: index });
    this.loadTabData(index);
  },

  // 加载标签页数据
  loadTabData(index) {
    const tabKey = this.data.tabs[index].key;
    this.setData({ isLoading: true });

    let promise;
    switch (tabKey) {
      case "likes":
        promise = socialStore.getLikesList();
        break;
      case "favorites":
        promise = socialStore.getFavoritesList();
        break;
      case "comments":
        promise = socialStore.getCommentsList({
          page: 1,
          pageSize: 20,
        });
        break;
      case "shares":
        promise = socialStore.getSharesList({
          page: 1,
          pageSize: 20,
        });
        break;
      default:
        promise = Promise.resolve();
    }

    promise.then(() => {
      let items = [];
      switch (tabKey) {
        case "likes":
          items = socialStore.likes;
          break;
        case "favorites":
          items = socialStore.favorites;
          break;
        case "comments":
          items = socialStore.comments;
          break;
        case "shares":
          items = socialStore.shares;
          break;
      }

      this.setData({
        shareItems: items,
        isLoading: false,
        isEmpty: items.length === 0,
      });
    });
  },

  // 点击项目
  handleItemClick(e) {
    const { id, type } = e.currentTarget.dataset;

    // 根据类型跳转到不同页面
    if (type === "book") {
      wx.navigateTo({
        url: `/views/goods/detail?id=${id}`,
      });
    } else if (type === "order") {
      wx.navigateTo({
        url: `/views/orderList/detail?id=${id}`,
      });
    }
  },

  // 分享功能
  onShareAppMessage(res) {
    // 如果是从按钮触发的分享
    if (res.from === "button") {
      const { id, type } = res.target.dataset;

      // 记录分享行为
      recordShare({
        itemId: id,
        itemType: type,
        shareType: "wechat",
      });

      // 返回分享信息
      return getShareParams({
        type,
        id,
      });
    }

    // 默认分享信息
    return {
      title: "emsBook - 让阅读更便捷",
      path: "/pages/home/<USER>",
      imageUrl: "/static/images/share/default_share.png",
    };
  },
});
