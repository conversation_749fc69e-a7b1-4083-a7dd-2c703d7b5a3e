<!-- pages/social/index.wxml -->
<view class="container">
    <view class="header">
        <text class="title">{{title}}</text>
    </view>
    <!-- 标签页导航 -->
    <ems-tab tabs="{{tabs}}" activeTab="{{activeTab}}" bindchange="handleTabChange"></ems-tab>
    <!-- 内容区域 -->
    <view class="content">
        <!-- 加载中 -->
        <view class="loading" wx:if="{{isLoading}}">
            <view class="loading-icon"></view>
            <text>加载中...</text>
        </view>
        <!-- 空状态 -->
        <view class="empty" wx:elif="{{isEmpty}}">
            <ems-empty text="暂无数据"></ems-empty>
        </view>
        <!-- 列表内容 -->
        <view class="list" wx:else>
            <view class="item" wx:for="{{shareItems}}" wx:key="id" bindtap="handleItemClick" data-id="{{item.itemId}}" data-type="{{item.itemType}}">
                <view class="item-content">
                    <!-- 图书类型 -->
                    <block wx:if="{{item.itemType === 'book'}}">
                        <image class="item-image" src="{{item.coverUrl || '/static/images/share/book_share.png'}}"></image>
                        <view class="item-info">
                            <text class="item-title">{{item.title || '图书'}}</text>
                            <text class="item-desc">{{item.description || '暂无描述'}}</text>
                            <text class="item-time">{{item.createTime}}</text>
                        </view>
                    </block>
                    <!-- 订单类型 -->
                    <block wx:elif="{{item.itemType === 'order'}}">
                        <image class="item-image" src="{{item.coverUrl || '/static/images/share/order_share.png'}}"></image>
                        <view class="item-info">
                            <text class="item-title">订单: {{item.orderNo || item.id}}</text>
                            <text class="item-desc">{{item.description || '暂无描述'}}</text>
                            <text class="item-time">{{item.createTime}}</text>
                        </view>
                    </block>
                    <!-- 评论类型 -->
                    <block wx:elif="{{activeTab === 2}}">
                        <view class="comment-content">
                            <text class="comment-text">{{item.content}}</text>
                            <text class="item-time">{{item.createTime}}</text>
                        </view>
                    </block>
                    <!-- 分享类型 -->
                    <block wx:elif="{{activeTab === 3}}">
                        <image class="item-image" src="{{item.coverUrl || '/static/images/share/' + item.itemType + '_share.png'}}"></image>
                        <view class="item-info">
                            <text class="item-title">
                                {{item.title || (item.itemType === 'book' ? '图书' : '订单')}}
                            </text>
                            <text class="item-desc">
                                分享方式: {{item.shareType === 'wechat' ? '微信好友' : '分享海报'}}
                            </text>
                            <text class="item-time">{{item.createTime}}</text>
                        </view>
                    </block>
                    <!-- 默认类型 -->
                    <block wx:else>
                        <image class="item-image" src="{{item.coverUrl || '/static/images/share/default_share.png'}}"></image>
                        <view class="item-info">
                            <text class="item-title">{{item.title || '内容'}}</text>
                            <text class="item-desc">{{item.description || '暂无描述'}}</text>
                            <text class="item-time">{{item.createTime}}</text>
                        </view>
                    </block>
                </view>
                <!-- 分享按钮 -->
                <view class="item-actions">
                    <button class="share-btn" open-type="share" data-id="{{item.itemId}}" data-type="{{item.itemType}}">
                        <image src="/static/icons/share.png"></image>
                        <text>分享</text>
                    </button>
                </view>
            </view>
        </view>
    </view>
</view>