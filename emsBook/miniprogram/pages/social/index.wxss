/* pages/social/index.wxss */

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
}

.header {
  padding: 20rpx 30rpx;
  background-color: #ffffff;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.content {
  flex: 1;
  overflow: hidden;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 30rpx;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 30rpx;
}

.list {
  height: 100%;
  overflow-y: auto;
}

.item {
  display: flex;
  flex-direction: column;
  margin: 20rpx;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.item-content {
  display: flex;
  flex-direction: row;
}

.item-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  background-color: #f0f0f0;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.item-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.item-desc {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-time {
  font-size: 24rpx;
  color: #999999;
}

.comment-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 10rpx 0;
}

.comment-text {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
  line-height: 1.5;
}

.item-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.share-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20rpx;
  height: 60rpx;
  background-color: #07c160;
  color: #ffffff;
  font-size: 26rpx;
  border-radius: 30rpx;
}

.share-btn image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.share-btn text {
  color: #ffffff;
}