import { connectPage } from "mobx-miniprogram-lite";
import { rootState } from "../../store/index";

connectPage({
  store: {
    ...rootState.shop,
  },
  data: {
    markers: [],
  },

  onLoad(options) { },
  onShow() {
    // 获取活动列表数据
    // this.store.listState.fetchCampaignList().then(() => {
    //   // 活动列表获取成功后，生成地图标记点
    //   this.generateMapMarkers();
    // });
  },

  // 生成地图标记点
  generateMapMarkers() {
    const shops = this.store.listState.shops || [];
    if (campaigns.length === 0) return;

    const markers = shops.map((item, index) => {
      return {
        id: item.id,
        latitude: item.latitude || 39.9761,
        longitude: item.longitude || 116.3282,
        title: item.name,
        iconPath: "/static/images/location.png",
        width: 30,
        height: 30,
        callout: {
          content: item.name,
          color: "#000000",
          fontSize: 12,
          borderRadius: 5,
          padding: 5,
          display: "BYCLICK",
        },
      };
    });

    this.setData({ markers });
  },

  // 点击地图标记点
  markertap(e) {
    const markerId = e.markerId;
    const campaign = this.store.listState.campaigns.find(
      (item) => item.id === markerId
    );

    if (campaign) {
      wx.navigateTo({
        url: `/pages/map/detail?id=${campaign.id}&name=${campaign.name
          }&address=${campaign.address || ""}&latitude=${campaign.latitude || 39.9761
          }&longitude=${campaign.longitude || 116.3282}`,
      });
    }
  },
});
