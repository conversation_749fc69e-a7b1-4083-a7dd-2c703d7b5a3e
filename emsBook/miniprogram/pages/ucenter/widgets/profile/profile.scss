.card-shadow {
  box-shadow: 0rpx 2rpx 20rpx -6rpx rgba(193, 211, 228, 1);
}

.vipCard {
  height: 560rpx;
  display: flex;
  flex-direction: column;
  background-color: transparent;
  width: 100%;
  box-sizing: border-box;
  align-items: center;
  overflow: hidden;
  position: relative;
  .img {
    position: absolute;
    top: 0;
    width: calc(100% - 64rpx);
    height: 380rpx;
    border-radius: 32rpx;
    overflow: hidden;
    z-index: 2;
  }
  .shadow {
    z-index: 1;
    border-radius: 32rpx;
    width: calc(100% - 32rpx);
    height: 32rpx;
    background-color: lightgray;
    position: absolute;
    bottom: calc(300rpx - 16rpx);
  }
  .profile {
    z-index: 3;
    height: 300rpx;
    position: absolute;
    bottom: 0;
    width: 100%;
    border-radius: 16rpx;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    .userInfo {
      width: 100%;
      display: flex;
      flex-direction: row;
      padding: 24rpx;
      z-index: 2;
      .avatar {
        width: 88rpx;
        height: 88rpx;
        border-radius: 50%;
        overflow: hidden;
        .img {
          width: 100%;
          /* border-radius: 50%; */
        }
      }

      .info {
        flex: 1;
        margin-left: 24rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        font-size: 32rpx;
        .info_name {
          font-size: 32rpx;
          font-weight: 500;
        }
        .info_level {
          font-size: 26rpx;
          color: darkgray;
        }
      }

      .qrcode {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text {
          font-size: 22rpx;
          color: dimgray;
        }
      }
    }
    .wallet {
    }
  }
}
