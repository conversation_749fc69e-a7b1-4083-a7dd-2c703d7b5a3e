// pages/ucenter/widgets/profile/profile.js
import { connectComponent } from "mobx-miniprogram-lite";
import { rootState } from "../../../../store/index";


import { copyText, navTo } from "../../../../utils/common/tools";
const themeBehavior = require("../../../../behavior/theme");
const computedBehavior = require("miniprogram-computed").behavior;

connectComponent({
  behaviors: [computedBehavior, themeBehavior],
  store: {
    app: rootState.app,
    ...rootState.user,
    auth: rootState.auth
  },
  properties: {},

  /**
   * 组件的初始数据
   */
  data: {
    iconEdit: "\u{e605}",
  },
  lifetimes: {
    ready() { },
  },
  /**
   * 组件的方法列表
   */
  methods: {
    editProfile() {
      navTo("/components/ems-profileEdit/index?showAvatar=true");
    },
    copy() {
      copyText(this.data.vipNo, "会员号");
    },
    qrcode() {
      const { qrUrl } = this.data.profile;
      navTo(
        `/components/ems-qrcode/index?title=个人码&url=${qrUrl}`,
        "wx://bottom-sheet"
      );
    },
  },
});
