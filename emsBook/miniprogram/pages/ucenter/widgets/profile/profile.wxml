<view class="vipCard">
  <image class="img" src="{{app.ucenterImg || '/static/images/logo.jpg'}}" mode="aspectFill">
  </image>
  <view class="shadow card-shadow">
  </view>
  <view class="profile  card-shadow">
    <view class="userInfo">
      <view class="avatar" catch:tap="editProfile">
        <image class="img" src="{{profile.avatarUrl||'/static/images/logo.jpg'}}" mode="widthFix"></image>
      </view>
      <view class="info" wx:if="{{auth.isAuthenticated}}">
        <span class="info_name" catch:tap="editProfile">
          <text overflow="ellipsis" max-lines="1">{{profile.displayName}}</text>
          <text style="color: {{themeData.primaryColor}};"> {{iconEdit}}</text>
        </span>
        <text class="info_level" catch:tap="copy">{{profile.formattedVipNo}}</text>
      </view>
      <view class="info" catch:tap="navTo" wx:else="">
        <text class="info_name">去验证</text>
        <text class="info_level">获取更多权益</text>
      </view>
      <view class="qrcode" catch:tap="qrcode">
        <image src="../../../../static/icons/qrcode.png" mode="widthFix" style="width: 60rpx;">
        </image>
        <text>个人码</text>
      </view>
    </view>
    <view class="wallet">
      <wallet hasTitle="{{false}}"></wallet>
    </view>
  </view>

</view>