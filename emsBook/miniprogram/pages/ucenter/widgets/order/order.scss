.container {
  width: 100%;
  display: flex;
  flex-direction: column;
  .header {
    display: flex;
    padding: 20rpx;
    justify-content: space-between;
    align-items: center;
  }
  .content {
    background-color: #ffffff;
    padding: 20rpx;
    border-radius: 20rpx;
    display: flex;
    flex-direction: column;
    .detail {
      height: 160rpx;
      background-color: #eeeeee;
      border-radius: 16rpx;
      margin-bottom: 32rpx;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .footer button {
      background-color: #e35048;
      color: #ffffff;
      border-radius: 40rpx;
    }
  }
}
