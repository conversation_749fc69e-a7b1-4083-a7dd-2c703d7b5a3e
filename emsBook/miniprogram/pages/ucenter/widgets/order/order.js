import { navTo } from "../../../../utils/common/tools";

// pages/ucenter/widgets/order/order.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {},

  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的方法列表
   */

  methods: {
    getInstance(e) {
      if (typeof this.getTabBar === "function") {
        this.getTabBar((tabBar) => {
          tabBar.switchTab(e);
        });
      }
    },
    goTo() {
      navTo("/views/orderList/orderList");
    },
    preOrder(e) {
      // navTo();
      this.getInstance(e);
    },
  },
});
