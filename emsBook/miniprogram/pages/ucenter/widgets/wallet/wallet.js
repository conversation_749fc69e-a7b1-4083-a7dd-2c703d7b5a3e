// pages/ucenter/widgets/wallet/wallet.js
import { connectComponent } from "mobx-miniprogram-lite";
import { rootState } from "../../../../store/index";
import { navTo } from "../../../../utils/common/tools";

connectComponent({
  /**
   * 组件的属性列表
   */
  store: {
    ...rootState.user
  },
  properties: {
    hasTitle: {
      type: Boolean,
      value: true,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    items: [
      {
        text: "优惠券",
        icon: "card",
        // url: "/pages/ucenter/widget/wallet/coupon",
        value: 0,
      },
      {
        text: "积分",
        icon: "card",
        // url: "/pages/ucenter/widget/wallet/credit",
        value: 0,
      },
      {
        text: "现金余额",
        icon: "card",
        // url: '/pages/ucenter/widget/wallet/cash',
        value: 0,
      },
      {
        text: "会员卡",
        icon: "card",
        // url: '/pages/ucenter/widget/wallet/vip',
        value: 0,
      },
      // {
      // 	text: '消费记录',
      // 	icon: 'card',
      // 	url: '/pages/ucenter/widget/wallet/order',
      // 	value: item.value.orderCnt
      // },
    ],
  },

  /**
   * 组件的方法列表
   */
  lifetimes: {
    ready() {
      this.store.profile.fetchUserWallet().then(res => {
        this.setData({
          "items[0].value": res.total_value,
          "items[1].value": res.total_rewards,
          "items[2].value": res.issued_count,
          "items[3].value": res.pending_count,
        });
      })

    },
  },
  methods: {
    goTo(e) {
      const { url } = e.currentTarget.dataset;
      navTo(url);
    },
  },
});
