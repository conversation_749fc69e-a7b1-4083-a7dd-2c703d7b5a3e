/**
 * 状态持久化工具
 * 提供统一的状态持久化和加载机制
 */

/**
 * 持久化状态到本地存储
 * @param {string} key - 存储键名
 * @param {object} state - 要持久化的状态对象
 */
export const persistState = (key, state) => {
  if (!key || typeof key !== "string") {
    console.error("persistState: key must be a non-empty string");
    return;
  }

  try {
    wx.setStorage({
      key,
      data: JSON.stringify(state),
    });
  } catch (error) {
    console.error(`Failed to persist state for ${key}:`, error);
  }
};

/**
 * 从本地存储加载持久化的状态
 * @param {string} key - 存储键名
 * @param {object} defaultState - 默认状态，当没有找到持久化状态时返回
 * @returns {object} 加载的状态或默认状态
 */
export const loadPersistedState = (key, defaultState = {}) => {
  if (!key || typeof key !== "string") {
    console.error("loadPersistedState: key must be a non-empty string");
    return defaultState;
  }

  try {
    const data = wx.getStorageSync(key);
    return data ? JSON.parse(data) : defaultState;
  } catch (error) {
    console.error(`Failed to load persisted state for ${key}:`, error);
    return defaultState;
  }
};

/**
 * 清除指定键的持久化状态
 * @param {string} key - 存储键名
 */
export const clearPersistedState = (key) => {
  if (!key || typeof key !== "string") {
    console.error("clearPersistedState: key must be a non-empty string");
    return;
  }

  try {
    wx.removeStorage({
      key,
      success: () => console.log(`Cleared persisted state for ${key}`),
      fail: (error) =>
        console.error(`Failed to clear persisted state for ${key}:`, error),
    });
  } catch (error) {
    console.error(`Failed to clear persisted state for ${key}:`, error);
  }
};
