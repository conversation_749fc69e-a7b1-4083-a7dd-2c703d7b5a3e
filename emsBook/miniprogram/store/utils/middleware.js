/**
 * 状态中间件
 * 提供状态变更的拦截和处理机制
 */

/**
 * 创建日志中间件
 * 记录状态变更的日志
 *
 * @param {object} options - 配置选项
 * @param {boolean} options.logChanges - 是否记录变更
 * @param {boolean} options.logActions - 是否记录动作
 * @returns {Function} 中间件函数
 */
export const createLogMiddleware = (options = {}) => {
  const { logChanges = true, logActions = true } = options;

  return (store, next) => (action, args) => {
    if (logActions) {
      console.group(`Action: ${action.name || "Anonymous"}`);
      console.log("Arguments:", args);
    }

    const prevState = logChanges ? JSON.parse(JSON.stringify(store)) : null;
    const result = next(action, args);

    if (logChanges) {
      const nextState = JSON.parse(JSON.stringify(store));
      console.log("State change:", {
        prev: prevState,
        next: nextState,
      });
    }

    if (logActions) {
      console.log("Result:", result);
      console.groupEnd();
    }

    return result;
  };
};

/**
 * 创建性能监控中间件
 * 监控状态变更的性能
 *
 * @param {object} options - 配置选项
 * @param {number} options.threshold - 性能警告阈值（毫秒）
 * @returns {Function} 中间件函数
 */
export const createPerformanceMiddleware = (options = {}) => {
  const { threshold = 16 } = options; // 默认16ms（约60fps）

  return (store, next) => (action, args) => {
    const actionName = action.name || "Anonymous";
    const startTime = performance.now();

    const result = next(action, args);

    const endTime = performance.now();
    const duration = endTime - startTime;

    if (duration > threshold) {
      console.warn(
        `Performance warning: Action '${actionName}' took ${duration.toFixed(
          2
        )}ms to complete, which exceeds the threshold of ${threshold}ms.`
      );
    }

    return result;
  };
};

/**
 * 创建错误处理中间件
 * 捕获并处理状态变更过程中的错误
 *
 * @param {object} options - 配置选项
 * @param {Function} options.errorHandler - 错误处理函数
 * @returns {Function} 中间件函数
 */
export const createErrorMiddleware = (options = {}) => {
  const { errorHandler = console.error } = options;

  return (store, next) => (action, args) => {
    try {
      return next(action, args);
    } catch (error) {
      errorHandler(error, { action, args, store });
      throw error; // 重新抛出错误，让调用者知道发生了错误
    }
  };
};
