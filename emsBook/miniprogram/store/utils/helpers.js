/**
 * 状态管理辅助函数
 * 提供状态管理相关的工具函数
 */

/**
 * 防抖函数
 * 用于减少频繁触发的函数调用，只在最后一次触发后执行
 *
 * @param {Function} func - 要执行的函数
 * @param {number} wait - 等待时间（毫秒）
 * @param {boolean} immediate - 是否在触发后立即执行
 * @returns {Function} 防抖处理后的函数
 */
export const debounce = (func, wait = 300, immediate = false) => {
  let timeout;

  return function (...args) {
    const context = this;

    const later = function () {
      timeout = null;
      if (!immediate) func.apply(context, args);
    };

    const callNow = immediate && !timeout;

    clearTimeout(timeout);
    timeout = setTimeout(later, wait);

    if (callNow) func.apply(context, args);
  };
};

/**
 * 节流函数
 * 用于限制函数的执行频率，每隔一段时间执行一次
 *
 * @param {Function} func - 要执行的函数
 * @param {number} limit - 时间间隔（毫秒）
 * @returns {Function} 节流处理后的函数
 */
export const throttle = (func, limit = 300) => {
  let inThrottle;

  return function (...args) {
    const context = this;

    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

/**
 * 深度比较两个对象是否相等
 *
 * @param {*} obj1 - 第一个对象
 * @param {*} obj2 - 第二个对象
 * @returns {boolean} 是否相等
 */
export const deepEqual = (obj1, obj2) => {
  if (obj1 === obj2) return true;

  if (
    typeof obj1 !== "object" ||
    obj1 === null ||
    typeof obj2 !== "object" ||
    obj2 === null
  ) {
    return false;
  }

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) return false;

  for (const key of keys1) {
    if (!keys2.includes(key)) return false;
    if (!deepEqual(obj1[key], obj2[key])) return false;
  }

  return true;
};

/**
 * 创建一个对象的浅拷贝
 *
 * @param {object} obj - 要拷贝的对象
 * @returns {object} 拷贝后的对象
 */
export const shallowCopy = (obj) => {
  if (obj === null || typeof obj !== "object") return obj;
  return Array.isArray(obj) ? [...obj] : { ...obj };
};

/**
 * 创建一个对象的深拷贝
 *
 * @param {*} obj - 要拷贝的对象
 * @returns {*} 拷贝后的对象
 */
export const deepCopy = (obj) => {
  if (obj === null || typeof obj !== "object") return obj;

  const copy = Array.isArray(obj) ? [] : {};

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      copy[key] = deepCopy(obj[key]);
    }
  }

  return copy;
};
