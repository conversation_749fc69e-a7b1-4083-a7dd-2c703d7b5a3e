import { observable, runInAction } from "mobx-miniprogram-lite";
import { persistState, loadPersistedState } from "../../utils/persistence";
import {
  getCampaignDetail,
  joinCampaign,
  getParticipants,
} from "../../../utils/api";
const STORAGE_KEY = "campaign_detail_state";

export const detailState = observable({
  // 从持久化存储加载初始状态
  ...loadPersistedState(STORAGE_KEY, {
    campaignDetail: {},
    loading: false,
    lastError: null,
    lastUpdated: null,
    participants: [],
    userParticipation: null,
  }),

  // 计算属性
  get hasCampaignDetail() {
    return Object.keys(this.campaignDetail).length > 0;
  },

  get campaignStatus() {
    return this.campaignDetail.status || "";
  },

  get isOngoing() {
    return this.campaignDetail.status === "ongoing";
  },

  get isEnded() {
    return this.campaignDetail.status === "ended";
  },

  get isComing() {
    return this.campaignDetail.status === "coming";
  },

  get hasParticipated() {
    return this.userParticipation !== null;
  },

  // 操作方法
  async fetchCampaignDetail(id) {
    runInAction(() => {
      this.loading = true;
      this.lastError = null;
    });

    try {
      const res = await getCampaignDetail({ campaignId: id });

      if (res && res.isSuccess) {
        runInAction(() => {
          this.campaignDetail = res.data;
          this.loading = false;
          this.lastUpdated = new Date();
          this._persistState();
        });

        return res.data;
      } else {
        throw new Error(res.resultMsg || "获取活动详情失败");
      }
    } catch (error) {
      console.error("获取活动详情失败:", error);
      runInAction(() => {
        this.loading = false;
        this.lastError = error;
      });
      wx.showToast({
        title: error.message || "获取活动详情失败",
        icon: "none",
      });
      return {};
    }
  },

  async participateCampaign(campaignId, userId) {
    runInAction(() => {
      this.loading = true;
      this.lastError = null;
    });

    try {
      const res = await joinCampaign({ campaignId, userId });

      if (res && res.isSuccess) {
        runInAction(() => {
          this.userParticipation = res.data;
          this.loading = false;
          this.lastUpdated = new Date();
          this._persistState();
        });

        wx.showToast({
          title: "参与活动成功",
          icon: "success",
        });

        return res.data;
      } else {
        throw new Error(res.resultMsg || "参与活动失败");
      }
    } catch (error) {
      console.error("参与活动失败:", error);
      runInAction(() => {
        this.loading = false;
        this.lastError = error;
      });
      wx.showToast({
        title: "参与活动失败",
        icon: "none",
      });
    }
  },

  async getParticipants(campaignId) {
    runInAction(() => {
      this.loading = true;
      this.lastError = null;
    });

    try {
      const res = await getParticipants({ campaignId });

      if (res && res.isSuccess) {
        runInAction(() => {
          this.participants = res.data;
          this.loading = false;
          this.lastUpdated = new Date();
          this._persistState();
        });

        return res.data;
      } else {
        throw new Error(res.resultMsg || "获取参与者列表失败");
      }
    } catch (error) {
      console.error("获取参与者列表失败:", error);
      runInAction(() => {
        this.loading = false;
        this.lastError = error;
      });
      wx.showToast({
        title: error.message || "获取参与者列表失败",
        icon: "none",
      });
      return [];
    }
  },

  clearCampaignDetail() {
    runInAction(() => {
      this.campaignDetail = {};
      this.userParticipation = null;
      this.participants = [];
      this.lastUpdated = new Date();
      this._persistState();
    });
  },

  // 私有方法，用于持久化状态
  _persistState() {
    const stateToSave = {
      campaignDetail: this.campaignDetail,
      userParticipation: this.userParticipation,
      participants: this.participants,
      lastUpdated: this.lastUpdated,
    };

    persistState(STORAGE_KEY, stateToSave);
  },
});
