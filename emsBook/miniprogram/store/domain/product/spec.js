import { observable, runInAction } from "mobx-miniprogram-lite";
import { persistState, loadPersistedState } from "../../utils/persistence";
import { getProductSpecList } from "../../../utils/api";

const STORAGE_KEY = "product_spec_state";

export const specState = observable({
  ...loadPersistedState(STORAGE_KEY, {
    specs: {},
    loading: false,
    lastError: null,
    lastUpdated: null,
  }),

  isLoading(productId) {
    return this.loading[productId];
  },

  async fetchProductSpecList(productId) {
    runInAction(() => {
      this.loading[productId] = true;
      this.lastError = null;
    });

    try {
      const res = await getProductSpecList({ productId });

      if (res && res.isSuccess) {
        runInAction(() => {
          this.specs[productId] = res.data;
          this.loading[productId] = false;
          this.lastUpdated = new Date();
          this._persistState();
        });

        return res.data;
      } else {
        throw new Error(res.resultMsg || "获取产品规格失败");
      }
    } catch (error) {
      console.error("获取产品规格失败:", error);
      runInAction(() => {
        this.loading[productId] = false;
        this.lastError = error;
      });
      wx.showToast({
        title: error.message || "获取产品规格失败",
        icon: "none",
      });
      return [];
    }
  },

  _persistState() {
    const stateToSave = {
      specs: this.specs,
      lastUpdated: this.lastUpdated,
    };

    persistState(STORAGE_KEY, stateToSave);
  },
});