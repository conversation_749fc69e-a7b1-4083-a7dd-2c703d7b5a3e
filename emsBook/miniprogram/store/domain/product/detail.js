import { observable, runInAction } from "mobx-miniprogram-lite";
import { persistState, loadPersistedState } from "../../utils/persistence";
import {
  getProductDetail
} from "../../../utils/api";
const STORAGE_KEY = "product_detail_state";

export const detailState = observable({
  // 从持久化存储加载初始状态
  ...loadPersistedState(STORAGE_KEY, {
    productDetail: {},
    loading: false,
    lastError: null,
    lastUpdated: null,
  }),


  // 操作方法
  async fetchProductDetail(id) {
    runInAction(() => {
      this.loading = true;
      this.lastError = null;
    });

    try {
      const res = await getProductDetail({ productId: id });

      if (res && res.isSuccess) {
        runInAction(() => {
          this.productDetail = res.data;
          this.loading = false;
          this.lastUpdated = new Date();
          this._persistState();
        });

        return res.data;
      } else {
        throw new Error(res.resultMsg || "获取产品详情失败");
      }
    } catch (error) {
      console.error("获取产品详情失败:", error);
      runInAction(() => {
        this.loading = false;
        this.lastError = error;
      });
      wx.showToast({
        title: error.message || "获取产品详情失败",
        icon: "none",
      });
      return {};
    }
  },



  clearProductDetail() {
    runInAction(() => {
      this.productDetail = {};
      this.lastUpdated = new Date();
      this._persistState();
    });
  },

  // 私有方法，用于持久化状态
  _persistState() {
    const stateToSave = {
      productDetail: this.productDetail,
      lastUpdated: this.lastUpdated,
    };

    persistState(STORAGE_KEY, stateToSave);
  },
});