import { observable, runInAction } from "mobx-miniprogram-lite";
import { persistState, loadPersistedState } from "../../utils/persistence";
import { getCategoryList } from "../../../utils/api";

const STORAGE_KEY = "product_category_state";

export const categoryState = observable({
  ...loadPersistedState(STORAGE_KEY, {
    categories: [],
    loading: false,
    lastError: null,
    lastUpdated: null,
  }),

  get hasCategories() {
    return this.categories.length > 0;
  },

  get isLoading() {
    return this.loading;
  },

  async fetchCategoryList(params = {}) {
    runInAction(() => {
      this.loading = true;
      this.lastError = null;
    });

    try {
      const res = await getCategoryList(params);

      if (res && res.isSuccess) {
        runInAction(() => {
          this.categories = res.data.items;
          this.loading = false;
          this.lastUpdated = new Date();
          this._persistState();
        });

        return res.data;
      } else {
        throw new Error(res.resultMsg || "获取分类列表失败");
      }
    } catch (error) {
      console.error("获取分类列表失败:", error);
      runInAction(() => {
        this.loading = false;
        this.lastError = error;
      });
      wx.showToast({
        title: error.message || "获取分类列表失败",
        icon: "none",
      });
      return {};
    }
  },

  _persistState() {
    const stateToSave = {
      categories: this.categories,
      lastUpdated: this.lastUpdated,
    };

    persistState(STORAGE_KEY, stateToSave);
  },
});