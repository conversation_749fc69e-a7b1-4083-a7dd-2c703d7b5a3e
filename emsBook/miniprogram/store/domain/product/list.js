import { observable, runInAction } from "mobx-miniprogram-lite";
import { persistState, loadPersistedState } from "../../utils/persistence";
import { getProductList } from "../../../utils/api";

const STORAGE_KEY = "product_list_state";

export const listState = observable({
  // 从持久化存储加载初始状态
  ...loadPersistedState(STORAGE_KEY, {
    products: [],
    loading: false,
    lastError: null,
    lastUpdated: null,
    pagination: {
      page: 1,
      pageSize: 10,
      total: 0,
    },
  }),

  // 计算属性
  get hasProducts() {
    return this.products.length > 0;
  },

  get isLoading() {
    return this.loading;
  },

  // 操作方法
  async fetchProductList(params = {}) {
    runInAction(() => {
      this.loading = true;
      this.lastError = null;
    });

    try {
      const res = await getProductList(params);

      if (res && res.isSuccess) {
        runInAction(() => {
          this.products = res.data.items;
          this.pagination = res.data.page;
          this.loading = false;
          this.lastUpdated = new Date();
          this._persistState();
        });

        return res.data;
      } else {
        throw new Error(res.resultMsg || "获取产品列表失败");
      }
    } catch (error) {
      console.error("获取产品列表失败:", error);
      runInAction(() => {
        this.loading = false;
        this.lastError = error;
      });
      wx.showToast({
        title: error.message || "获取产品列表失败",
        icon: "none",
      });
      return {};
    }
  },

  // 私有方法，用于持久化状态
  _persistState() {
    const stateToSave = {
      products: this.products,
      pagination: this.pagination,
      lastUpdated: this.lastUpdated,
    };

    persistState(STORAGE_KEY, stateToSave);
  },
});