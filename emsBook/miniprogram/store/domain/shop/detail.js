import { observable, runInAction } from "mobx-miniprogram-lite";
import { persistState, loadPersistedState } from "../../utils/persistence";
import {
  getShopDetail
} from "../../../utils/api";
const STORAGE_KEY = "campaign_detail_state";

export const detailState = observable({
  // 从持久化存储加载初始状态
  ...loadPersistedState(STORAGE_KEY, {
    shopDetail: {},
    loading: false,
    lastError: null,
    lastUpdated: null,
  }),


  // 操作方法
  async fetchShopDetail(id) {
    runInAction(() => {
      this.loading = true;
      this.lastError = null;
    });

    try {
      const res = await getShopDetail({ shopId: id });

      if (res && res.isSuccess) {
        runInAction(() => {
          this.shopDetail = res.data;
          this.loading = false;
          this.lastUpdated = new Date();
          this._persistState();
        });

        return res.data;
      } else {
        throw new Error(res.resultMsg || "获取商店详情失败");
      }
    } catch (error) {
      console.error("获取商店详情失败:", error);
      runInAction(() => {
        this.loading = false;
        this.lastError = error;
      });
      wx.showToast({
        title: error.message || "获取商店详情失败",
        icon: "none",
      });
      return {};
    }
  },



  clearshopDetail() {
    runInAction(() => {
      this.shopDetail = {};
      this.lastUpdated = new Date();
      this._persistState();
    });
  },

  // 私有方法，用于持久化状态
  _persistState() {
    const stateToSave = {
      shopDetail: this.shopDetail,
      lastUpdated: this.lastUpdated,
    };

    persistState(STORAGE_KEY, stateToSave);
  },
});
