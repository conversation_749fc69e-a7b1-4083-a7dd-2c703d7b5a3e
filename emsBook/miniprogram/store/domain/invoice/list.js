/**
 * 发票列表状态
 * 管理发票列表相关的状态
 */
import { observable, runInAction } from "mobx-miniprogram-lite";
import { persistState, loadPersistedState } from "../../utils/persistence";
import { debounce } from "../../utils/helpers";

const STORAGE_KEY = "invoice_list_state";

export const invoiceListState = observable({
  // 从持久化存储加载初始状态
  ...loadPersistedState(STORAGE_KEY, {
    invoiceList: [],
    total: 0,
    loading: false,
    currentStatus: "all", // all, pending, completed, cancelled
    currentPage: 1,
    pageSize: 10,
    lastError: null,
    lastUpdated: null,
  }),

  // 计算属性
  get hasInvoices() {
    return this.invoiceList.length > 0;
  },

  get pendingInvoices() {
    return this.invoiceList.filter((invoice) => invoice.status === "pending");
  },

  get completedInvoices() {
    return this.invoiceList.filter((invoice) => invoice.status === "completed");
  },

  get cancelledInvoices() {
    return this.invoiceList.filter((invoice) => invoice.status === "cancelled");
  },

  get refundedInvoices() {
    return this.invoiceList.filter((invoice) => invoice.status === "refunded");
  },
  get paginationInfo() {
    return {
      currentPage: this.currentPage,
      pageSize: this.pageSize,
      total: this.total,
    };
  },

  // 操作方法
  async getInvoiceList(query = {}) {
    runInAction(() => {
      this.loading = true;
      this.lastError = null;
    });

    try {
      // 模拟API调用，实际项目中应替换为真实API
      // const res = await getInvoiceList({
      //   page: this.currentPage,
      //   pageSize: this.pageSize,
      //   status: this.currentStatus,
      //   ...query
      // });
      const mockData = {
        data: {
          items: [
            {
              id: "1",
              bookName: "小王子",
              bookId: "101",
              coverUrl: "/static/images/logo.jpg",
              orderTime: "2023-07-15 14:30",
              returnTime: "2023-07-30 14:30",
              status: "pending",
              price: 5.0,
            },
            {
              id: "2",
              bookName: "百年孤独",
              bookId: "102",
              coverUrl: "/static/images/logo.jpg",
              orderTime: "2023-07-10 09:15",
              returnTime: "2023-07-25 09:15",
              status: "completed",
              price: 8.0,
            },
            {
              id: "3",
              bookName: "活着",
              bookId: "103",
              coverUrl: "/static/images/logo.jpg",
              orderTime: "2023-07-05 16:45",
              returnTime: "2023-07-20 16:45",
              status: "cancelled",
              price: 6.5,
            },
          ],
          total: 3,
        },
      };

      runInAction(() => {
        this.invoiceList = [...mockData.data.items];
        this.total = mockData.data.total;
        this.loading = false;
        this.lastUpdated = new Date();
        this._persistState();
      });

      return mockData.data.items;
    } catch (error) {
      console.error("获取发票列表失败:", error);
      runInAction(() => {
        this.loading = false;
        this.lastError = error;
      });
      wx.showToast({
        title: "获取发票列表失败",
        icon: "none",
      });
      return [];
    }
  },

  // 防抖处理的获取发票列表方法
  debouncedGetInvoiceList: debounce(async function (query = {}) {
    return await this.getInvoiceList(query);
  }, 300),

  setCurrentStatus(status) {
    runInAction(() => {
      this.currentStatus = status;
      this.lastUpdated = new Date();
      this._persistState();
    });
  },

  setPage(page) {
    runInAction(() => {
      this.currentPage = page;
      this.lastUpdated = new Date();
      this._persistState();
    });
  },

  setPageSize(size) {
    runInAction(() => {
      this.pageSize = size;
      this.currentPage = 1; // 重置到第一页
      this.lastUpdated = new Date();
      this._persistState();
    });
  },

  clearInvoiceList() {
    runInAction(() => {
      this.invoiceList = [];
      this.total = 0;
      this.currentPage = 1;
      this.lastUpdated = new Date();
      this._persistState();
    });
  },

  // 新增：更新列表中特定发票的状态
  updateInvoiceStatusInList(invoiceId, status) {
    runInAction(() => {
      const invoiceIndex = this.invoiceList.findIndex(
        (invoice) => invoice.id === invoiceId
      );
      if (invoiceIndex !== -1) {
        // 创建一个新的发票对象以确保 MobX 检测到变化
        this.invoiceList[invoiceIndex] = {
          ...this.invoiceList[invoiceIndex],
          status: status,
        };
        // 可选：强制触发数组更新，如果直接修改对象属性 MobX 未响应
        // this.invoiceList = [...this.invoiceList];
        this.lastUpdated = new Date();
        this._persistState();
        console.log(
          `Invoice ${invoiceId} status updated to ${status} in invoiceListState`
        );
      } else {
        console.warn(
          `Invoice ${invoiceId} not found in invoiceListState for status update.`
        );
      }
    });
  },

  // 私有方法，用于持久化状态
  _persistState() {
    const stateToSave = {
      invoiceList: this.invoiceList,
      total: this.total,
      currentStatus: this.currentStatus,
      currentPage: this.currentPage,
      pageSize: this.pageSize,
      lastUpdated: this.lastUpdated,
    };

    persistState(STORAGE_KEY, stateToSave);
  },
});
