/**
 * 发票详情状态
 * 管理发票详情相关的状态
 */
import { observable, runInAction } from "mobx-miniprogram-lite";
import { persistState, loadPersistedState } from "../../utils/persistence";
// import { getOrderDetail, cancelOrder as apiCancelOrder, updateOrderStatus as apiUpdateOrderStatus } from "../../../utils/api"; // 假设有这些API

const STORAGE_KEY = "invoice_detail_state";

export const invoiceDetailState = observable({
  // 从持久化存储加载初始状态
  ...loadPersistedState(STORAGE_KEY, {
    invoiceDetail: {},
    loading: false,
    lastError: null,
    lastUpdated: null,
  }),

  // 计算属性
  get hasInvoiceDetail() {
    return Object.keys(this.invoiceDetail).length > 0;
  },

  get invoiceStatus() {
    return this.invoiceDetail.status || "";
  },

  get isPending() {
    return this.invoiceDetail.status === "pending";
  },

  get isCompleted() {
    return this.invoiceDetail.status === "completed";
  },

  get isCancelled() {
    return this.invoiceDetail.status === "cancelled";
  },

  get isRefunded() {
    return this.invoiceDetail.status === "refunded";
  },

  // 操作方法
  async getInvoiceDetail(id) {
    runInAction(() => {
      this.loading = true;
      this.lastError = null;
    });

    try {
      // TODO: 替换为真实API调用
      // const res = await getInvoiceDetail(id);
      const mockData = {
        data: {
          id: id,
          bookName: "小王子",
          bookId: "101",
          coverUrl: "/static/images/logo.jpg",
          orderTime: "2023-07-15 14:30",
          returnTime: "2023-07-30 14:30",
          status: "pending", // 初始状态
          price: 5.0,
          location: "中央图书馆",
          address: "北京市海淀区中关村南大街5号",
          latitude: 39.9761,
          longitude: 116.3282,
          paymentId: `payment_${id}`, // 假设关联的支付ID
        },
      };

      runInAction(() => {
        this.invoiceDetail = mockData.data;
        this.loading = false;
        this.lastUpdated = new Date();
        this._persistState();
      });

      return mockData.data;
    } catch (error) {
      console.error("获取发票详情失败:", error);
      runInAction(() => {
        this.loading = false;
        this.lastError = error;
      });
      wx.showToast({
        title: "获取发票详情失败",
        icon: "none",
      });
      return {};
    }
  },

  async cancelInvoice(id) {
    runInAction(() => {
      this.loading = true;
      this.lastError = null;
    });

    try {
      // TODO: 替换为真实API调用
      // const res = await apiCancelInvoice(id);
      const mockData = {
        success: true,
        message: "发票取消成功",
      };

      if (mockData.success) {
        // 调用通用状态更新方法
        this.updateInvoiceStatus(id, "cancelled");

        wx.showToast({
          title: "发票取消成功",
          icon: "success",
        });

        return true;
      }

      return false;
    } catch (error) {
      console.error("取消发票失败:", error);
      runInAction(() => {
        this.loading = false;
        this.lastError = error;
      });
      wx.showToast({
        title: "取消发票失败",
        icon: "none",
      });
      return false;
    }
  },

  // 新增：更新发票状态 Action
  async updateInvoiceStatus(invoiceId, status) {
    // 检查是否是当前详情页的发票
    if (this.invoiceDetail.id !== invoiceId) {
      console.warn(
        `updateInvoiceStatus called for invoice ${invoiceId}, but current detail is ${this.invoiceDetail.id}`
      );
      // 可选：如果不是当前发票，可以考虑是否需要调用API更新后端状态，或者依赖列表页更新
      // await apiUpdateInvoiceStatus(invoiceId, status); // 调用API更新后端状态
      return; // 如果只更新当前详情页，则直接返回
    }

    runInAction(() => {
      // 更新本地状态
      this.invoiceDetail = {
        ...this.invoiceDetail,
        status: status,
      };
      this.lastUpdated = new Date();
      this._persistState();
      console.log(
        `Invoice ${invoiceId} status updated to ${status} in invoiceDetailState`
      );
    });

    // 可选：如果需要，可以在这里调用API更新后端状态
    // try {
    //   await apiUpdateInvoiceStatus(invoiceId, status);
    // } catch (error) {
    //   console.error(`Failed to update invoice ${invoiceId} status on backend:`, error);
    //   // 处理API调用失败的情况，例如回滚本地状态或提示用户
    // }
  },

  clearInvoiceDetail() {
    runInAction(() => {
      this.invoiceDetail = {};
      this.lastUpdated = new Date();
      this._persistState();
    });
  },

  // 私有方法，用于持久化状态
  _persistState() {
    const stateToSave = {
      invoiceDetail: this.invoiceDetail,
      lastUpdated: this.lastUpdated,
    };

    persistState(STORAGE_KEY, stateToSave);
  },
});
