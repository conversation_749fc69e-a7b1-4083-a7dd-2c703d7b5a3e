/**
 * 帖子状态模块导出
 * 集中导出帖子相关的所有状态模块
 */

import {
  observable,
  computed,
  action,
  runInAction,
} from "mobx-miniprogram-lite";
import { postsList } from "../../../utils/api";
import { persistState, loadPersistedState } from "../../utils/persistence";

const STORAGE_KEY = "posts";

// 状态定义
export const postsState = observable({
  // 从持久化存储加载初始状态
  ...loadPersistedState(STORAGE_KEY, {
    list: [], // 帖子列表
    total: 0, // 帖子总数
    currentPage: 1, // 当前页码
    pageSize: 10, // 每页数量
    isLoading: false, // 加载状态
    lastError: null, // 最后一次错误
    lastUpdated: null, // 最后更新时间
    categories: [], // 帖子分类
    currentCategory: null, // 当前选中的分类
  }),

  // 计算属性
  get hasPosts() {
    return this.list.length > 0;
  },

  get hasMorePosts() {
    return this.list.length < this.total;
  },

  get categorizedPosts() {
    if (!this.currentCategory) return this.list;
    return this.list.filter((post) => post.categoryId === this.currentCategory);
  },

  // 内部方法
  _persistState() {
    persistState(STORAGE_KEY, {
      list: this.list,
      total: this.total,
      currentPage: this.currentPage,
      lastUpdated: this.lastUpdated,
      categories: this.categories,
      currentCategory: this.currentCategory,
    });
  },

  // 操作方法
  async fetchPosts(
    params = { page: 1, pageSize: 10, reset: false, categoryId: null }
  ) {
    this.isLoading = true;
    this.lastError = null;

    // 如果需要重置，则清空当前帖子列表
    if (params.reset) {
      this.list = [];
      this.currentPage = 1;
    }

    // 设置当前分类
    if (params.categoryId !== undefined) {
      this.currentCategory = params.categoryId;
    }

    try {
      const query = {
        page: params.page || this.currentPage,
        pageSize: params.pageSize || this.pageSize,
        categoryId: this.currentCategory,
        ...params.query,
      };

      const res = await postsList({ query });

      runInAction(() => {
        // 如果是第一页或重置，则替换帖子列表；否则追加
        this.list =
          params.page === 1 || params.reset
            ? res.data.list || []
            : [...this.list, ...(res.data.list || [])];

        this.total = res.data.total || 0;
        this.currentPage = (params.page || this.currentPage) + 1;
        this.lastUpdated = new Date();
        this._persistState();
      });

      return this.list;
    } catch (error) {
      runInAction(() => {
        this.lastError = error;
      });
      return [];
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  },

  /**
   * 获取帖子分类
   */
  async fetchCategories() {
    this.isLoading = true;
    this.lastError = null;

    try {
      // 假设有一个获取分类的API
      const res = await postsList({ query: { type: "categories" } });

      runInAction(() => {
        this.categories = res.data.categories || [];
        this.lastUpdated = new Date();
        this._persistState();
      });

      return this.categories;
    } catch (error) {
      runInAction(() => {
        this.lastError = error;
      });
      return [];
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  },

  /**
   * 设置当前分类
   * @param {string|null} categoryId - 分类ID，null表示全部
   */
  setCurrentCategory(categoryId) {
    runInAction(() => {
      this.currentCategory = categoryId;
      this._persistState();
    });
  },

  // 清除帖子数据
  clearPostsData() {
    runInAction(() => {
      this.list = [];
      this.total = 0;
      this.currentPage = 1;
      this.currentCategory = null;
      this.lastUpdated = new Date();
      this._persistState();
    });
  },
});
