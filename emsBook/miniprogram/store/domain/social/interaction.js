/**
 * 社交互动状态
 * 管理点赞、收藏等互动相关的状态
 */
import {
  observable,
  computed,
  action,
  runInAction,
} from "mobx-miniprogram-lite";
import { wxRequest } from "../../../utils/wxRequest";
import { persistState, loadPersistedState } from "../../utils/persistence";

const STORAGE_KEY = "social_interaction";

// 状态定义
export const interactionState = observable({
  // 从持久化存储加载初始状态
  ...loadPersistedState(STORAGE_KEY, {
    likedItems: [], // 用户点赞的项目列表
    favoritedItems: [], // 用户收藏的项目列表
    isLoading: false, // 加载状态
    lastError: null, // 最后一次错误
    lastUpdated: null, // 最后更新时间
  }),

  // 计算属性
  get hasLikedItems() {
    return this.likedItems.length > 0;
  },

  get hasFavoritedItems() {
    return this.favoritedItems.length > 0;
  },

  // 内部方法
  _persistState() {
    persistState(STORAGE_KEY, {
      likedItems: this.likedItems,
      favoritedItems: this.favoritedItems,
      lastUpdated: this.lastUpdated,
    });
  },

  // 操作方法
  async fetchLikedItems() {
    this.isLoading = true;
    this.lastError = null;

    try {
      const res = await wxRequest({}, "social/likes", true, "获取点赞列表");
      runInAction(() => {
        this.likedItems = res.data.list || [];
        this.lastUpdated = new Date();
        this._persistState();
      });
      return this.likedItems;
    } catch (error) {
      runInAction(() => {
        this.lastError = error;
      });
      return [];
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  },

  async fetchFavoritedItems() {
    this.isLoading = true;
    this.lastError = null;

    try {
      const res = await wxRequest({}, "social/favorites", true, "获取收藏列表");
      runInAction(() => {
        this.favoritedItems = res.data.list || [];
        this.lastUpdated = new Date();
        this._persistState();
      });
      return this.favoritedItems;
    } catch (error) {
      runInAction(() => {
        this.lastError = error;
      });
      return [];
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  },

  async toggleLike(params) {
    if (!params.itemId || !params.itemType) {
      console.error("toggleLike: itemId and itemType are required");
      return false;
    }

    this.isLoading = true;
    this.lastError = null;

    try {
      const res = await wxRequest(
        {
          method: "POST",
          data: params,
        },
        "social/like/toggle",
        true,
        "更新点赞状态"
      );

      // 更新点赞列表
      await this.fetchLikedItems();
      return res.data.isLiked;
    } catch (error) {
      runInAction(() => {
        this.lastError = error;
      });
      return false;
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  },

  async toggleFavorite(params) {
    if (!params.itemId || !params.itemType) {
      console.error("toggleFavorite: itemId and itemType are required");
      return false;
    }

    this.isLoading = true;
    this.lastError = null;

    try {
      const res = await wxRequest(
        {
          method: "POST",
          data: params,
        },
        "social/favorite/toggle",
        true,
        "更新收藏状态"
      );

      // 更新收藏列表
      await this.fetchFavoritedItems();
      return res.data.isFavorited;
    } catch (error) {
      runInAction(() => {
        this.lastError = error;
      });
      return false;
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  },

  // 检查项目是否已被点赞
  isItemLiked(itemId, itemType) {
    return this.likedItems.some(
      (item) => item.itemId === itemId && item.itemType === itemType
    );
  },

  // 检查项目是否已被收藏
  isItemFavorited(itemId, itemType) {
    return this.favoritedItems.some(
      (item) => item.itemId === itemId && item.itemType === itemType
    );
  },

  // 清除互动数据
  clearInteractionData() {
    runInAction(() => {
      this.likedItems = [];
      this.favoritedItems = [];
      this.lastUpdated = new Date();
      this._persistState();
    });
  },
});
