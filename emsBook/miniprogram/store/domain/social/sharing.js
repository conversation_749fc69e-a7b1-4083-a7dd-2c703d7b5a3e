/**
 * 社交分享状态
 * 管理分享相关的状态
 */
import {
  observable,
  computed,
  action,
  runInAction,
} from "mobx-miniprogram-lite";
import { wxRequest } from "../../../utils/wxRequest";
import { persistState, loadPersistedState } from "../../utils/persistence";

const STORAGE_KEY = "social_sharing";

// 状态定义
export const sharingState = observable({
  // 从持久化存储加载初始状态
  ...loadPersistedState(STORAGE_KEY, {
    shares: [], // 分享记录列表
    shareTotal: 0, // 分享总数
    shareStats: {}, // 分享统计数据
    currentPage: 1, // 当前页码
    pageSize: 10, // 每页数量
    isLoading: false, // 加载状态
    lastError: null, // 最后一次错误
    lastUpdated: null, // 最后更新时间
  }),

  // 计算属性
  get hasShares() {
    return this.shares.length > 0;
  },

  get hasMoreShares() {
    return this.shares.length < this.shareTotal;
  },

  // 内部方法
  _persistState() {
    persistState(STORAGE_KEY, {
      shares: this.shares,
      shareTotal: this.shareTotal,
      shareStats: this.shareStats,
      currentPage: this.currentPage,
      lastUpdated: this.lastUpdated,
    });
  },

  // 操作方法
  async fetchShares(params = { page: 1, pageSize: 10, reset: false }) {
    this.isLoading = true;
    this.lastError = null;

    // 如果需要重置，则清空当前分享列表
    if (params.reset) {
      this.shares = [];
      this.currentPage = 1;
    }

    try {
      const res = await wxRequest(
        {
          data: {
            page: params.page || this.currentPage,
            pageSize: params.pageSize || this.pageSize,
          },
        },
        "social/shares",
        true,
        "获取分享列表"
      );

      runInAction(() => {
        // 如果是第一页或重置，则替换分享列表；否则追加
        this.shares =
          params.page === 1 || params.reset
            ? res.data.list || []
            : [...this.shares, ...(res.data.list || [])];

        this.shareTotal = res.data.total || 0;
        this.currentPage = (params.page || this.currentPage) + 1;
        this.lastUpdated = new Date();
        this._persistState();
      });

      return this.shares;
    } catch (error) {
      runInAction(() => {
        this.lastError = error;
      });
      return [];
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  },

  /**
   * 获取分享统计数据
   */
  async fetchShareStats() {
    this.isLoading = true;
    this.lastError = null;

    try {
      const res = await wxRequest(
        {},
        "social/shares/stats",
        true,
        "获取分享统计"
      );

      runInAction(() => {
        this.shareStats = res.data || {};
        this.lastUpdated = new Date();
        this._persistState();
      });

      return this.shareStats;
    } catch (error) {
      runInAction(() => {
        this.lastError = error;
      });
      return {};
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  },

  /**
   * 记录分享行为
   * @param {object} params - 分享参数
   * @param {string} params.itemId - 分享项目ID
   * @param {string} params.itemType - 分享项目类型
   * @param {string} params.shareType - 分享类型（wechat, poster等）
   */
  async recordShare(params) {
    if (!params.itemId || !params.itemType || !params.shareType) {
      console.error("recordShare: itemId, itemType and shareType are required");
      return false;
    }

    try {
      const res = await wxRequest(
        {
          method: "POST",
          data: params,
        },
        "social/share/record",
        true,
        "记录分享"
      );

      // 更新分享列表和统计数据
      if (res.data.success) {
        this.fetchShares({ reset: true });
        this.fetchShareStats();
      }

      return res.data.success;
    } catch (error) {
      console.error("记录分享失败:", error);
      return false;
    }
  },

  // 清除分享数据
  clearShareData() {
    runInAction(() => {
      this.shares = [];
      this.shareTotal = 0;
      this.shareStats = {};
      this.currentPage = 1;
      this.lastUpdated = new Date();
      this._persistState();
    });
  },
});
