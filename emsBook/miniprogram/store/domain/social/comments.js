/**
 * 社交评论状态
 * 管理评论相关的状态
 */
import {
  observable,
  computed,
  action,
  runInAction,
} from "mobx-miniprogram-lite";
import { wxRequest } from "../../../utils/wxRequest";
import { persistState, loadPersistedState } from "../../utils/persistence";

const STORAGE_KEY = "social_comments";

// 状态定义
export const commentsState = observable({
  // 从持久化存储加载初始状态
  ...loadPersistedState(STORAGE_KEY, {
    comments: [], // 评论列表
    commentTotal: 0, // 评论总数
    currentPage: 1, // 当前页码
    pageSize: 10, // 每页数量
    isLoading: false, // 加载状态
    lastError: null, // 最后一次错误
    lastUpdated: null, // 最后更新时间
  }),

  // 计算属性
  get hasComments() {
    return this.comments.length > 0;
  },

  get hasMoreComments() {
    return this.comments.length < this.commentTotal;
  },

  // 内部方法
  _persistState() {
    persistState(STORAGE_KEY, {
      comments: this.comments,
      commentTotal: this.commentTotal,
      currentPage: this.currentPage,
      lastUpdated: this.lastUpdated,
    });
  },

  // 操作方法
  async fetchComments(params = { page: 1, pageSize: 10, reset: false }) {
    this.isLoading = true;
    this.lastError = null;

    // 如果需要重置，则清空当前评论列表
    if (params.reset) {
      this.comments = [];
      this.currentPage = 1;
    }

    try {
      const res = await wxRequest(
        {
          data: {
            page: params.page || this.currentPage,
            pageSize: params.pageSize || this.pageSize,
            itemId: params.itemId,
            itemType: params.itemType,
          },
        },
        "social/comments",
        true,
        "获取评论列表"
      );

      runInAction(() => {
        // 如果是第一页或重置，则替换评论列表；否则追加
        this.comments =
          params.page === 1 || params.reset
            ? res.data.list || []
            : [...this.comments, ...(res.data.list || [])];

        this.commentTotal = res.data.total || 0;
        this.currentPage = (params.page || this.currentPage) + 1;
        this.lastUpdated = new Date();
        this._persistState();
      });

      return this.comments;
    } catch (error) {
      runInAction(() => {
        this.lastError = error;
      });
      return [];
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  },

  async addComment(params) {
    if (!params.itemId || !params.itemType || !params.content) {
      console.error("addComment: itemId, itemType and content are required");
      return false;
    }

    this.isLoading = true;
    this.lastError = null;

    try {
      const res = await wxRequest(
        {
          method: "POST",
          data: params,
        },
        "social/comment/add",
        true,
        "添加评论"
      );

      // 重新获取评论列表，确保数据最新
      await this.fetchComments({
        itemId: params.itemId,
        itemType: params.itemType,
        reset: true,
      });

      return res.data.success;
    } catch (error) {
      runInAction(() => {
        this.lastError = error;
      });
      return false;
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  },

  /**
   * 删除评论
   * @param {string} commentId - 评论ID
   * @param {object} queryParams - 查询参数，用于重新获取评论列表
   */
  async deleteComment(commentId, queryParams) {
    if (!commentId) {
      console.error("deleteComment: commentId is required");
      return false;
    }

    this.isLoading = true;
    this.lastError = null;

    try {
      const res = await wxRequest(
        {
          method: "DELETE",
        },
        `social/comment/${commentId}`,
        true,
        "删除评论"
      );

      // 重新获取评论列表，确保数据最新
      await this.fetchComments({
        ...queryParams,
        reset: true,
      });

      return res.data.success;
    } catch (error) {
      runInAction(() => {
        this.lastError = error;
      });
      return false;
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  },

  // 清除评论数据
  clearCommentData() {
    runInAction(() => {
      this.comments = [];
      this.commentTotal = 0;
      this.currentPage = 1;
      this.lastUpdated = new Date();
      this._persistState();
    });
  },
});
