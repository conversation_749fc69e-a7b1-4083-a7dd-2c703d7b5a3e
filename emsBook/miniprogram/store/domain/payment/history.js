/**
 * 支付历史状态
 * 管理支付历史记录相关的状态
 */
import { observable, runInAction } from "mobx-miniprogram-lite";
import { persistState, loadPersistedState } from "../../utils/persistence";
import { getPaymentList } from "../../../utils/api";

const STORAGE_KEY = "payment_history_state";

export const historyState = observable({
  // 从持久化存储加载初始状态
  ...loadPersistedState(STORAGE_KEY, {
    paymentList: [],
    total: 0,
    loading: false,
    currentPage: 1,
    pageSize: 10,
    lastError: null,
    lastUpdated: null,
  }),

  // 计算属性
  get hasPayments() {
    return this.paymentList.length > 0;
  },

  get paginationInfo() {
    return {
      currentPage: this.currentPage,
      pageSize: this.pageSize,
      total: this.total,
    };
  },

  // 操作方法
  async getPaymentList(query = {}) {
    runInAction(() => {
      this.loading = true;
      this.lastError = null;
    });

    try {
      const res = await getPaymentList({
        page: this.currentPage,
        pageSize: this.pageSize,
        ...query,
      });

      runInAction(() => {
        this.paymentList = res.data.items;
        this.total = res.data.total;
        this.loading = false;
        this.lastUpdated = new Date();
        this._persistState();
      });

      return res.data;
    } catch (error) {
      console.error("获取支付列表失败:", error);
      runInAction(() => {
        this.loading = false;
        this.lastError = error;
      });
      wx.showToast({
        title: "获取支付列表失败",
        icon: "none",
      });
      return null;
    }
  },

  setPage(page) {
    runInAction(() => {
      this.currentPage = page;
      this.lastUpdated = new Date();
      this._persistState();
    });
  },

  setPageSize(size) {
    runInAction(() => {
      this.pageSize = size;
      this.currentPage = 1; // 重置到第一页
      this.lastUpdated = new Date();
      this._persistState();
    });
  },

  clearPaymentList() {
    runInAction(() => {
      this.paymentList = [];
      this.total = 0;
      this.currentPage = 1;
      this.lastUpdated = new Date();
      this._persistState();
    });
  },

  // 私有方法，用于持久化状态
  _persistState() {
    const stateToSave = {
      paymentList: this.paymentList,
      total: this.total,
      currentPage: this.currentPage,
      pageSize: this.pageSize,
      lastUpdated: this.lastUpdated,
    };

    persistState(STORAGE_KEY, stateToSave);
  },
});
