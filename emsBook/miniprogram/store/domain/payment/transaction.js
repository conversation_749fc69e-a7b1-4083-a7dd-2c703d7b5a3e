/**
 * 支付交易状态
 * 管理支付交易相关的状态
 */
import { observable, runInAction } from "mobx-miniprogram-lite";
import { persistState, loadPersistedState } from "../../utils/persistence";
import {
  createPayment,
  getPaymentDetail,
  refundPayment as apiRefundPayment, // Rename to avoid conflict
  pay,
} from "../../../utils/api";
// Import invoice state to update invoice status after refund
import { invoiceState } from "../invoice/index";

const STORAGE_KEY = "payment_transaction_state";

export const transactionState = observable({
  // 从持久化存储加载初始状态
  ...loadPersistedState(STORAGE_KEY, {
    paymentDetail: {},
    loading: false,
    lastError: null,
    lastUpdated: null,
  }),

  // 计算属性
  get hasPaymentDetail() {
    return Object.keys(this.paymentDetail).length > 0;
  },

  get paymentStatus() {
    return this.paymentDetail.status || "";
  },

  get isPaid() {
    return this.paymentDetail.status === "paid";
  },

  // 操作方法
  async getPaymentDetail(paymentId) {
    runInAction(() => {
      this.loading = true;
      this.lastError = null;
    });

    try {
      const res = await getPaymentDetail({ paymentId });

      runInAction(() => {
        this.paymentDetail = res.data;
        this.loading = false;
        this.lastUpdated = new Date();
        this._persistState();
      });

      return res.data;
    } catch (error) {
      console.error("获取支付详情失败:", error);
      runInAction(() => {
        this.loading = false;
        this.lastError = error;
      });
      wx.showToast({
        title: "获取支付详情失败",
        icon: "none",
      });
      return null;
    }
  },

  async createPayment(paymentData) {
    runInAction(() => {
      this.loading = true;
      this.lastError = null;
    });

    try {
      const res = await createPayment(paymentData);

      runInAction(() => {
        this.loading = false;
        this.lastUpdated = new Date();
      });

      return res.data;
    } catch (error) {
      console.error("创建支付失败:", error);
      runInAction(() => {
        this.loading = false;
        this.lastError = error;
      });
      wx.showToast({
        title: "创建支付失败",
        icon: "none",
      });
      return null;
    }
  },

  async requestWxPayment(paymentData) {
    try {
      // 创建支付订单
      const paymentResult = await this.createPayment(paymentData);
      if (!paymentResult) return false;

      // 调用微信支付
      const payResult = await pay({
        paymentId: paymentResult.id,
      });

      if (!payResult || !payResult.data) {
        wx.showToast({
          title: "获取支付参数失败",
          icon: "none",
        });
        return false;
      }

      // 发起微信支付
      return new Promise((resolve) => {
        wx.requestPayment({
          nonceStr: payResult.data.nonceStr,
          signType: "MD5",
          paySign: payResult.data.paySign,
          timeStamp: payResult.data.timeStamp,
          package: payResult.data.packageValue,
          success: () => {
            wx.showToast({
              title: "支付成功",
              icon: "success",
            });
            resolve(true);
          },
          fail: (err) => {
            console.error("支付失败:", err);
            wx.showToast({
              title: "支付失败",
              icon: "none",
            });
            resolve(false);
          },
        });
      });
    } catch (error) {
      console.error("发起支付失败:", error);
      runInAction(() => {
        this.lastError = error;
      });
      wx.showToast({
        title: "发起支付失败",
        icon: "none",
      });
      return false;
    }
  },

  async refundPayment(paymentId, orderId, reason = "") {
    // Add orderId parameter
    runInAction(() => {
      this.loading = true;
      this.lastError = null;
    });

    try {
      // Call the actual refund API
      const res = await apiRefundPayment({
        paymentId,
        reason,
      });

      runInAction(() => {
        this.loading = false;
        // If the current payment detail is the one being refunded, update its status
        if (this.paymentDetail.id === paymentId) {
          this.paymentDetail = {
            ...this.paymentDetail,
            status: "refunded", // Update payment status
          };
        }
        this.lastUpdated = new Date();
        this._persistState(); // Persist payment state changes
      });

      // After successful refund, update the corresponding order status
      if (orderId) {
        // Use the imported invoiceState to call the update function
        invoiceState.detail.updateInvoiceStatus(orderId, "refunded");
        // Also update the invoice status in the list state
        invoiceState.list.updateInvoiceStatusInList(orderId, "refunded");
        console.log(
          `Triggered invoice status update for invoice ${orderId} after refund.`
        );
      } else {
        console.warn(
          "orderId not provided to refundPayment, cannot update order status."
        );
      }

      wx.showToast({
        title: "退款申请成功",
        icon: "success",
      });

      return res.data;
    } catch (error) {
      console.error("申请退款失败:", error);
      runInAction(() => {
        this.loading = false;
        this.lastError = error;
      });
      wx.showToast({
        title: "申请退款失败",
        icon: "none",
      });
      return null;
    }
  },

  clearPaymentDetail() {
    runInAction(() => {
      this.paymentDetail = {};
      this.lastUpdated = new Date();
      this._persistState();
    });
  },

  // 私有方法，用于持久化状态
  _persistState() {
    const stateToSave = {
      paymentDetail: this.paymentDetail,
      lastUpdated: this.lastUpdated,
    };

    persistState(STORAGE_KEY, stateToSave);
  },
});
