import { observable, runInAction } from "mobx-miniprogram-lite";
import { getRewardStats, wxGetUserInfo, wxUpdateUserInfo } from "../../../utils/api";
import { persistState, loadPersistedState } from "../../utils/persistence";

/**
 * 用户资料状态
 * 管理用户个人资料相关的状态
 */
const STORAGE_KEY = "user_profile";

export const profileState = observable({
  // 从持久化存储加载初始状态
  ...loadPersistedState(STORAGE_KEY, {
    id: "",
    nickname: "",
    avatar: "",
    phone: "",
    gender: "",
    birthday: "",
    vipNo: "",
    isLoading: false,
    lastError: null,
    lastUpdated: null,
  }),

  // 计算属性
  get hasProfile() {
    return !!this.id;
  },

  get displayName() {
    return this.nickname || this.phone || this.vipNo || "用户";
  },

  get formattedVipNo() {
    return (this.vipNo || this.phone || "").slice(0, 10);
  },

  // 操作方法
  async fetchUserProfile() {
    this.isLoading = true;
    this.lastError = null;

    try {
      const res = await wxGetUserInfo();
      // const statsRes = await getRewardStats()

      runInAction(() => {
        // 更新用户资料
        Object.assign(this, res.data.userInfo || res.data.wechat_user);
        this.lastUpdated = new Date();

        // 持久化状态
        this._persistState();
      });

      return res.data;
    } catch (error) {
      runInAction(() => {
        this.lastError = error;
      });
      return null;
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  },

  async updateProfile(profileData) {
    this.isLoading = true;
    this.lastError = null;

    try {
      const res = await wxUpdateUserInfo({
        method: "put",
        data: profileData,
      });

      runInAction(() => {
        // 更新用户资料
        Object.assign(this, res.data.userInfo || res.data.wechat_user);
        this.lastUpdated = new Date();

        // 持久化状态
        this._persistState();
      });

      return res.data;
    } catch (error) {
      runInAction(() => {
        this.lastError = error;
      });
      return null;
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  },


  async fetchUserWallet() {
    this.isLoading = true;
    this.lastError = null;

    try {
      const res = await getRewardStats()

      runInAction(() => {
        // 获取用户钱包
        Object.assign(this, res.data);


        // 持久化状态
        // this._persistState();
      });

      return res.data;
    } catch (error) {
      runInAction(() => {
        this.lastError = error;
      });
      return null;
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  },


  clearProfile() {
    runInAction(() => {
      this.id = "";
      this.nickname = "";
      this.avatar = "";
      this.phone = "";
      this.gender = "";
      this.birthday = "";
      this.vipNo = "";
      this.lastUpdated = new Date();

      // 持久化状态
      this._persistState();
    });
  },

  // 私有方法，用于持久化状态
  _persistState() {
    const stateToSave = {
      id: this.id,
      nickname: this.nickname,
      avatar: this.avatar,
      phone: this.phone,
      gender: this.gender,
      birthday: this.birthday,
      vipNo: this.vipNo,
      lastUpdated: this.lastUpdated,
    };

    persistState(STORAGE_KEY, stateToSave);
  },
});
