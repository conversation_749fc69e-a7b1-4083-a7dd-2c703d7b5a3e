/**
 * 用户偏好状态
 * 管理用户偏好设置相关的状态
 */
import { observable, runInAction } from "mobx-miniprogram-lite";
import { persistState, loadPersistedState } from "../../utils/persistence";

const STORAGE_KEY = "user_preferences";

export const preferencesState = observable({
  // 从持久化存储加载初始状态
  ...loadPersistedState(STORAGE_KEY, {
    theme: "light", // 主题偏好：light或dark
    notifications: true, // 是否接收通知
    language: "zh_CN", // 语言偏好
    fontScale: 1, // 字体缩放比例
    lastUpdated: null,
  }),

  // 计算属性
  get isDarkMode() {
    return this.theme === "dark";
  },

  get displayLanguage() {
    const languages = {
      zh_CN: "简体中文",
      en_US: "English",
    };
    return languages[this.language] || "简体中文";
  },

  // 操作方法
  updateTheme(theme) {
    runInAction(() => {
      this.theme = theme;
      this.lastUpdated = new Date();
      this._persistState();
    });
  },

  updateNotifications(enabled) {
    runInAction(() => {
      this.notifications = enabled;
      this.lastUpdated = new Date();
      this._persistState();
    });
  },

  updateLanguage(language) {
    runInAction(() => {
      this.language = language;
      this.lastUpdated = new Date();
      this._persistState();
    });
  },

  updateFontScale(scale) {
    runInAction(() => {
      this.fontScale = scale;
      this.lastUpdated = new Date();
      this._persistState();
    });
  },

  resetPreferences() {
    runInAction(() => {
      this.theme = "light";
      this.notifications = true;
      this.language = "zh_CN";
      this.fontScale = 1;
      this.lastUpdated = new Date();
      this._persistState();
    });
  },

  // 私有方法，用于持久化状态
  _persistState() {
    const stateToSave = {
      theme: this.theme,
      notifications: this.notifications,
      language: this.language,
      fontScale: this.fontScale,
      lastUpdated: this.lastUpdated,
    };

    persistState(STORAGE_KEY, stateToSave);
  },
});
