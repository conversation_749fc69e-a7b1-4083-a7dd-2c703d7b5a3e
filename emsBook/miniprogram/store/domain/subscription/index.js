/**
 * 订阅计划状态模块
 * 管理订阅计划相关的状态
 */

import {
  observable,
  computed,
  action,
  runInAction,
} from "mobx-miniprogram-lite";
import { getSubscriptionPlanList } from "../../../utils/api";
import { persistState, loadPersistedState } from "../../utils/persistence";

const STORAGE_KEY = "subscription_plan";

// 状态定义
export const subscriptionState = observable({
  // 从持久化存储加载初始状态
  ...loadPersistedState(STORAGE_KEY, {
    list: [], // 订阅计划列表
    total: 0, // 订阅计划总数
    currentPlan: null, // 当前选中的计划
    userSubscriptions: [], // 用户已订阅的计划
    isLoading: false, // 加载状态
    lastError: null, // 最后一次错误
    lastUpdated: null, // 最后更新时间
  }),

  // 计算属性
  get hasPlans() {
    return this.list.length > 0;
  },

  get hasUserSubscriptions() {
    return this.userSubscriptions.length > 0;
  },

  get isSubscribed() {
    return this.userSubscriptions.length > 0;
  },

  // 内部方法
  _persistState() {
    persistState(STORAGE_KEY, {
      list: this.list,
      total: this.total,
      currentPlan: this.currentPlan,
      userSubscriptions: this.userSubscriptions,
      lastUpdated: this.lastUpdated,
    });
  },

  // 操作方法
  async fetchPlans(query = {}) {
    this.isLoading = true;
    this.lastError = null;

    try {
      const res = await getSubscriptionPlanList({ query });

      runInAction(() => {
        this.list = res.data.items || [];
        this.total = res.data.total || 0;
        this.lastUpdated = new Date();
        this._persistState();
      });

      return this.list;
    } catch (error) {
      runInAction(() => {
        this.lastError = error;
      });
      return [];
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  },

  /**
   * 获取用户订阅
   */
  async fetchUserSubscriptions() {
    this.isLoading = true;
    this.lastError = null;

    try {
      // 假设有一个获取用户订阅的API
      const res = await getSubscriptionPlanList({ query: { type: "user" } });

      runInAction(() => {
        this.userSubscriptions = res.data.items || [];
        this.lastUpdated = new Date();
        this._persistState();
      });

      return this.userSubscriptions;
    } catch (error) {
      runInAction(() => {
        this.lastError = error;
      });
      return [];
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  },

  /**
   * 设置当前计划
   * @param {string|null} planId - 计划ID
   */
  setCurrentPlan(planId) {
    const plan = this.list.find((p) => p.id === planId) || null;

    runInAction(() => {
      this.currentPlan = plan;
      this._persistState();
    });

    return plan;
  },

  /**
   * 订阅计划
   * @param {string} planId - 计划ID
   */
  async subscribePlan(planId) {
    if (!planId) {
      console.error("subscribePlan: planId is required");
      return false;
    }

    this.isLoading = true;
    this.lastError = null;

    try {
      // 假设有一个订阅计划的API
      const res = await getSubscriptionPlanList({
        query: { type: "subscribe", planId },
      });

      // 更新用户订阅
      await this.fetchUserSubscriptions();
      return res.data.success;
    } catch (error) {
      runInAction(() => {
        this.lastError = error;
      });
      return false;
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  },

  /**
   * 取消订阅
   * @param {string} subscriptionId - 订阅ID
   */
  async cancelSubscription(subscriptionId) {
    if (!subscriptionId) {
      console.error("cancelSubscription: subscriptionId is required");
      return false;
    }

    this.isLoading = true;
    this.lastError = null;

    try {
      // 假设有一个取消订阅的API
      const res = await getSubscriptionPlanList({
        query: { type: "cancel", subscriptionId },
      });

      // 更新用户订阅
      await this.fetchUserSubscriptions();
      return res.data.success;
    } catch (error) {
      runInAction(() => {
        this.lastError = error;
      });
      return false;
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  },

  // 清除订阅数据
  clearSubscriptionData() {
    runInAction(() => {
      this.list = [];
      this.total = 0;
      this.currentPlan = null;
      this.userSubscriptions = [];
      this.lastUpdated = new Date();
      this._persistState();
    });
  },
});
