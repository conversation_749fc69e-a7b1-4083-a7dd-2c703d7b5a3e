/**
 * 根状态
 * 导出所有状态模块，提供统一的状态访问入口
 */

// 导入核心状态
import { appState, appConfigStore } from "./core/app";
import { authState } from "./core/auth";
import { uiState } from "./core/ui";

// 导入领域状态
import { userState } from "./domain/user/index";
import { invoiceState } from "./domain/invoice/index";
import { paymentState } from "./domain/payment/index";
import { socialState } from "./domain/social/index";
import { postsState } from "./domain/posts/index";
import { subscriptionState } from "./domain/subscription/index";
import { campaignState } from "./domain/campaign/index";
import { shopState } from "./domain/shop/index";
import { productState } from "./domain/product/index";

// 导出所有状态模块
export const rootState = {
  // 核心状态
  app: appState,
  auth: authState,
  ui: uiState,

  // 领域状态
  user: userState,
  invoice: invoiceState,
  payment: paymentState,
  social: socialState,
  posts: postsState,
  subscription: subscriptionState,
  campaign: campaignState,
  shop: shopState,
  product: productState
};
