import { observable, runInAction } from "mobx-miniprogram-lite";
import { appConfig } from "../../utils/api";
import { persistState, loadPersistedState } from "../utils/persistence";

/**
 * 应用全局状态
 * 管理应用级别的全局配置和状态
 */
const STORAGE_KEY = "app_state";

export const appState = observable({
  // 从持久化存储加载初始状态
  ...loadPersistedState(STORAGE_KEY, {
    config: {},
    isInitialized: false,
    appVersion: "1.0.0",
    systemInfo: {},
    lastError: null,
    lastUpdated: null,
  }),

  // 计算属性
  get isReady() {
    return this.isInitialized && Object.keys(this.config).length > 0;
  },

  // 操作方法
  async initialize() {
    try {
      // 获取系统信息
      const sysInfo = wx.getSystemInfoSync();
      runInAction(() => {
        this.systemInfo = sysInfo;
      });

      // 加载应用配置
      await this.refreshConfig();

      runInAction(() => {
        this.isInitialized = true;
        this.lastUpdated = new Date();
        this._persistState();
      });

      return true;
    } catch (error) {
      runInAction(() => {
        this.lastError = error;
      });
      return false;
    }
  },

  async refreshConfig() {
    try {
      const res = await appConfig();
      runInAction(() => {
        this.config = JSON.parse(res.data);
        this.lastUpdated = new Date();
        this._persistState();
      });
      return this.config;
    } catch (error) {
      runInAction(() => {
        this.lastError = error;
      });
      return {};
    }
  },

  // 私有方法，用于持久化状态
  _persistState() {
    const stateToSave = {
      config: this.config,
      isInitialized: this.isInitialized,
      appVersion: this.appVersion,
      lastUpdated: this.lastUpdated,
    };

    persistState(STORAGE_KEY, stateToSave);
  },
});
