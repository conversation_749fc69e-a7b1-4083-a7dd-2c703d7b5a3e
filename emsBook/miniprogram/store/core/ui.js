import { observable, runInAction } from "mobx-miniprogram-lite";
import { persistState, loadPersistedState } from "../utils/persistence";
import { debounce } from "../utils/helpers";

/**
 * UI全局状态
 * 管理全局UI相关的状态，如主题、加载状态、全局弹窗等
 */
const STORAGE_KEY = "ui_state";

export const uiState = observable({
  // 从持久化存储加载初始状态
  ...loadPersistedState(STORAGE_KEY, {
    theme: "light", // 主题模式：light或dark
    isLoading: false, // 全局加载状态
    toasts: [], // 全局提示消息队列
    modalVisible: false, // 全局模态框可见性
    modalConfig: {}, // 全局模态框配置
    navbarHeight: 0, // 导航栏高度
    statusBarHeight: 0, // 状态栏高度
    screenWidth: 0, // 屏幕宽度
    screenHeight: 0, // 屏幕高度
    lastUpdated: null,
  }),

  // 计算属性
  get isDarkMode() {
    return this.theme === "dark";
  },

  get hasToasts() {
    return this.toasts.length > 0;
  },

  // 操作方法
  initialize() {
    // 获取系统信息设置UI尺寸
    try {
      const systemInfo = wx.getSystemInfoSync();
      runInAction(() => {
        this.statusBarHeight = systemInfo.statusBarHeight;
        this.screenWidth = systemInfo.windowWidth;
        this.screenHeight = systemInfo.windowHeight;

        // 根据系统主题设置应用主题
        if (systemInfo.theme) {
          this.theme = systemInfo.theme;
        }

        this.lastUpdated = new Date();
        this._persistState();
      });
    } catch (error) {
      console.error("获取系统信息失败", error);
    }

    // 计算导航栏高度
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
    runInAction(() => {
      this.navbarHeight =
        (menuButtonInfo.top - this.statusBarHeight) * 2 +
        menuButtonInfo.height +
        this.statusBarHeight;
      this.lastUpdated = new Date();
      this._persistState();
    });
  },

  setTheme(theme) {
    if (theme === "light" || theme === "dark") {
      this.theme = theme;
      this.lastUpdated = new Date();
      this._persistState();
    }
  },

  showLoading() {
    this.isLoading = true;
    this.lastUpdated = new Date();
  },

  hideLoading() {
    this.isLoading = false;
    this.lastUpdated = new Date();
  },

  setLoading(isLoading) {
    runInAction(() => {
      this.isLoading = isLoading;
      this.lastUpdated = new Date();
    });
  },

  showToast(message, type = "info", duration = 2000) {
    const id = Date.now();
    const toast = {
      id,
      message,
      type,
      duration,
    };

    this.toasts.push(toast);
    this.lastUpdated = new Date();

    // 自动移除toast
    setTimeout(() => {
      runInAction(() => {
        this.toasts = this.toasts.filter((item) => item.id !== id);
        this.lastUpdated = new Date();
      });
    }, duration);

    return id;
  },

  removeToast(id) {
    this.toasts = this.toasts.filter((item) => item.id !== id);
    this.lastUpdated = new Date();
  },

  showModal(config) {
    this.modalConfig = config;
    this.modalVisible = true;
    this.lastUpdated = new Date();
    this._persistState();
  },

  hideModal() {
    this.modalVisible = false;
    this.lastUpdated = new Date();
    this._persistState();
  },

  // 防抖处理示例
  debouncedSetTheme: debounce(function (theme) {
    this.setTheme(theme);
  }, 300),

  // 私有方法，用于持久化状态
  _persistState() {
    const stateToSave = {
      theme: this.theme,
      navbarHeight: this.navbarHeight,
      statusBarHeight: this.statusBarHeight,
      screenWidth: this.screenWidth,
      screenHeight: this.screenHeight,
      lastUpdated: this.lastUpdated,
    };

    persistState(STORAGE_KEY, stateToSave);
  },
});
