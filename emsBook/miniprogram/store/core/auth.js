import { observable, runInAction } from "mobx-miniprogram-lite";
import { wxlogin } from "../../utils/api";
import { persistState, loadPersistedState } from "../utils/persistence";

/**
 * 认证状态
 * 管理用户认证相关的状态，如登录状态、token等
 */
const STORAGE_KEY = "auth_state";

export const authState = observable({
  // 从持久化存储加载初始状态
  ...loadPersistedState(STORAGE_KEY, {
    hasLogin: false,
    hasBindPhone: false,
    token: "",
    lastLoginTime: null,
    lastError: null,
  }),

  // 计算属性
  get isAuthenticated() {
    return this.hasLogin && !!this.token;
  },

  get needBindPhone() {
    return this.hasLogin && !this.hasBindPhone;
  },

  // 操作方法
  mpLogin(isFirst = false, getUserInfo = false) {
    wx.login({
      success: (res) => {
        wxlogin({
          method: "post",
          data: {
            code: res.code,
            avatarUrl: null,
            fromCode: getApp().globalData.shareuser,
            nickname: null,
            phone: null,
          },
        }).then(async (res) => {
          if (res.resultCode == "200") {
            this.login(res.data);
            if (getUserInfo) {
              if (res.data.userInfo.id || res.data.userInfo.nickname) {
                wx.navigateBack({
                  delta: 1,
                });
                return;
              }
              wx.showModal({
                title: "完善头像和昵称",
                content: "提供更好的个性化服务",
                confirmText: "去完善",
                cancelText: "暂不",
                success({ confirm, cancel }) {
                  if (confirm) {
                    wx.navigateTo({
                      url: "/pages/common/profile",
                    });
                  } else {
                    wx.navigateBack({
                      delta: 1,
                    });
                  }
                },
              });
              return;
            }
            if (!isFirst) {
              wx.navigateBack({
                delta: 1,
              });
            }
          } else {
            wx.showToast({
              title: "服务器开小差,联系客服",
              icon: "none",
              duration: 2000,
            });
          }
        });
      },
      fail: (err) => {
        wx.showToast({
          title: "服务器开小差,联系客服",
          icon: "none",
          duration: 2000,
        });
      },
    });
  },

  login(provider) {
    runInAction(() => {
      this.hasLogin = !!provider.userInfo.id;
      this.hasBindPhone = !!provider.userInfo.phone;
      this.token = provider.tokenData.accessToken;
      this.lastLoginTime = new Date();
      this.lastError = null;

      // 持久化认证状态
      this._persistState();
    });
  },

  logout() {
    runInAction(() => {
      this.hasLogin = false;
      this.hasBindPhone = false;
      this.token = "";
      this.lastLoginTime = null;

      // 清除存储的认证信息
      wx.removeStorageSync("token");

      // 持久化认证状态
      this._persistState();
    });
  },

  setToken(token) {
    if (!token) return;

    runInAction(() => {
      this.token = token;
      this.hasLogin = true;

      // 存储token
      // wx.setStorageSync("token", token);

      // 持久化认证状态
      this._persistState();
    });
  },

  async bindPhone(phoneData) {
    try {
      // 这里应该有绑定手机号的API调用
      // const res = await bindPhoneAPI(phoneData);

      runInAction(() => {
        this.hasBindPhone = true;

        // 持久化认证状态
        this._persistState();
      });

      return true;
    } catch (error) {
      runInAction(() => {
        this.lastError = error;
      });
      return false;
    }
  },

  // 私有方法，用于持久化状态
  _persistState() {
    const stateToSave = {
      hasLogin: this.hasLogin,
      hasBindPhone: this.hasBindPhone,
      token: this.token,
      lastLoginTime: this.lastLoginTime,
    };

    persistState(STORAGE_KEY, stateToSave);
  },
});
