import { wxRequest, wxpay, wxUpload } from "./wxRequest.js";
import { mpConfig } from "./config.js";

//支付
export function pay(result) {
  return wxpay(result);
}

// ================ 微信认证相关接口 ================

// 基础用户登录
export function wxlogin(params = {}) {
  return wxRequest(
    {
      ...params,
    },
    `wechat/login`,
    false,
    "账号登录中"
  );
}

// 微信账号绑定
export function wxbind(params = {}) {
  return wxRequest(
    {
      ...params,
    },
    `wechat/bind`,
    false,
    "账号绑定中"
  );
}

// 获取微信用户信息
export function wxGetUserInfo(params = {}) {
  return wxRequest(
    {
      ...params,
    },
    `wechat/userinfo`,
    true,
    "获取用户信息"
  );
}

export function wxGetUserRoles(params = {}) {
  return wxRequest(
    {
      ...params,
    },
    `roles`,
    true,
    "获取用户角色"
  );
}

// 更新微信用户信息
export function wxUpdateUserInfo(params = {}) {
  return wxRequest(
    {
      method: "put",
      ...params,
    },
    `wechat/userinfo`,
    true,
    "更新用户信息"
  );
}

// ================ 支付相关接口 ================

// 获取支付列表
export function getPaymentList(params = {}) {
  return wxRequest(
    {
      ...params,
    },
    `payments`,
    true,
    "获取支付列表"
  );
}

// 获取支付详情
export function getPaymentDetail(params = { paymentId }) {
  return wxRequest({}, `payments/${paymentId}`, true, "获取支付详情");
}

// 创建支付
export function createPayment(params = {}) {
  return wxRequest(
    {
      method: "POST",
      ...params,
    },
    `payments`,
    true,
    "创建支付"
  );
}

// 申请退款
export function refundPayment(params = { paymentId }) {
  return wxRequest(
    {},
    `payments/${paymentId}/refund`,
    true,
    "申请退款",
    "POST"
  );
}

// ================ 账单相关接口 ================

// 获取账单列表
export function getInvoiceList(params = {}) {
  return wxRequest(
    {
      ...params,
    },
    `invoices/mine`,
    true,
    "获取账单列表"
  );
}

// 获取账单详情
export function getInvoiceDetail(params = { invoiceId }) {
  return wxRequest({}, `invoices/${invoiceId}`, true, "获取账单详情");
}

// 创建账单
export function createInvoice(params = {}) {
  return wxRequest(
    {
      method: "post",
      ...params,
    },
    `invoices`,
    true,
    "创建账单"
  );
}

// ================ 订阅相关接口 ================

// 获取订阅计划列表
export function getSubscriptionPlanList(params = {}) {
  return wxRequest(
    {
      ...params,
    },
    `subscription_plans/list`,
    true,
    "获取订阅计划列表"
  );
}

// 获取订阅计划详情
export function getSubscriptionPlanDetail(parms = { planId }) {
  return wxRequest(
    {},
    `subscription-plans/${planId}`,
    true,
    "获取订阅计划详情"
  );
}

// 获取用户订阅列表
export function getUserSubscriptions(params = {}) {
  return wxRequest(
    {
      ...params,
    },
    `subscriptions`,
    true,
    "获取订阅列表"
  );
}

// 创建订阅
export function createSubscription(params = {}) {
  return wxRequest(
    {
      method: "post",
      ...params,
    },
    `subscriptions`,
    true,
    "创建订阅"
  );
}

// 取消订阅
export function cancelSubscription(params = { subscriptionId }) {
  return wxRequest(
    {
      method: "post",
    },
    `subscriptions/${subscriptionId}/cancel`,
    true,
    "取消订阅"
  );
}

/**
 * 提交订单评价
 * @param {Object} reviewData - 评价数据
 * @returns {Promise} - 返回评价结果
 */
export function submitReview(reviewData) {
  return wxRequest({
    url: "/api/reviews",
    method: "POST",
    data: reviewData,
  });
}

// ================ 营销活动相关接口 ================

// 获取活动列表
export function getCampaignList(params = {}) {
  return wxRequest(
    {
      ...params,
    },
    `campaign/active`,
    true,
    "获取活动列表"
  );
}

// 获取活动详情
export function getCampaignDetail(params = { campaignId }) {
  return wxRequest(
    {},
    `campaign/details/${params.campaignId}`,
    true,
    "获取活动详情"
  );
}

// 参与活动
export function joinCampaign(params = {}) {
  return wxRequest(
    {
      method: "POST",
      ...params,
    },
    `campaign/join`,
    true,
    "参与活动中"
  );
}

// 获取活动参与者列表
export function getParticipants(params = { campaignId }) {
  return wxRequest(
    {},
    `campaigns/${params.campaignId}/participants`,
    true,
    "获取参与者列表"
  );
}

// 获取奖励统计
export function getRewardStats(params) {
  return wxRequest({}, `reward/stats`);
}

export function getShopList(params = {}) {
  return wxRequest(
    {
      ...params,
    },
    `shop/list`,
    true,
    "获取商店列表"
  );
}

export function getShopDetail(params = { shopId }) {
  return wxRequest({}, `shop/${params.shopId}`, true, "获取商店详情");
}

// ================ 产品相关接口 ================

// 获取产品列表
export function getProductList(params = {}) {
  return wxRequest(
    {
      ...params,
    },
    `product/list`,
    true,
    "获取产品列表"
  );
}

// 获取推荐产品列表
export function getRecommendProductList(params = {}) {
  return wxRequest(
    {
      ...params,
    },
    `product/recommended`,
    true,
    "获取推荐产品列表"
  );
}

// 获取产品详情
export function getProductDetail(params = { productId }) {
  return wxRequest({}, `product/${params.productId}`, true, "获取产品详情");
}

// 获取分类列表
export function getCategoryList(params = {}) {
  return wxRequest(
    {
      ...params,
    },
    `category/list`,
    true,
    "获取分类列表"
  );
}

// 获取产品规格列表
export function getProductSpecList(params = { productId }) {
  return wxRequest(
    {},
    `product/${params.productId}/specs`,
    true,
    "获取产品规格列表"
  );
}
