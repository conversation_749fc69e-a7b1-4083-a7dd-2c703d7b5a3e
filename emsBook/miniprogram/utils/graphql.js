import { graphqlUrl } from "./config.js";
import { wxRequest } from "./wxRequest.js"; // Assuming we might reuse some logic or structure

/**
 * 发送 GraphQL 请求
 * @param {string} query - GraphQL 查询语句
 * @param {object} [variables={}] - GraphQL 查询变量
 * @param {string} [operationName=null] - GraphQL 操作名称 (可选)
 * @param {object} [options={}] - wx.request 的其他配置 (可选)
 * @returns {Promise} - 返回一个 Promise 对象，包含请求结果或错误信息
 */
export function requestGraphQL(
  query,
  variables = {},
  operationName = null,
  options = {}
) {
  const graphqlEndpoint = `${baseUrl}/graphql`; // 假设 GraphQL 端点

  const requestData = {
    query,
    variables,
  };

  if (operationName) {
    requestData.operationName = operationName;
  }

  // 构造 wxRequest 所需的参数
  // 我们需要决定是直接使用 wx.request 还是通过 wxRequest 封装
  // 考虑到 wxRequest 已经处理了通用逻辑（如header、loading、错误处理），复用它会更好
  // 但 wxRequest 的第二个参数是 path，第三个是 isShowLoading，第四个是 loadingText
  // 我们需要适配 GraphQL 的请求方式

  // 方案1: 直接使用 wx.request，并自行处理通用逻辑 (可能重复 wxRequest 的部分功能)
  // 方案2: 修改 wxRequest 或创建一个新的通用请求函数来更好地支持 GraphQL
  // 方案3: 尝试将 GraphQL 请求适配到现有 wxRequest 的结构中

  // 暂时采用方案3的思路，将 GraphQL 请求视为一种特殊的 POST 请求
  // wxRequest 的 path 参数可以设为 'graphql' (相对于 baseUrl)
  // wxRequest 的 data 参数就是我们构造的 requestData

  const wxRequestOptions = {
    method: "POST",
    data: requestData,
    ...options, // 允许覆盖或添加额外的 wx.request 选项
  };

  // 第三个参数 isShowLoading，第四个参数 loadingText 可以根据需要设置，或从 options 传入
  const isShowLoading =
    options.isShowLoading !== undefined ? options.isShowLoading : true;
  const loadingText = options.loadingText || "加载中...";

  return wxRequest(
    wxRequestOptions,
    "graphql", // 这里的 'graphql' 会被拼接到 baseUrl 后面
    isShowLoading,
    loadingText
  )
    .then((response) => {
      // GraphQL 响应通常包含 data 和 errors 字段
      // 如果 errors 字段存在，即使 HTTP 状态码是 200，也表示请求中存在业务错误
      if (response && response.errors && response.errors.length > 0) {
        // 提取第一个错误信息进行提示
        const firstError = response.errors[0];
        const errorMessage = firstError.message || "GraphQL 请求失败";
        wx.showToast({
          title: errorMessage,
          icon: "none",
          duration: 2500,
        });
        return Promise.reject(response.errors); // 抛出完整的 errors 数组
      }
      // 如果没有 errors 字段，或者 errors 为空数组，则认为请求成功，返回 data 部分
      return response.data;
    })
    .catch((error) => {
      // 这里可以进一步处理 wxRequest 本身抛出的网络错误或其他错误
      // 或者直接将错误继续抛出，由调用方处理
      return Promise.reject(error);
    });
}
