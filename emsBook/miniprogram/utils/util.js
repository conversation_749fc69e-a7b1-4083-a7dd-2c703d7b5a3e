export const formatTime = (date) => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  const second = date.getSeconds();

  return (
    [year, month, day].map(formatNumber).join("/") +
    " " +
    [hour, minute, second].map(formatNumber).join(":")
  );
};

const formatNumber = (n) => {
  const s = n.toString();
  return s[1] ? s : "0" + s;
};

export function urlParse(url) {
  const newOpt = decodeURIComponent(url).split("&");
  if (newOpt[0] === "undefined") {
    return false;
  }
  return newOpt
    .map((v) => {
      const obj = v.split("=");
      if (obj[0] !== "undefined") {
        return {
          [obj[0]]: obj[1],
        };
      }
    })
    .reduce(
      (prev, cur) => {
        return Object.assign(prev, cur);
      },
      {
        refId: 0,
        code: 0,
      }
    );
}
