import { baseUrl } from "./config.js";

// 网络状态常量
const NETWORK_TYPE = {
  WIFI: "wifi",
  CELLULAR: "2g/3g/4g/5g",
  UNKNOWN: "unknown",
  NONE: "none",
};

// 请求拦截器
const requestInterceptors = [];

// 响应拦截器
const responseInterceptors = [];

// 错误码映射表
const ERROR_CODE_MAP = {
  // 需要跳转登录页面的错误码
  AUTH_ERROR: [30009, 10008, 401, 403],
  // 普通提示错误
  NORMAL_ERROR: ["30"],
  // 业务错误
  BUSINESS_ERROR: [10000],
  // 系统限流错误
  RATE_LIMIT_ERROR: [20010, 429],
  // 需要特殊处理的错误
  SPECIAL_ERROR: [30004, 30008],
  // 网络错误
  NETWORK_ERROR: [
    -1, -2, -6, -7, -9, -10, -11, -12, -14, -15, -16, -100, -101, -102,
  ],
  // 超时错误
  TIMEOUT_ERROR: [-7],
};

// 请求队列，用于防止重复请求
const pendingRequests = new Map();

// 生成请求的唯一key
const generateRequestKey = (config) => {
  const { url, method, data } = config;
  return `${method}:${url}:${JSON.stringify(data)}`;
};

// 添加请求到队列
const addPendingRequest = (config) => {
  const requestKey = generateRequestKey(config);
  config._requestKey = requestKey;

  if (!pendingRequests.has(requestKey)) {
    // 创建取消令牌
    const abort = () => {
      removePendingRequest(config);
      return new Error("请求已取消");
    };

    pendingRequests.set(requestKey, abort);
  }

  return config;
};

// 从队列中移除请求
const removePendingRequest = (config) => {
  const requestKey = config._requestKey;
  if (requestKey && pendingRequests.has(requestKey)) {
    pendingRequests.delete(requestKey);
  }
  return config;
};

// 取消重复的请求
const cancelDuplicateRequest = (config) => {
  const requestKey = generateRequestKey(config);
  if (pendingRequests.has(requestKey)) {
    const abort = pendingRequests.get(requestKey);
    abort();
  }
  return config;
};

// 添加请求拦截器
const addRequestInterceptor = (interceptor) => {
  requestInterceptors.push(interceptor);
  return () => {
    const index = requestInterceptors.indexOf(interceptor);
    if (index !== -1) requestInterceptors.splice(index, 1);
  };
};

// 添加响应拦截器
const addResponseInterceptor = (interceptor) => {
  responseInterceptors.push(interceptor);
  return () => {
    const index = responseInterceptors.indexOf(interceptor);
    if (index !== -1) responseInterceptors.splice(index, 1);
  };
};

// 处理请求配置
const processRequestConfig = async (config) => {
  let currentConfig = { ...config };

  // 应用请求拦截器
  for (const interceptor of requestInterceptors) {
    try {
      currentConfig = await interceptor(currentConfig);
    } catch (error) {
      return Promise.reject(error);
    }
  }

  return currentConfig;
};

// 处理响应数据
const processResponseData = async (response, config) => {
  let currentResponse = { ...response };

  // 应用响应拦截器
  for (const interceptor of responseInterceptors) {
    try {
      currentResponse = await interceptor(currentResponse, config);
    } catch (error) {
      return Promise.reject(error);
    }
  }

  return currentResponse;
};

// 处理错误响应
const handleErrorResponse = (res, reject) => {
  // 处理认证错误
  if (ERROR_CODE_MAP.AUTH_ERROR.includes(res.data.resultCode)) {
    wx.navigateTo({
      url: "/pages/common/login",
    });
    reject(res.data);
    return true;
  }

  // 处理普通错误
  if (`${res.data.resultCode}`.includes(ERROR_CODE_MAP.NORMAL_ERROR[0])) {
    wx.showToast({
      title: res.data.resultMsg,
      icon: "none",
      duration: 2500,
    });
    reject(res.data);
    return true;
  }

  // 处理业务错误
  if (ERROR_CODE_MAP.BUSINESS_ERROR.includes(res.data.resultCode)) {
    wx.showToast({
      title: res.data.resultMsg,
      icon: "none",
      duration: 2500,
    });
    reject(res.data);
    return true;
  }

  // 处理限流错误
  if (ERROR_CODE_MAP.RATE_LIMIT_ERROR.includes(res.data.resultCode)) {
    wx.showToast({
      title: "累了,需要休息了,稍后再聊",
      icon: "none",
      duration: 2500,
    });
    reject(res.data);
    return true;
  }

  // 处理特殊错误
  if (ERROR_CODE_MAP.SPECIAL_ERROR.includes(res.data.resultCode)) {
    wx.showModal({
      cancelText: "取消",
      confirmText: "去获取",
      title: res.data.resultMsg,
      content: "如有疑问,请联系客服YOLO_Style",
      showCancel: false,
      success({ confirm, cancel }) {
        if (confirm) {
          wx.switchTab({
            url: "/pages/ucenter/ucenter",
          });
        }
      },
      complete() {
        wx.setClipboardData({
          data: "YOLO_Style",
        });
      },
    });
    reject(res.data);
    return true;
  }

  return false;
};

// 获取存储的token
const getToken = () => {
  try {
    const authState = wx.getStorageSync("auth_state");
    if (authState) {
      const parsedAuthState = JSON.parse(authState);

      // 检查token是否过期
      // 使用lastLoginTime和expiresIn来判断token是否过期
      if (
        parsedAuthState.lastLogin &&
        parsedAuthState.tokenData &&
        parsedAuthState.tokenData.expiresIn
      ) {
        const lastLoginTime = new Date(parsedAuthState.lastLogin).getTime();
        const expiresIn = parsedAuthState.tokenData.expiresIn * 1000; // 转换为毫秒
        const currentTime = new Date().getTime();

        // 如果当前时间超过了登录时间+有效期，则token过期
        if (currentTime > lastLoginTime + expiresIn) {
          console.warn("Token已过期，需要重新登录");
          // 清除过期token
          wx.removeStorageSync("auth_state");
          // 跳转到登录页
          setTimeout(() => {
            wx.navigateTo({
              url: "/pages/common/login",
            });
          }, 0);
          return "";
        }
      }

      return parsedAuthState.token || "";
    }
  } catch (error) {
    console.error("获取token失败:", error);
  }
  return "";
};

// 检查网络状态
const checkNetworkStatus = () => {
  return new Promise((resolve) => {
    wx.getNetworkType({
      success: (res) => {
        resolve({
          isConnected: res.networkType !== NETWORK_TYPE.NONE,
          networkType: res.networkType,
        });
      },
      fail: () => {
        resolve({
          isConnected: false,
          networkType: NETWORK_TYPE.UNKNOWN,
        });
      },
    });
  });
};

// 监听网络状态变化
const listenNetworkStatus = () => {
  wx.onNetworkStatusChange((res) => {
    console.log(
      `网络状态变化: ${res.isConnected ? "已连接" : "已断开"}, 类型: ${
        res.networkType
      }`
    );
    // 可以在这里处理网络状态变化的逻辑
    if (!res.isConnected) {
      wx.showToast({
        title: "网络已断开，请检查网络设置",
        icon: "none",
        duration: 2000,
      });
    }
  });
};

// 初始化网络监听
listenNetworkStatus();

// 请求重试函数
const retryRequest = async (config, retries = 3, delay = 1000) => {
  try {
    // 检查网络状态
    const networkStatus = await checkNetworkStatus();
    if (!networkStatus.isConnected) {
      wx.showToast({
        title: "网络连接已断开，请检查网络设置",
        icon: "none",
        duration: 2000,
      });
      return Promise.reject(new Error("网络连接已断开"));
    }

    return await wxRequest(
      config,
      config.url,
      config.showLoading,
      config.loadingText
    );
  } catch (error) {
    if (retries <= 0) {
      return Promise.reject(error);
    }

    // 延迟一段时间后重试
    await new Promise((resolve) => setTimeout(resolve, delay));

    console.log(`请求失败，正在重试，剩余重试次数: ${retries - 1}`);
    return retryRequest(config, retries - 1, delay * 2);
  }
};

/**
 * 网络请求封装
 * @param {Object} params - 请求参数
 * @param {string} url - 请求地址
 * @param {boolean} showLoading - 是否显示加载提示
 * @param {string} loadingText - 加载提示文本
 * @param {number} retryCount - 重试次数
 * @param {boolean} checkNetwork - 是否检查网络状态
 * @returns {Promise} - 请求结果
 */
const wxRequest = async (
  params = {},
  url,
  showLoading = true,
  loadingText = "数据加载中",
  retryCount = 0,
  checkNetwork = true
) => {
  // 如果需要检查网络状态
  if (checkNetwork) {
    const networkStatus = await checkNetworkStatus();
    if (!networkStatus.isConnected) {
      wx.showToast({
        title: "网络连接已断开，请检查网络设置",
        icon: "none",
        duration: 2000,
      });
      return Promise.reject(new Error("网络连接已断开"));
    }
  }

  // 构建请求配置
  const requestConfig = {
    url,
    method: params.method || "GET",
    data: params.query || params.data || {},
    showLoading,
    loadingText,
    retryCount,
    checkNetwork,
  };

  // 取消重复请求
  cancelDuplicateRequest(requestConfig);

  // 添加到请求队列
  addPendingRequest(requestConfig);

  // 处理请求配置
  const processedConfig = await processRequestConfig(requestConfig);

  // 获取token
  const token = getToken();

  // 设置请求头
  let header = {
    "Content-Type": "application/json;charset=UTF-8",
    Authorization: "Bearer " + token,
  };

  // 如果拦截器修改了header，使用拦截器的结果
  if (processedConfig.header) {
    header = { ...header, ...processedConfig.header };
  }

  let res = await new Promise((resolve, reject) => {
    // 显示加载提示
    if (showLoading) {
      wx.showLoading({
        title: loadingText,
        mask: true,
        success: () => {},
      });
    }

    wx.request({
      url: /https/g.test(url) ? url : `${baseUrl}/${url}`,
      method: processedConfig.method,
      data: processedConfig.data,
      header,
      responseType: params.rspType || "text",
      dataType: params.dataType || "json",
      timeout: 30000,
      forceCellularNetwork: false,
      enableChunked: false,
      success: async (res) => {
        // 从请求队列中移除
        removePendingRequest(requestConfig);

        // 处理响应数据
        try {
          const processedResponse = await processResponseData(
            res,
            processedConfig
          );

          if (processedResponse.statusCode === 200) {
            // 处理错误响应
            const isErrorHandled = handleErrorResponse(
              processedResponse,
              reject
            );
            if (!isErrorHandled) {
              resolve(processedResponse.data);
            }
          } else {
            // 处理HTTP错误
            if ([401, 400].includes(processedResponse.statusCode)) {
              wx.navigateTo({
                url: "/pages/common/login",
              });
            }
            wx.showToast({
              title: "系统错误,稍后重试",
              icon: "none",
            });
            reject(processedResponse.data);
          }
        } catch (error) {
          reject(error);
        }
      },
      fail: (err) => {
        // 从请求队列中移除
        removePendingRequest(requestConfig);

        // 处理网络错误
        if (ERROR_CODE_MAP.AUTH_ERROR.includes(err.code)) {
          wx.navigateTo({
            url: "/pages/common/login",
          });
        } else if (ERROR_CODE_MAP.NETWORK_ERROR.includes(err.code)) {
          // 处理网络错误
          wx.showToast({
            title: "网络异常，请检查网络连接",
            icon: "none",
            duration: 2000,
          });
        } else if (ERROR_CODE_MAP.TIMEOUT_ERROR.includes(err.code)) {
          // 处理超时错误
          wx.showToast({
            title: "请求超时，请稍后重试",
            icon: "none",
            duration: 2000,
          });
        }

        // 如果设置了重试，尝试重试请求
        if (retryCount > 0) {
          console.log(`请求失败，准备重试，剩余重试次数: ${retryCount}`);
          wxRequest(
            params,
            url,
            showLoading,
            loadingText,
            retryCount - 1,
            false
          ) // 重试时不再检查网络状态
            .then(resolve)
            .catch(reject);
          return;
        }

        reject(err);
      },
      complete: () => {
        // 隐藏加载提示
        if (showLoading) {
          wx.hideLoading({
            fail: () => {},
          });
        }
      },
    });
  });

  return res;
};

/**
 * 文件上传封装
 * @param {Object} params - 上传参数
 * @param {string} url - 上传地址
 * @param {boolean} showProgress - 是否显示上传进度
 * @param {number} retryCount - 重试次数
 * @returns {Promise} - 上传结果
 */
const wxUpload = async (params, url, showProgress = true, retryCount = 0) => {
  // 检查网络状态
  const networkStatus = await checkNetworkStatus();
  if (!networkStatus.isConnected) {
    wx.showToast({
      title: "网络连接已断开，请检查网络设置",
      icon: "none",
      duration: 2000,
    });
    return Promise.reject(new Error("网络连接已断开"));
  }

  // 获取token
  const token = getToken();

  // 构建上传配置
  const uploadConfig = {
    url: `${baseUrl}/${url}`,
    name: params.name || "file",
    filePath: params.filePath,
    formData: params.formData || {},
    header: {
      Authorization: "Bearer " + token,
      ...params.header,
    },
  };

  return await new Promise((resolve, reject) => {
    const uploadTask = wx.uploadFile({
      ...uploadConfig,
      success: (res) => {
        // 尝试解析响应数据
        let responseData;
        try {
          responseData = JSON.parse(res.data);

          // 检查上传结果状态码
          if (responseData.resultCode && responseData.resultCode !== 0) {
            // 处理错误响应
            const isErrorHandled = handleErrorResponse(
              { data: responseData, statusCode: 200 },
              reject
            );
            if (!isErrorHandled) {
              wx.showToast({
                title: "上传成功",
                icon: "success",
              });
              resolve(responseData);
            }
            return;
          }
        } catch (e) {
          responseData = res.data;
        }

        wx.showToast({
          title: "上传成功",
          icon: "success",
        });
        resolve(responseData);
      },
      fail: (err) => {
        console.error("上传失败:", err);

        // 如果设置了重试，尝试重试上传
        if (retryCount > 0) {
          console.log(`上传失败，准备重试，剩余重试次数: ${retryCount}`);
          setTimeout(() => {
            wxUpload(params, url, showProgress, retryCount - 1)
              .then(resolve)
              .catch(reject);
          }, 1000);
          return;
        }

        wx.showToast({
          title: "上传失败，请重试",
          icon: "none",
        });
        reject(err);
      },
    });

    // 显示上传进度
    if (showProgress) {
      uploadTask.onProgressUpdate((res) => {
        if (params.onProgress && typeof params.onProgress === "function") {
          params.onProgress(res);
        } else {
          // 默认进度处理
          console.log("上传进度", res.progress);
          // 可以在这里实现默认的进度显示
        }
      });
    }
  });
};

/**
 * 微信支付封装
 * @param {Object} result - 支付参数
 * @returns {Promise} - 支付结果
 */
const wxpay = async (result) => {
  return await new Promise((resolve, reject) => {
    wx.requestPayment({
      provider: "wxpay",
      nonceStr: result.data.nonceStr,
      signType: "MD5",
      paySign: result.data.paySign,
      timeStamp: result.data.timeStamp,
      package: result.data.packageValue,
      success: (res) => {
        resolve(res);
      },
      fail: (err) => {
        reject(err);
      },
    });
  });
};

/**
 * 取消请求
 * @param {Object} config - 请求配置
 * @returns {boolean} - 是否成功取消
 */
const cancelRequest = (config) => {
  const requestKey = generateRequestKey(config);
  if (pendingRequests.has(requestKey)) {
    const abort = pendingRequests.get(requestKey);
    abort();
    return true;
  }
  return false;
};

/**
 * 获取当前网络状态
 * @returns {Promise} - 网络状态信息
 */
const getNetworkStatus = () => {
  return checkNetworkStatus();
};

export {
  wxRequest,
  wxpay,
  wxUpload,
  addRequestInterceptor,
  addResponseInterceptor,
  retryRequest,
  cancelRequest,
  getNetworkStatus,
  NETWORK_TYPE,
};
