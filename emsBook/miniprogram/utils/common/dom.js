class AsyncConstructor {
  constructor(asyncConstructor) {
    const init = (async () => {
      await asyncConstructor()
      delete this.then
      return this
    })()
    this.then = init.then.bind(init)
  }
}

export class Dom extends AsyncConstructor {
  constructor(args) {
    super(async () => {
      // 获取多个属性会影响渲染时间
      const node = await new Promise((resolve, reject) => {
        const ins = args.ins.createSelectorQuery()
        ins.select(`${args.node}`).fields({
          size: args?.opts?.size || true,
          node: args?.opts?.node || false,
          rect: args?.opts?.rect || true
        }, (res) => {
          resolve(res)
        }).exec()
      })
      this.dom = node
    })
    this.dpr = wx.getSystemInfoSync().pixelRatio
  }
  getRect() {
    const {
      top,
      bottom,
      left,
      right
    } = this.dom
    return {
      top,
      bottom,
      left,
      right
    }
  }
  width() {
    return this.dom.width
  }

  height() {
    return this.dom.height
  }
  getNode(width, height) {
    this.dom.node.width = width * this.dpr
    this.dom.node.height = height * this.dpr
    this.dom.node.getCtx = (type = '2d') => {
      let ctx = this.dom.node.getContext(type)
      switch (type) {
        case '2d':
          ctx.scale(this.dpr, this.dpr)
          break;
        case 'webgl':
          ctx = startGL(ctx, this.dom.node, this.dpr)
          break;
      }
      return ctx
    }
    return this.dom.node
  }

}