export const navTo = (url, routeType = 'wx://cupertino-modal-inside') => {
  if (!url) {
    wx.showToast({
      title: '暂未开放',
      icon: 'none'
    })
    return
  }
  wx.navigateTo({
    url,
    routeType
  })
}
export const navBack = (url = '/pages/index/index') => {
  const page = getCurrentPages()
  if (page.length > 1) {
    wx.navigateBack()
  } else {
    wx.switchTab({
      url
    })
  }
}

export const copyText = (text, desc = '') => {
  if (!text) return
  wx.setClipboardData({
    data: `${text}`,
    success: () => {
      wx.showToast({
        title: `复制${desc}成功`,
        icon: 'none'
      })
    }
  })
}

export const arrayRange = (start, stop, step) => Array.from(
  { length: (stop - start) / step + 1 },
  (value, index) => start + index * step
)

export const lightBlue = {
  0: '#E1F5FE',
  100: '#B3E5FC',
  200: '#81D4FA',
  300: '#4FC3F7',
  400: '#29B6F6',
  500: '#03A9F4',
  600: '#039BE5',
  700: '#0288D1',
  800: '#0277BD',
  900: '#01579B',
}









export const clamp = function (cur, lowerBound, upperBound) {
  'worklet';
  if (cur > upperBound) return upperBound;
  if (cur < lowerBound) return lowerBound;
  return cur;
};

export const compareVersion = function (v1, v2) {
  v1 = v1.split('.')
  v2 = v2.split('.')
  const len = Math.max(v1.length, v2.length)

  while (v1.length < len) {
    v1.push('0')
  }
  while (v2.length < len) {
    v2.push('0')
  }

  for (let i = 0; i < len; i++) {
    const num1 = parseInt(v1[i], 10)
    const num2 = parseInt(v2[i], 10)

    if (num1 > num2) {
      return 1
    } else if (num1 < num2) {
      return -1
    }
  }

  return 0
}


export function throttled(fn, delay) {
  let timer = null
  let starttime = Date.now()
  return function () {
    let curTime = Date.now() // 当前时间
    let remaining = delay - (curTime - starttime) // 从上一次到现在，还剩下多少多余时间
    let context = this
    let args = arguments
    clearTimeout(timer)
    if (remaining <= 0) {
      fn.apply(context, args)
      starttime = Date.now()
    } else {
      timer = setTimeout(fn, remaining);
    }
  }
}

export function debounce(func, wait, immediate = false) {

  let timeout;

  return function () {
    let context = this;
    let args = arguments;

    if (timeout) clearTimeout(timeout); // timeout 不为null
    if (immediate) {
      let callNow = !timeout; // 第一次会立即执行，以后只有事件执行后才会再次触发
      timeout = setTimeout(function () {
        timeout = null;
      }, wait)
      if (callNow) {
        func.apply(context, args)
      }
    } else {
      timeout = setTimeout(function () {
        func.apply(context, args)
      }, wait);
    }
  }
}

export const grouby = (list, key = 'createTime') => {
  const temp = new Map()
  for (let i = 0; i < list.length; i++) {
    const newkey = list[i][key].split(' ')[0]
    if (!temp.has(newkey)) {
      temp.set(newkey, [])
    }
    temp.get(newkey).push(list[i])
  }
  return [...temp.entries()].sort((a, b) => {
    if (a[0] > b[0]) {
      return -1
    }
    if (a[0] < b[0]) {
      return 1
    }
    return 0
  }).reduce((obj, [key, value]) => (obj[key] = value,
    obj), {})
}