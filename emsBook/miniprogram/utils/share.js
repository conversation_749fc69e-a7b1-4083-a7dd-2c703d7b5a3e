/**
 * 社交分享工具类
 * 提供微信小程序内的分享功能，包括分享给好友、生成分享图片等
 */

import { wxRequest } from "./wxRequest.js";

/**
 * 获取分享参数
 * @param {Object} params - 分享参数
 * @param {string} params.type - 分享类型：book(图书)、order(订单)
 * @param {string} params.id - 分享内容ID
 * @returns {Object} 返回分享参数
 */
export function getShareParams(params = {}) {
  const { type, id } = params;
  return {
    title: getShareTitle(type),
    path: getSharePath(type, id),
    imageUrl: getShareImage(type),
  };
}

/**
 * 获取分享标题
 * @param {string} type - 分享类型
 * @returns {string} 分享标题
 */
function getShareTitle(type) {
  const titles = {
    book: "我发现了一本好书，快来看看吧！",
    order: "我刚刚预约了这本书，推荐给你",
    default: "emsBook - 让阅读更便捷",
  };
  return titles[type] || titles.default;
}

/**
 * 获取分享路径
 * @param {string} type - 分享类型
 * @param {string} id - 内容ID
 * @returns {string} 分享路径
 */
function getSharePath(type, id) {
  const paths = {
    book: `/views/goods/detail?id=${id}&share=true`,
    order: `/views/orderList/detail?id=${id}&share=true`,
    default: "/pages/home/<USER>",
  };
  return paths[type] || paths.default;
}

/**
 * 获取分享图片
 * @param {string} type - 分享类型
 * @returns {string} 分享图片路径
 */
function getShareImage(type) {
  const images = {
    book: "/static/images/share/book_share.png",
    order: "/static/images/share/order_share.png",
    default: "/static/images/share/default_share.png",
  };
  return images[type] || images.default;
}

/**
 * 生成分享海报
 * @param {Object} params - 海报参数
 * @returns {Promise} 返回海报图片临时路径
 */
export function generatePoster(params = {}) {
  return wxRequest(
    {
      method: "POST",
      data: params,
    },
    "share/poster",
    true,
    "生成分享海报"
  );
}

/**
 * 记录分享行为
 * @param {Object} params - 分享记录参数
 * @returns {Promise} 分享记录结果
 */
export function recordShare(params = {}) {
  return wxRequest(
    {
      method: "POST",
      data: params,
    },
    "share/record",
    true,
    "记录分享行为"
  );
}
