{"version": 3, "sources": ["index.js", "mobx.cjs.production.min.js", "mobx.cjs.development.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,AENA;AFOA,AENA;AFOA,AENA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./mobx.cjs.production.min.js')\n} else {\n  module.exports = require('./mobx.cjs.development.js')\n}\n", "function t(t){for(var n=arguments.length,i=new Array(n>1?n-1:0),r=1;r<n;r++)i[r-1]=arguments[r];throw new Error(\"number\"==typeof t?\"[MobX] minified error nr: \"+t+(i.length?\" \"+i.map(String).join(\",\"):\"\")+\". Find the full error at: https://github.com/mobxjs/mobx/blob/main/packages/mobx/src/errors.js\":\"[MobX] \"+t)}function n(){return\"undefined\"!=typeof globalThis?globalThis:\"undefined\"!=typeof window?window:\"undefined\"!=typeof global?global:\"undefined\"!=typeof self?self:Mn}function i(){Ln||t(\"Proxy not available\")}function r(t){var n=!1;return function(){if(!n)return n=!0,t.apply(this,arguments)}}function e(t){return\"function\"==typeof t}function u(t){switch(typeof t){case\"string\":case\"symbol\":case\"number\":return!0}return!1}function o(t){return null!==t&&\"object\"==typeof t}function s(t){if(!o(t))return!1;var n=Object.getPrototypeOf(t);if(null==n)return!0;var i=Object.hasOwnProperty.call(n,\"constructor\")&&n.constructor;return\"function\"==typeof i&&i.toString()===Cn}function f(t){var n=null==t?void 0:t.constructor;return!!n&&(\"GeneratorFunction\"===n.name||\"GeneratorFunction\"===n.displayName)}function c(t,n,i){Vn(t,n,{enumerable:!1,writable:!0,configurable:!0,value:i})}function a(t,n,i){Vn(t,n,{enumerable:!1,writable:!1,configurable:!0,value:i})}function h(t,n){var i=\"isMobX\"+t;return n.prototype[i]=!0,function(t){return o(t)&&!0===t[i]}}function v(t){return t instanceof Map}function l(t){return t instanceof Set}function d(t){return null===t?null:\"object\"==typeof t?\"\"+t:t}function b(t,n){return Rn.hasOwnProperty.call(t,n)}function p(t,n){for(var i=0;i<n.length;i++){var r=n[i];r.enumerable=r.enumerable||!1,r.configurable=!0,\"value\"in r&&(r.writable=!0),Object.defineProperty(t,\"symbol\"==typeof(e=function(t){if(\"object\"!=typeof t||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,\"string\");if(\"object\"!=typeof i)return i;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return String(t)}(r.key))?e:String(e),r)}var e}function y(t,n,i){return n&&p(t.prototype,n),i&&p(t,i),Object.defineProperty(t,\"prototype\",{writable:!1}),t}function m(){return(m=Object.assign?Object.assign.bind():function(t){for(var n=1;n<arguments.length;n++){var i=arguments[n];for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(t[r]=i[r])}return t}).apply(this,arguments)}function w(t,n){var i,r;t.prototype=Object.create(n.prototype),t.prototype.constructor=t,i=t,r=n,(Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,n){return t.__proto__=n,t})(i,r)}function j(t){if(void 0===t)throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");return t}function O(t,n){(null==n||n>t.length)&&(n=t.length);for(var i=0,r=new Array(n);i<n;i++)r[i]=t[i];return r}function x(t,n){var i=\"undefined\"!=typeof Symbol&&t[Symbol.iterator]||t[\"@@iterator\"];if(i)return(i=i.call(t)).next.bind(i);if(Array.isArray(t)||(i=function(t){if(t){if(\"string\"==typeof t)return O(t,void 0);var n=Object.prototype.toString.call(t).slice(8,-1);return\"Object\"===n&&t.constructor&&(n=t.constructor.name),\"Map\"===n||\"Set\"===n?Array.from(t):\"Arguments\"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?O(t,void 0):void 0}}(t))||n&&t&&\"number\"==typeof t.length){i&&(t=i);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\")}function _(t){return Object.assign((function(n,i){if(A(i))return t.t(n,i);g(n,i,t)}),t)}function g(t,n,i){b(t,Bn)||c(t,Bn,m({},t[Bn])),function(t){return\"override\"===t.i}(i)||(t[Bn][n]=i)}function A(t){return\"object\"==typeof t&&\"string\"==typeof t.kind}function S(t,n,i){void 0===n&&(n=In),void 0===i&&(i=In);var r=new qn(t);return n!==In&&Tt(r,n),i!==In&&kt(r,i),r}function M(t,n,i){return Gt(t)?t:Array.isArray(t)?ni.array(t,{name:i}):s(t)?ni.object(t,void 0,{name:i}):v(t)?ni.map(t,{name:i}):l(t)?ni.set(t,{name:i}):\"function\"!=typeof t||Nt(t)||Bt(t)?t:f(t)?Ii(t):Ri(i,t)}function E(t){return t}function N(t,n){return{i:t,u:n,o:V,s:R,t:T}}function V(t,n,i,r){var e;if(null!=(e=this.u)&&e.bound)return null===this.s(t,n,i,!1)?0:1;if(r===t.h)return null===this.s(t,n,i,!1)?0:2;if(Nt(i.value))return 1;var u=k(t,this,n,i,!1);return Vn(r,n,u),2}function R(t,n,i,r){var e=k(t,this,n,i);return t.v(n,e,r)}function T(n,i){var r=i.kind,e=i.name,u=i.addInitializer,o=this;if(\"field\"!=r){var s,f,c,a,h,v;if(\"method\"==r)return Nt(n)||(f=n,n=tt(null!=(c=null==(a=o.u)?void 0:a.name)?c:e.toString(),f,null!=(h=null==(v=o.u)?void 0:v.autoAction)&&h)),null!=(s=this.u)&&s.bound&&u((function(){var t=this[e].bind(this);t.isMobxAction=!0,this[e]=t})),n;t(\"Cannot apply '\"+o.i+\"' to '\"+String(e)+\"' (kind: \"+r+\"):\\n'\"+o.i+\"' can only be used on properties with a function value.\")}else u((function(){g(this,e,o)}))}function k(t,n,i,r,e){var u,o,s,f,c,a,h;void 0===e&&(e=xi.safeDescriptors);var v,l=r.value;return null!=(u=n.u)&&u.bound&&(l=l.bind(null!=(v=t.l)?v:t.h)),{value:tt(null!=(o=null==(s=n.u)?void 0:s.name)?o:i.toString(),l,null!=(f=null==(c=n.u)?void 0:c.autoAction)&&f,null!=(a=n.u)&&a.bound?null!=(h=t.l)?h:t.h:void 0),configurable:!e||t.p,enumerable:!1,writable:!e}}function L(t,n){return{i:t,u:n,o:C,s:I,t:K}}function C(t,n,i,r){var e;if(r===t.h)return null===this.s(t,n,i,!1)?0:2;if(null!=(e=this.u)&&e.bound&&(!b(t.h,n)||!Bt(t.h[n]))&&null===this.s(t,n,i,!1))return 0;if(Bt(i.value))return 1;var u=P(t,0,0,i,!1,!1);return Vn(r,n,u),2}function I(t,n,i,r){var e,u=P(t,0,0,i,null==(e=this.u)?void 0:e.bound);return t.v(n,u,r)}function K(t,n){var i,r=n.name,e=n.addInitializer;return Bt(t)||(t=Ii(t)),null!=(i=this.u)&&i.bound&&e((function(){var t=this[r].bind(this);t.isMobXFlow=!0,this[r]=t})),t}function P(t,n,i,r,e,u){void 0===u&&(u=xi.safeDescriptors);var o,s=r.value;return Bt(s)||(s=Ii(s)),e&&((s=s.bind(null!=(o=t.l)?o:t.h)).isMobXFlow=!0),{value:s,configurable:!u||t.p,enumerable:!1,writable:!u}}function D(t,n){return{i:t,u:n,o:B,s:W,t:q}}function B(t,n,i){return null===this.s(t,n,i,!1)?0:1}function W(t,n,i,r){return t.m(n,m({},this.u,{get:i.get,set:i.set}),r)}function q(t,n){var i=this,r=n.name;return(0,n.addInitializer)((function(){var n=hn(this)[Wn],e=m({},i.u,{get:t,context:this});e.name||(e.name=\"ObservableObject.\"+r.toString()),n.j.set(r,new bi(e))})),function(){return this[Wn].O(r)}}function G(t,n){return{i:t,u:n,o:H,s:X,t:U}}function H(t,n,i){return null===this.s(t,n,i,!1)?0:1}function X(t,n,i,r){var e,u;return t._(n,i.value,null!=(e=null==(u=this.u)?void 0:u.enhancer)?e:M,r)}function U(t,n){function i(t,n){var i,e,s=hn(t)[Wn],f=new hi(n,null!=(i=null==(e=r.u)?void 0:e.enhancer)?i:M,\"ObservableObject.\"+u.toString(),!1);s.j.set(u,f),o.add(t)}var r=this,e=n.kind,u=n.name,o=new WeakSet;if(\"accessor\"==e)return{get:function(){return o.has(this)||i(this,t.get.call(this)),this[Wn].O(u)},set:function(t){return o.has(this)||i(this,t),this[Wn].g(u,t)},init:function(t){return o.has(this)||i(this,t),t}}}function F(t){return{i:\"true\",u:t,o:z,s:$,t:J}}function z(t,n,i,r){var e,u,o,s;if(i.get)return ei.o(t,n,i,r);if(i.set){var c=tt(n.toString(),i.set);return r===t.h?null===t.v(n,{configurable:!xi.safeDescriptors||t.p,set:c})?0:2:(Vn(r,n,{configurable:!0,set:c}),2)}if(r!==t.h&&\"function\"==typeof i.value)return f(i.value)?(null!=(s=this.u)&&s.autoBind?Ii.bound:Ii).o(t,n,i,r):(null!=(o=this.u)&&o.autoBind?Ri.bound:Ri).o(t,n,i,r);var a,h=!1===(null==(e=this.u)?void 0:e.deep)?ni.ref:ni;return\"function\"==typeof i.value&&null!=(u=this.u)&&u.autoBind&&(i.value=i.value.bind(null!=(a=t.l)?a:t.h)),h.o(t,n,i,r)}function $(t,n,i,r){var e,u,o;return i.get?ei.s(t,n,i,r):i.set?t.v(n,{configurable:!xi.safeDescriptors||t.p,set:tt(n.toString(),i.set)},r):(\"function\"==typeof i.value&&null!=(e=this.u)&&e.autoBind&&(i.value=i.value.bind(null!=(o=t.l)?o:t.h)),(!1===(null==(u=this.u)?void 0:u.deep)?ni.ref:ni).s(t,n,i,r))}function J(){t(\"'\"+this.i+\"' cannot be used as a decorator\")}function Y(t){return t||Fn}function Q(t){return!0===t.deep?M:!1===t.deep?E:(n=t.defaultDecorator)&&null!=(i=null==(r=n.u)?void 0:r.enhancer)?i:M;var n,i,r}function Z(t,n,i){return A(n)?zn.t(t,n):u(n)?void g(t,n,zn):Gt(t)?t:s(t)?ni.object(t,n,i):Array.isArray(t)?ni.array(t,n):v(t)?ni.map(t,n):l(t)?ni.set(t,n):\"object\"==typeof t&&null!==t?t:ni.box(t,n)}function tt(t,n,i,r){function e(){return nt(0,i,n,r||this,arguments)}return void 0===i&&(i=!1),e.isMobxAction=!0,e.toString=function(){return n.toString()},fi&&(ci.value=t,Vn(e,\"name\",ci)),e}function nt(t,n,i,r,e){var u=it(0,n);try{return i.apply(r,e)}catch(t){throw u.A=t,t}finally{rt(u)}}function it(t,n){var i=xi.trackingDerivation,r=!n||!i;jt();var e=xi.allowStateChanges;r&&(vt(),e=ut(!0));var u={S:r,M:i,N:e,V:dt(!0),R:!1,T:0,k:si++,L:oi};return oi=u.k,u}function rt(n){oi!==n.k&&t(30),oi=n.L,void 0!==n.A&&(xi.suppressReactionErrors=!0),ot(n.N),bt(n.V),Ot(),n.S&&lt(n.M),xi.suppressReactionErrors=!1}function et(t,n){var i=ut(t);try{return n()}finally{ot(i)}}function ut(t){var n=xi.allowStateChanges;return xi.allowStateChanges=t,n}function ot(t){xi.allowStateChanges=t}function st(t){return t instanceof yi}function ft(t){switch(t.C){case li.I:return!1;case li.K:case li.P:return!0;case li.D:for(var n=dt(!0),i=vt(),r=t.B,e=r.length,u=0;u<e;u++){var o=r[u];if(pi(o)){if(xi.disableErrorBoundaries)o.get();else try{o.get()}catch(t){return lt(i),bt(n),!0}if(t.C===li.P)return lt(i),bt(n),!0}}return pt(t),lt(i),bt(n),!1}}function ct(t,n,i){var r=dt(!0);pt(t),t.W=new Array(t.B.length+100),t.q=0,t.G=++xi.runId;var e,u=xi.trackingDerivation;if(xi.trackingDerivation=t,xi.inBatch++,!0===xi.disableErrorBoundaries)e=n.call(i);else try{e=n.call(i)}catch(t){e=new yi(t)}return xi.inBatch--,xi.trackingDerivation=u,function(t){for(var n=t.B,i=t.B=t.W,r=li.I,e=0,u=t.q,o=0;o<u;o++){var s=i[o];0===s.H&&(s.H=1,e!==o&&(i[e]=s),e++),s.C>r&&(r=s.C)}for(i.length=e,t.W=null,u=n.length;u--;){var f=n[u];0===f.H&&mt(f,t),f.H=0}for(;e--;){var c=i[e];1===c.H&&(c.H=0,yt(c,t))}r!==li.I&&(t.C=r,t.X())}(t),bt(r),e}function at(t){var n=t.B;t.B=[];for(var i=n.length;i--;)mt(n[i],t);t.C=li.K}function ht(t){var n=vt();try{return t()}finally{lt(n)}}function vt(){var t=xi.trackingDerivation;return xi.trackingDerivation=null,t}function lt(t){xi.trackingDerivation=t}function dt(t){var n=xi.allowStateReads;return xi.allowStateReads=t,n}function bt(t){xi.allowStateReads=t}function pt(t){if(t.C!==li.I){t.C=li.I;for(var n=t.B,i=n.length;i--;)n[i].U=li.I}}function yt(t,n){t.F.add(n),t.U>n.C&&(t.U=n.C)}function mt(t,n){t.F.delete(n),0===t.F.size&&wt(t)}function wt(t){!1===t.$&&(t.$=!0,xi.pendingUnobservations.push(t))}function jt(){xi.inBatch++}function Ot(){if(0==--xi.inBatch){gt();for(var t=xi.pendingUnobservations,n=0;n<t.length;n++){var i=t[n];i.$=!1,0===i.F.size&&(i.J&&(i.J=!1,i.onBUO()),i instanceof bi&&i.Y())}xi.pendingUnobservations=[]}}function xt(t){var n=xi.trackingDerivation;return null!==n?(n.G!==t.Z&&(t.Z=n.G,n.W[n.q++]=t,!t.J&&xi.trackingContext&&(t.J=!0,t.onBO())),t.J):(0===t.F.size&&xi.inBatch>0&&wt(t),!1)}function _t(t){t.U!==li.P&&(t.U=li.P,t.F.forEach((function(t){t.C===li.I&&t.X(),t.C=li.P})))}function gt(){xi.inBatch>0||xi.isRunningReactions||gi(At)}function At(){xi.isRunningReactions=!0;for(var t=xi.pendingReactions,n=0;t.length>0;){100==++n&&(console.error(\"[mobx] cycle in reaction: \"+t[0]),t.splice(0));for(var i=t.splice(0),r=0,e=i.length;r<e;r++)i[r].tt()}xi.isRunningReactions=!1}function St(){return console.warn(\"[mobx.spy] Is a no-op in production builds\"),function(){}}function Mt(t){return function(n,i){return e(n)?tt(n.name||\"<unnamed action>\",n,t):e(i)?tt(n,i,t):A(i)?(t?Ei:Si).t(n,i):u(i)?g(n,i,t?Ei:Si):u(n)?_(N(t?\"autoAction\":\"action\",{name:n,autoAction:t})):void 0}}function Et(t){return nt(0,!1,t,this,void 0)}function Nt(t){return e(t)&&!0===t.isMobxAction}function Vt(t,n){function i(){t(f)}var r,e,u,o,s;void 0===n&&(n=kn);var f,c=null!=(r=null==(e=n)?void 0:e.name)?r:\"Autorun\";if(n.scheduler||n.delay){var a=Rt(n),h=!1;f=new _i(c,(function(){h||(h=!0,a((function(){h=!1,f.nt||f.track(i)})))}),n.onError,n.requiresObservable)}else f=new _i(c,(function(){this.track(i)}),n.onError,n.requiresObservable);return null!=(u=n)&&null!=(o=u.signal)&&o.aborted||f.it(),f.rt(null==(s=n)?void 0:s.signal)}function Rt(t){return t.scheduler?t.scheduler:t.delay?function(n){return setTimeout(n,t.delay)}:Ti}function Tt(t,n,i){return Lt(\"onBO\",t,n,i)}function kt(t,n,i){return Lt(\"onBUO\",t,n,i)}function Lt(t,n,i,r){var u=\"function\"==typeof r?wn(n,i):wn(n),o=e(r)?r:i,s=t+\"L\";return u[s]?u[s].add(o):u[s]=new Set([o]),function(){var t=u[s];t&&(t.delete(o),0===t.size&&delete u[s])}}function Ct(t,n,i,r){var e=Dn(n);return xn((function(){var n=hn(t,r)[Wn];Pn(e).forEach((function(t){n.s(t,e[t],!i||!(t in i)||i[t])}))})),t}function It(t){var n,i={name:t.et};return t.B&&t.B.length>0&&(i.dependencies=(n=t.B,Array.from(new Set(n))).map(It)),i}function Kt(t){var n={name:t.et};return function(t){return t.F&&t.F.size>0}(t)&&(n.observers=Array.from(function(t){return t.F}(t)).map(Kt)),n}function Pt(){this.message=\"FLOW_CANCELLED\"}function Dt(t){e(t.cancel)&&t.cancel()}function Bt(t){return!0===(null==t?void 0:t.isMobXFlow)}function Wt(t,n){if(void 0===n)return pi(t);if(!1===ln(t))return!1;if(!t[Wn].j.has(n))return!1;var i=wn(t,n);return pi(i)}function qt(t,n){return!!t&&(void 0!==n?!!ln(t)&&t[Wn].j.has(n):ln(t)||!!t[Wn]||Gn(t)||Ai(t)||pi(t))}function Gt(t){return qt(t)}function Ht(n){return ln(n)?n[Wn].ut():Yi(n)||ir(n)?Array.from(n.keys()):an(n)?n.map((function(t,n){return n})):void t(5)}function Xt(n,i){return ln(n)?n[Wn].ot(i):Yi(n)||ir(n)?n.has(i):an(n)?i>=0&&i<n.length:void t(10)}function Ut(n){if(ln(n))return n[Wn].st();t(38)}function Ft(t,n,i){return t.set(n,i),i}function zt(t,n){void 0===n&&(n=void 0),jt();try{return t.apply(n)}finally{Ot()}}function $t(t,n,i){var r;if(\"number\"==typeof i.timeout){var e=new Error(\"WHEN_TIMEOUT\");r=setTimeout((function(){if(!o[Wn].nt){if(o(),!i.onError)throw e;i.onError(e)}}),i.timeout)}i.name=\"When\";var u=tt(\"When-effect\",n),o=Vt((function(n){et(!1,t)&&(n.dispose(),r&&clearTimeout(r),u())}),i);return o}function Jt(t,n){var i,r,e;if(null!=n&&null!=(i=n.signal)&&i.aborted)return Object.assign(Promise.reject(new Error(\"WHEN_ABORTED\")),{cancel:function(){return null}});var u=new Promise((function(i,u){var o,s=$t(t,i,m({},n,{onError:u}));r=function(){s(),u(new Error(\"WHEN_CANCELLED\"))},e=function(){s(),u(new Error(\"WHEN_ABORTED\"))},null==n||null==(o=n.signal)||null==o.addEventListener||o.addEventListener(\"abort\",e)})).finally((function(){var t;return null==n||null==(t=n.signal)||null==t.removeEventListener?void 0:t.removeEventListener(\"abort\",e)}));return u.cancel=r,u}function Yt(t){return t[Wn]}function Qt(t){return void 0!==t.ft&&t.ft.length>0}function Zt(t,n){var i=t.ft||(t.ft=[]);return i.push(n),r((function(){var t=i.indexOf(n);-1!==t&&i.splice(t,1)}))}function tn(n,i){var r=vt();try{for(var e=[].concat(n.ft||[]),u=0,o=e.length;u<o&&((i=e[u](i))&&!i.type&&t(14),i);u++);return i}finally{lt(r)}}function nn(t){return void 0!==t.ct&&t.ct.length>0}function rn(t,n){var i=t.ct||(t.ct=[]);return i.push(n),r((function(){var t=i.indexOf(n);-1!==t&&i.splice(t,1)}))}function en(t,n){var i=vt(),r=t.ct;if(r){for(var e=0,u=(r=r.slice()).length;e<u;e++)r[e](n);lt(i)}}function un(t,n,r,e){return void 0===r&&(r=\"ObservableArray\"),void 0===e&&(e=!1),i(),xn((function(){var i=new Wi(r,n,e,!1);a(i.j,Wn,i);var u=new Proxy(i.j,Bi);return i.l=u,t&&t.length&&i.at(0,0,t),u}))}function on(t,n){\"function\"==typeof Array.prototype[t]&&(qi[t]=n(t))}function sn(t){return function(){var n=this[Wn];n.ht.reportObserved();var i=n.vt(n.j);return i[t].apply(i,arguments)}}function fn(t){return function(n,i){var r=this,e=this[Wn];return e.ht.reportObserved(),e.vt(e.j)[t]((function(t,e){return n.call(i,t,e,r)}))}}function cn(t){return function(){var n=this,i=this[Wn];i.ht.reportObserved();var r=i.vt(i.j),e=arguments[0];return arguments[0]=function(t,i,r){return e(t,i,r,n)},r[t].apply(r,arguments)}}function an(t){return o(t)&&Xi(t[Wn])}function hn(t,n){var i;if(b(t,Wn))return t;var r=null!=(i=null==n?void 0:n.name)?i:\"ObservableObject\",e=new er(t,new Map,String(r),function(t){var n;return t?null!=(n=t.defaultDecorator)?n:F(t):void 0}(n));return c(t,Wn,e),t}function vn(t){return rr[t]||(rr[t]={get:function(){return this[Wn].O(t)},set:function(n){return this[Wn].g(t,n)}})}function ln(t){return!!o(t)&&ur(t[Wn])}function dn(t,n,i){var r;null==(r=t.h[Bn])||delete r[i]}function bn(t){return{enumerable:!1,configurable:!0,get:function(){return this[Wn].lt(t)},set:function(n){this[Wn].dt(t,n)}}}function pn(t){Vn(ar.prototype,\"\"+t,bn(t))}function yn(t){if(t>fr){for(var n=fr;n<t+100;n++)pn(n);fr=t}}function mn(t,n,i){return new ar(t,n,i)}function wn(n,i){if(\"object\"==typeof n&&null!==n){if(an(n))return void 0!==i&&t(23),n[Wn].ht;if(ir(n))return n.ht;if(Yi(n)){if(void 0===i)return n.bt;var r=n.pt.get(i)||n.yt.get(i);return r||t(25,i,On(n)),r}if(ln(n)){if(!i)return t(26);var u=n[Wn].j.get(i);return u||t(27,i,On(n)),u}if(Gn(n)||pi(n)||Ai(n))return n}else if(e(n)&&Ai(n[Wn]))return n[Wn];t(28)}function jn(n,i){return n||t(29),void 0!==i?jn(wn(n,i)):Gn(n)||pi(n)||Ai(n)||Yi(n)||ir(n)?n:n[Wn]?n[Wn]:void t(24,n)}function On(t,n){var i;if(void 0!==n)i=wn(t,n);else{if(Nt(t))return t.name;i=ln(t)||Yi(t)||ir(t)?jn(t):wn(t)}return i.et}function xn(t){var n=vt(),i=ut(!0);jt();try{return t()}finally{Ot(),ot(i),lt(n)}}function _n(t,n,i){return void 0===i&&(i=-1),function t(n,i,r,u,o){if(n===i)return 0!==n||1/n==1/i;if(null==n||null==i)return!1;if(n!=n)return i!=i;var s=typeof n;if(\"function\"!==s&&\"object\"!==s&&\"object\"!=typeof i)return!1;var f=hr.call(n);if(f!==hr.call(i))return!1;switch(f){case\"[object RegExp]\":case\"[object String]\":return\"\"+n==\"\"+i;case\"[object Number]\":return+n!=+n?+i!=+i:0==+n?1/+n==1/i:+n==+i;case\"[object Date]\":case\"[object Boolean]\":return+n==+i;case\"[object Symbol]\":return\"undefined\"!=typeof Symbol&&Symbol.valueOf.call(n)===Symbol.valueOf.call(i);case\"[object Map]\":case\"[object Set]\":r>=0&&r++}n=gn(n),i=gn(i);var c=\"[object Array]\"===f;if(!c){if(\"object\"!=typeof n||\"object\"!=typeof i)return!1;var a=n.constructor,h=i.constructor;if(a!==h&&!(e(a)&&a instanceof a&&e(h)&&h instanceof h)&&\"constructor\"in n&&\"constructor\"in i)return!1}if(0===r)return!1;r<0&&(r=-1),o=o||[];for(var v=(u=u||[]).length;v--;)if(u[v]===n)return o[v]===i;if(u.push(n),o.push(i),c){if((v=n.length)!==i.length)return!1;for(;v--;)if(!t(n[v],i[v],r-1,u,o))return!1}else{var l,d=Object.keys(n);if(v=d.length,Object.keys(i).length!==v)return!1;for(;v--;)if(!b(i,l=d[v])||!t(n[l],i[l],r-1,u,o))return!1}return u.pop(),o.pop(),!0}(t,n,i)}function gn(t){return an(t)?t.slice():v(t)||Yi(t)||l(t)||ir(t)?Array.from(t.entries()):t}function An(t){return t[Symbol.iterator]=Sn,t}function Sn(){return this}Object.defineProperty(exports,\"__esModule\",{value:!0});var Mn={},En=Object.assign,Nn=Object.getOwnPropertyDescriptor,Vn=Object.defineProperty,Rn=Object.prototype,Tn=[];Object.freeze(Tn);var kn={};Object.freeze(kn);var Ln=\"undefined\"!=typeof Proxy,Cn=Object.toString(),In=function(){},Kn=void 0!==Object.getOwnPropertySymbols,Pn=\"undefined\"!=typeof Reflect&&Reflect.ownKeys?Reflect.ownKeys:Kn?function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:Object.getOwnPropertyNames,Dn=Object.getOwnPropertyDescriptors||function(t){var n={};return Pn(t).forEach((function(i){n[i]=Nn(t,i)})),n},Bn=Symbol(\"mobx-stored-annotations\"),Wn=Symbol(\"mobx administration\"),qn=function(){function t(t){void 0===t&&(t=\"Atom\"),this.et=void 0,this.$=!1,this.J=!1,this.F=new Set,this.H=0,this.Z=0,this.U=li.K,this.onBOL=void 0,this.onBUOL=void 0,this.et=t}var n=t.prototype;return n.onBO=function(){this.onBOL&&this.onBOL.forEach((function(t){return t()}))},n.onBUO=function(){this.onBUOL&&this.onBUOL.forEach((function(t){return t()}))},n.reportObserved=function(){return xt(this)},n.reportChanged=function(){jt(),_t(this),Ot()},n.toString=function(){return this.et},t}(),Gn=h(\"Atom\",qn),Hn={identity:function(t,n){return t===n},structural:function(t,n){return _n(t,n)},default:function(t,n){return Object.is?Object.is(t,n):t===n?0!==t||1/t==1/n:t!=t&&n!=n},shallow:function(t,n){return _n(t,n,1)}},Xn=_({i:\"override\",o:function(){return 0},s:function(){t(\"'\"+this.i+\"' can only be used with 'makeObservable'\")},t:function(){console.warn(\"'\"+this.i+\"' cannot be used with decorators - this is a no-op\")}}),Un=F(),Fn={deep:!0,name:void 0,defaultDecorator:void 0,proxy:!0};Object.freeze(Fn);var zn=G(\"observable\"),$n=G(\"observable.ref\",{enhancer:E}),Jn=G(\"observable.shallow\",{enhancer:function(t,n,i){return null==t||ln(t)||an(t)||Yi(t)||ir(t)?t:Array.isArray(t)?ni.array(t,{name:i,deep:!1}):s(t)?ni.object(t,void 0,{name:i,deep:!1}):v(t)?ni.map(t,{name:i,deep:!1}):l(t)?ni.set(t,{name:i,deep:!1}):void 0}}),Yn=G(\"observable.struct\",{enhancer:function(t,n){return _n(t,n)?n:t}}),Qn=_(zn);En(Z,Qn);var Zn,ti,ni=En(Z,{box:function(t,n){var i=Y(n);return new hi(t,Q(i),i.name,!0,i.equals)},array:function(t,n){var i=Y(n);return(!1===xi.useProxies||!1===i.proxy?mn:un)(t,Q(i),i.name)},map:function(t,n){var i=Y(n);return new Ji(t,Q(i),i.name)},set:function(t,n){var i=Y(n);return new nr(t,Q(i),i.name)},object:function(t,n,r){return xn((function(){return Ct(!1===xi.useProxies||!1===(null==r?void 0:r.proxy)?hn({},r):function(t,n){var r,e;return i(),null!=(e=(r=(t=hn(t,n))[Wn]).l)?e:r.l=new Proxy(t,Ki)}({},r),t,n)}))},ref:_($n),shallow:_(Jn),deep:Qn,struct:_(Yn)}),ii=D(\"computed\"),ri=D(\"computed.struct\",{equals:Hn.structural}),ei=function(t,n){if(A(n))return ii.t(t,n);if(u(n))return g(t,n,ii);if(s(t))return _(D(\"computed\",t));var i=s(n)?n:{};return i.get=t,i.name||(i.name=t.name||\"\"),new bi(i)};Object.assign(ei,ii),ei.struct=_(ri);var ui,oi=0,si=1,fi=null!=(Zn=null==(ti=Nn((function(){}),\"name\"))?void 0:ti.configurable)&&Zn,ci={value:\"action\",configurable:!0,writable:!1,enumerable:!1};ui=Symbol.toPrimitive;var ai,hi=function(t){function n(n,i,r,e,u){var o;return void 0===r&&(r=\"ObservableValue\"),void 0===u&&(u=Hn.default),(o=t.call(this,r)||this).enhancer=void 0,o.et=void 0,o.equals=void 0,o.wt=!1,o.ft=void 0,o.ct=void 0,o.jt=void 0,o.dehancer=void 0,o.enhancer=i,o.et=r,o.equals=u,o.jt=i(n,void 0,r),o}w(n,t);var i=n.prototype;return i.dehanceValue=function(t){return void 0!==this.dehancer?this.dehancer(t):t},i.set=function(t){(t=this.Ot(t))!==xi.UNCHANGED&&this.xt(t)},i.Ot=function(t){if(Qt(this)){var n=tn(this,{object:this,type:Di,newValue:t});if(!n)return xi.UNCHANGED;t=n.newValue}return t=this.enhancer(t,this.jt,this.et),this.equals(this.jt,t)?xi.UNCHANGED:t},i.xt=function(t){var n=this.jt;this.jt=t,this.reportChanged(),nn(this)&&en(this,{type:Di,object:this,newValue:t,oldValue:n})},i.get=function(){return this.reportObserved(),this.dehanceValue(this.jt)},i._t=function(t){return Zt(this,t)},i.gt=function(t,n){return n&&t({observableKind:\"value\",debugObjectName:this.et,object:this,type:Di,newValue:this.jt,oldValue:void 0}),rn(this,t)},i.raw=function(){return this.jt},i.toJSON=function(){return this.get()},i.toString=function(){return this.et+\"[\"+this.jt+\"]\"},i.valueOf=function(){return d(this.get())},i[ui]=function(){return this.valueOf()},n}(qn),vi=h(\"ObservableValue\",hi);ai=Symbol.toPrimitive;var li,di,bi=function(){function n(n){this.C=li.K,this.B=[],this.W=null,this.J=!1,this.$=!1,this.F=new Set,this.H=0,this.G=0,this.Z=0,this.U=li.I,this.q=0,this.jt=new yi(null),this.et=void 0,this.At=void 0,this.St=!1,this.Mt=!1,this.derivation=void 0,this.Et=void 0,this.Nt=di.NONE,this.Vt=void 0,this.Rt=void 0,this.Tt=void 0,this.kt=void 0,this.onBOL=void 0,this.onBUOL=void 0,n.get||t(31),this.derivation=n.get,this.et=n.name||\"ComputedValue\",n.set&&(this.Et=tt(\"ComputedValue-setter\",n.set)),this.Rt=n.equals||(n.compareStructural||n.struct?Hn.structural:Hn.default),this.Vt=n.context,this.Tt=n.requiresReaction,this.kt=!!n.keepAlive}var i=n.prototype;return i.X=function(){!function(t){t.U===li.I&&(t.U=li.D,t.F.forEach((function(t){t.C===li.I&&(t.C=li.D,t.X())})))}(this)},i.onBO=function(){this.onBOL&&this.onBOL.forEach((function(t){return t()}))},i.onBUO=function(){this.onBUOL&&this.onBUOL.forEach((function(t){return t()}))},i.get=function(){if(this.St&&t(32,this.et,this.derivation),0!==xi.inBatch||0!==this.F.size||this.kt){if(xt(this),ft(this)){var n=xi.trackingContext;this.kt&&!n&&(xi.trackingContext=this),this.trackAndCompute()&&function(t){t.U!==li.P&&(t.U=li.P,t.F.forEach((function(n){n.C===li.D?n.C=li.P:n.C===li.I&&(t.U=li.I)})))}(this),xi.trackingContext=n}}else ft(this)&&(this.Lt(),jt(),this.jt=this.Ct(!1),Ot());var i=this.jt;if(st(i))throw i.cause;return i},i.set=function(n){if(this.Et){this.Mt&&t(33,this.et),this.Mt=!0;try{this.Et.call(this.Vt,n)}finally{this.Mt=!1}}else t(34,this.et)},i.trackAndCompute=function(){var t=this.jt,n=this.C===li.K,i=this.Ct(!0),r=n||st(t)||st(i)||!this.Rt(t,i);return r&&(this.jt=i),r},i.Ct=function(t){this.St=!0;var n,i=ut(!1);if(t)n=ct(this,this.derivation,this.Vt);else if(!0===xi.disableErrorBoundaries)n=this.derivation.call(this.Vt);else try{n=this.derivation.call(this.Vt)}catch(t){n=new yi(t)}return ot(i),this.St=!1,n},i.Y=function(){this.kt||(at(this),this.jt=void 0)},i.gt=function(t,n){var i=this,r=!0,e=void 0;return Vt((function(){var u=i.get();if(!r||n){var o=vt();t({observableKind:\"computed\",debugObjectName:i.et,type:Di,object:i,newValue:u,oldValue:e}),lt(o)}r=!1,e=u}))},i.Lt=function(){},i.toString=function(){return this.et+\"[\"+this.derivation.toString()+\"]\"},i.valueOf=function(){return d(this.get())},i[ai]=function(){return this.valueOf()},n}(),pi=h(\"ComputedValue\",bi);!function(t){t[t.K=-1]=\"NOT_TRACKING_\",t[t.I=0]=\"UP_TO_DATE_\",t[t.D=1]=\"POSSIBLY_STALE_\",t[t.P=2]=\"STALE_\"}(li||(li={})),function(t){t[t.NONE=0]=\"NONE\",t[t.LOG=1]=\"LOG\",t[t.BREAK=2]=\"BREAK\"}(di||(di={}));var yi=function(t){this.cause=void 0,this.cause=t},mi=[\"mobxGuid\",\"spyListeners\",\"enforceActions\",\"computedRequiresReaction\",\"reactionRequiresObservable\",\"observableRequiresReaction\",\"allowStateReads\",\"disableErrorBoundaries\",\"runId\",\"UNCHANGED\",\"useProxies\"],wi=function(){this.version=6,this.UNCHANGED={},this.trackingDerivation=null,this.trackingContext=null,this.runId=0,this.mobxGuid=0,this.inBatch=0,this.pendingUnobservations=[],this.pendingReactions=[],this.isRunningReactions=!1,this.allowStateChanges=!1,this.allowStateReads=!0,this.enforceActions=!0,this.spyListeners=[],this.globalReactionErrorHandlers=[],this.computedRequiresReaction=!1,this.reactionRequiresObservable=!1,this.observableRequiresReaction=!1,this.disableErrorBoundaries=!1,this.suppressReactionErrors=!1,this.useProxies=!0,this.verifyProxies=!1,this.safeDescriptors=!0},ji=!0,Oi=!1,xi=function(){var i=n();return i.__mobxInstanceCount>0&&!i.__mobxGlobals&&(ji=!1),i.__mobxGlobals&&i.__mobxGlobals.version!==(new wi).version&&(ji=!1),ji?i.__mobxGlobals?(i.__mobxInstanceCount+=1,i.__mobxGlobals.UNCHANGED||(i.__mobxGlobals.UNCHANGED={}),i.__mobxGlobals):(i.__mobxInstanceCount=1,i.__mobxGlobals=new wi):(setTimeout((function(){Oi||t(35)}),1),new wi)}(),_i=function(){function t(t,n,i,r){void 0===t&&(t=\"Reaction\"),this.et=void 0,this.It=void 0,this.Kt=void 0,this.Pt=void 0,this.B=[],this.W=[],this.C=li.K,this.H=0,this.G=0,this.q=0,this.nt=!1,this.Dt=!1,this.Bt=!1,this.Wt=!1,this.Nt=di.NONE,this.et=t,this.It=n,this.Kt=i,this.Pt=r}var n=t.prototype;return n.X=function(){this.it()},n.it=function(){this.Dt||(this.Dt=!0,xi.pendingReactions.push(this),gt())},n.isScheduled=function(){return this.Dt},n.tt=function(){if(!this.nt){jt(),this.Dt=!1;var t=xi.trackingContext;if(xi.trackingContext=this,ft(this)){this.Bt=!0;try{this.It()}catch(t){this.qt(t)}}xi.trackingContext=t,Ot()}},n.track=function(t){if(!this.nt){jt(),this.Wt=!0;var n=xi.trackingContext;xi.trackingContext=this;var i=ct(this,t,void 0);xi.trackingContext=n,this.Wt=!1,this.Bt=!1,this.nt&&at(this),st(i)&&this.qt(i.cause),Ot()}},n.qt=function(t){var n=this;if(this.Kt)this.Kt(t,this);else{if(xi.disableErrorBoundaries)throw t;xi.suppressReactionErrors||console.error(\"[mobx] uncaught error in '\"+this+\"'\",t),xi.globalReactionErrorHandlers.forEach((function(i){return i(t,n)}))}},n.dispose=function(){this.nt||(this.nt=!0,this.Wt||(jt(),at(this),Ot()))},n.rt=function(t){var n=this,i=function i(){n.dispose(),null==t||null==t.removeEventListener||t.removeEventListener(\"abort\",i)};return null==t||null==t.addEventListener||t.addEventListener(\"abort\",i),i[Wn]=this,i},n.toString=function(){return\"Reaction[\"+this.et+\"]\"},n.trace=function(){},t}(),gi=function(t){return t()},Ai=h(\"Reaction\",_i),Si=N(\"action\"),Mi=N(\"action.bound\",{bound:!0}),Ei=N(\"autoAction\",{autoAction:!0}),Ni=N(\"autoAction.bound\",{autoAction:!0,bound:!0}),Vi=Mt(!1);Object.assign(Vi,Si);var Ri=Mt(!0);Object.assign(Ri,Ei),Vi.bound=_(Mi),Ri.bound=_(Ni);var Ti=function(t){return t()},ki=0;Pt.prototype=Object.create(Error.prototype);var Li=L(\"flow\"),Ci=L(\"flow.bound\",{bound:!0}),Ii=Object.assign((function(t,n){if(A(n))return Li.t(t,n);if(u(n))return g(t,n,Li);var i=t,r=i.name||\"<unnamed flow>\",o=function(){var t,n=this,u=arguments,o=++ki,s=Vi(r+\" - runid: \"+o+\" - init\",i).apply(n,u),f=void 0,c=new Promise((function(n,i){function u(t){var n;f=void 0;try{n=Vi(r+\" - runid: \"+o+\" - yield \"+h++,s.next).call(s,t)}catch(t){return i(t)}a(n)}function c(t){var n;f=void 0;try{n=Vi(r+\" - runid: \"+o+\" - yield \"+h++,s.throw).call(s,t)}catch(t){return i(t)}a(n)}function a(t){if(!e(null==t?void 0:t.then))return t.done?n(t.value):(f=Promise.resolve(t.value)).then(u,c);t.then(a,i)}var h=0;t=i,u(void 0)}));return c.cancel=Vi(r+\" - runid: \"+o+\" - cancel\",(function(){try{f&&Dt(f);var n=s.return(void 0),i=Promise.resolve(n.value);i.then(In,In),Dt(i),t(new Pt)}catch(n){t(n)}})),c};return o.isMobXFlow=!0,o}),Li);Ii.bound=_(Ci);var Ki={has:function(t,n){return Yt(t).ot(n)},get:function(t,n){return Yt(t).lt(n)},set:function(t,n,i){var r;return!!u(n)&&(null==(r=Yt(t).dt(n,i,!0))||r)},deleteProperty:function(t,n){var i;return!!u(n)&&(null==(i=Yt(t).Gt(n,!0))||i)},defineProperty:function(t,n,i){var r;return null==(r=Yt(t).v(n,i))||r},ownKeys:function(t){return Yt(t).st()},preventExtensions:function(){t(13)}},Pi=Symbol(\"mobx-keys\"),Di=\"update\",Bi={get:function(t,n){var i=t[Wn];return n===Wn?i:\"length\"===n?i.Ht():\"string\"!=typeof n||isNaN(n)?b(qi,n)?qi[n]:t[n]:i.lt(parseInt(n))},set:function(t,n,i){var r=t[Wn];return\"length\"===n&&r.Xt(i),\"symbol\"==typeof n||isNaN(n)?t[n]=i:r.dt(parseInt(n),i),!0},preventExtensions:function(){t(15)}},Wi=function(){function n(t,n,i,r){void 0===t&&(t=\"ObservableArray\"),this.Ut=void 0,this.Ft=void 0,this.ht=void 0,this.j=[],this.ft=void 0,this.ct=void 0,this.zt=void 0,this.dehancer=void 0,this.l=void 0,this.$t=0,this.Ut=i,this.Ft=r,this.ht=new qn(t),this.zt=function(t,i){return n(t,i,\"ObservableArray[..]\")}}var i=n.prototype;return i.Jt=function(t){return void 0!==this.dehancer?this.dehancer(t):t},i.vt=function(t){return void 0!==this.dehancer&&t.length>0?t.map(this.dehancer):t},i._t=function(t){return Zt(this,t)},i.gt=function(t,n){return void 0===n&&(n=!1),n&&t({observableKind:\"array\",object:this.l,debugObjectName:this.ht.et,type:\"splice\",index:0,added:this.j.slice(),addedCount:this.j.length,removed:[],removedCount:0}),rn(this,t)},i.Ht=function(){return this.ht.reportObserved(),this.j.length},i.Xt=function(n){(\"number\"!=typeof n||isNaN(n)||n<0)&&t(\"Out of range: \"+n);var i=this.j.length;if(n!==i)if(n>i){for(var r=new Array(n-i),e=0;e<n-i;e++)r[e]=void 0;this.at(i,0,r)}else this.at(n,i-n)},i.Yt=function(n,i){n!==this.$t&&t(16),this.$t+=i,this.Ft&&i>0&&yn(n+i+1)},i.at=function(t,n,i){var r=this,e=this.j.length;if(void 0===t?t=0:t>e?t=e:t<0&&(t=Math.max(0,e+t)),n=1===arguments.length?e-t:null==n?0:Math.max(0,Math.min(n,e-t)),void 0===i&&(i=Tn),Qt(this)){var u=tn(this,{object:this.l,type:\"splice\",index:t,removedCount:n,added:i});if(!u)return Tn;n=u.removedCount,i=u.added}if(i=0===i.length?i:i.map((function(t){return r.zt(t,void 0)})),this.Ft){var o=i.length-n;this.Yt(e,o)}var s=this.Qt(t,n,i);return 0===n&&0===i.length||this.Zt(t,i,s),this.vt(s)},i.Qt=function(t,n,i){var r;if(i.length<1e4)return(r=this.j).splice.apply(r,[t,n].concat(i));var e=this.j.slice(t,t+n),u=this.j.slice(t+n);this.j.length+=i.length-n;for(var o=0;o<i.length;o++)this.j[t+o]=i[o];for(var s=0;s<u.length;s++)this.j[t+i.length+s]=u[s];return e},i.tn=function(t,n,i){var r=!this.Ut&&!1,e=nn(this),u=e||r?{observableKind:\"array\",object:this.l,type:Di,debugObjectName:this.ht.et,index:t,newValue:n,oldValue:i}:null;this.ht.reportChanged(),e&&en(this,u)},i.Zt=function(t,n,i){var r=!this.Ut&&!1,e=nn(this),u=e||r?{observableKind:\"array\",object:this.l,debugObjectName:this.ht.et,type:\"splice\",index:t,removed:i,added:n,removedCount:i.length,addedCount:n.length}:null;this.ht.reportChanged(),e&&en(this,u)},i.lt=function(t){if(!(this.Ft&&t>=this.j.length))return this.ht.reportObserved(),this.Jt(this.j[t]);console.warn(\"[mobx] Out of bounds read: \"+t)},i.dt=function(n,i){var r=this.j;if(this.Ft&&n>r.length&&t(17,n,r.length),n<r.length){var e=r[n];if(Qt(this)){var u=tn(this,{type:Di,object:this.l,index:n,newValue:i});if(!u)return;i=u.newValue}(i=this.zt(i,e))!==e&&(r[n]=i,this.tn(n,i,e))}else{for(var o=new Array(n+1-r.length),s=0;s<o.length-1;s++)o[s]=void 0;o[o.length-1]=i,this.at(r.length,0,o)}},n}(),qi={clear:function(){return this.splice(0)},replace:function(t){var n=this[Wn];return n.at(0,n.j.length,t)},toJSON:function(){return this.slice()},splice:function(t,n){for(var i=arguments.length,r=new Array(i>2?i-2:0),e=2;e<i;e++)r[e-2]=arguments[e];var u=this[Wn];switch(arguments.length){case 0:return[];case 1:return u.at(t);case 2:return u.at(t,n)}return u.at(t,n,r)},spliceWithArray:function(t,n,i){return this[Wn].at(t,n,i)},push:function(){for(var t=this[Wn],n=arguments.length,i=new Array(n),r=0;r<n;r++)i[r]=arguments[r];return t.at(t.j.length,0,i),t.j.length},pop:function(){return this.splice(Math.max(this[Wn].j.length-1,0),1)[0]},shift:function(){return this.splice(0,1)[0]},unshift:function(){for(var t=this[Wn],n=arguments.length,i=new Array(n),r=0;r<n;r++)i[r]=arguments[r];return t.at(0,0,i),t.j.length},reverse:function(){return xi.trackingDerivation&&t(37,\"reverse\"),this.replace(this.slice().reverse()),this},sort:function(){xi.trackingDerivation&&t(37,\"sort\");var n=this.slice();return n.sort.apply(n,arguments),this.replace(n),this},remove:function(t){var n=this[Wn],i=n.vt(n.j).indexOf(t);return i>-1&&(this.splice(i,1),!0)}};on(\"at\",sn),on(\"concat\",sn),on(\"flat\",sn),on(\"includes\",sn),on(\"indexOf\",sn),on(\"join\",sn),on(\"lastIndexOf\",sn),on(\"slice\",sn),on(\"toString\",sn),on(\"toLocaleString\",sn),on(\"toSorted\",sn),on(\"toSpliced\",sn),on(\"with\",sn),on(\"every\",fn),on(\"filter\",fn),on(\"find\",fn),on(\"findIndex\",fn),on(\"findLast\",fn),on(\"findLastIndex\",fn),on(\"flatMap\",fn),on(\"forEach\",fn),on(\"map\",fn),on(\"some\",fn),on(\"toReversed\",fn),on(\"reduce\",cn),on(\"reduceRight\",cn);var Gi,Hi,Xi=h(\"ObservableArrayAdministration\",Wi),Ui={},Fi=\"add\";Gi=Symbol.iterator,Hi=Symbol.toStringTag;var zi,$i,Ji=function(){function n(n,i,r){var u=this;void 0===i&&(i=M),void 0===r&&(r=\"ObservableMap\"),this.zt=void 0,this.et=void 0,this[Wn]=Ui,this.pt=void 0,this.yt=void 0,this.bt=void 0,this.ft=void 0,this.ct=void 0,this.dehancer=void 0,this.zt=i,this.et=r,e(Map)||t(18),xn((function(){u.bt=S(\"ObservableMap.keys()\"),u.pt=new Map,u.yt=new Map,n&&u.merge(n)}))}var i=n.prototype;return i.ot=function(t){return this.pt.has(t)},i.has=function(t){var n=this;if(!xi.trackingDerivation)return this.ot(t);var i=this.yt.get(t);if(!i){var r=i=new hi(this.ot(t),E,\"ObservableMap.key?\",!1);this.yt.set(t,r),kt(r,(function(){return n.yt.delete(t)}))}return i.get()},i.set=function(t,n){var i=this.ot(t);if(Qt(this)){var r=tn(this,{type:i?Di:Fi,object:this,newValue:n,name:t});if(!r)return this;n=r.newValue}return i?this.nn(t,n):this.in(t,n),this},i.delete=function(t){var n=this;if(Qt(this)&&!tn(this,{type:\"delete\",object:this,name:t}))return!1;if(this.ot(t)){var i=nn(this),r=i?{observableKind:\"map\",debugObjectName:this.et,type:\"delete\",object:this,oldValue:this.pt.get(t).jt,name:t}:null;return zt((function(){var i;n.bt.reportChanged(),null==(i=n.yt.get(t))||i.xt(!1),n.pt.get(t).xt(void 0),n.pt.delete(t)})),i&&en(this,r),!0}return!1},i.nn=function(t,n){var i=this.pt.get(t);if((n=i.Ot(n))!==xi.UNCHANGED){var r=nn(this),e=r?{observableKind:\"map\",debugObjectName:this.et,type:Di,object:this,oldValue:i.jt,name:t,newValue:n}:null;i.xt(n),r&&en(this,e)}},i.in=function(t,n){var i=this;zt((function(){var r,e=new hi(n,i.zt,\"ObservableMap.key\",!1);i.pt.set(t,e),n=e.jt,null==(r=i.yt.get(t))||r.xt(!0),i.bt.reportChanged()}));var r=nn(this);r&&en(this,r?{observableKind:\"map\",debugObjectName:this.et,type:Fi,object:this,name:t,newValue:n}:null)},i.get=function(t){return this.has(t)?this.Jt(this.pt.get(t).get()):this.Jt(void 0)},i.Jt=function(t){return void 0!==this.dehancer?this.dehancer(t):t},i.keys=function(){return this.bt.reportObserved(),this.pt.keys()},i.values=function(){var t=this,n=this.keys();return An({next:function(){var i=n.next(),r=i.done;return{done:r,value:r?void 0:t.get(i.value)}}})},i.entries=function(){var t=this,n=this.keys();return An({next:function(){var i=n.next(),r=i.done,e=i.value;return{done:r,value:r?void 0:[e,t.get(e)]}}})},i[Gi]=function(){return this.entries()},i.forEach=function(t,n){for(var i,r=x(this);!(i=r()).done;){var e=i.value;t.call(n,e[1],e[0],this)}},i.merge=function(n){var i=this;return Yi(n)&&(n=new Map(n)),zt((function(){s(n)?function(t){var n=Object.keys(t);if(!Kn)return n;var i=Object.getOwnPropertySymbols(t);return i.length?[].concat(n,i.filter((function(n){return Rn.propertyIsEnumerable.call(t,n)}))):n}(n).forEach((function(t){return i.set(t,n[t])})):Array.isArray(n)?n.forEach((function(t){return i.set(t[0],t[1])})):v(n)?(n.constructor!==Map&&t(19,n),n.forEach((function(t,n){return i.set(n,t)}))):null!=n&&t(20,n)})),this},i.clear=function(){var t=this;zt((function(){ht((function(){for(var n,i=x(t.keys());!(n=i()).done;)t.delete(n.value)}))}))},i.replace=function(n){var i=this;return zt((function(){for(var r,e=function(n){if(v(n)||Yi(n))return n;if(Array.isArray(n))return new Map(n);if(s(n)){var i=new Map;for(var r in n)i.set(r,n[r]);return i}return t(21,n)}(n),u=new Map,o=!1,f=x(i.pt.keys());!(r=f()).done;){var c=r.value;if(!e.has(c))if(i.delete(c))o=!0;else{var a=i.pt.get(c);u.set(c,a)}}for(var h,l=x(e.entries());!(h=l()).done;){var d=h.value,b=d[0],p=d[1],y=i.pt.has(b);if(i.set(b,p),i.pt.has(b)){var m=i.pt.get(b);u.set(b,m),y||(o=!0)}}if(!o)if(i.pt.size!==u.size)i.bt.reportChanged();else for(var w=i.pt.keys(),j=u.keys(),O=w.next(),_=j.next();!O.done;){if(O.value!==_.value){i.bt.reportChanged();break}O=w.next(),_=j.next()}i.pt=u})),this},i.toString=function(){return\"[object ObservableMap]\"},i.toJSON=function(){return Array.from(this)},i.gt=function(t){return rn(this,t)},i._t=function(t){return Zt(this,t)},y(n,[{key:\"size\",get:function(){return this.bt.reportObserved(),this.pt.size}},{key:Hi,get:function(){return\"Map\"}}]),n}(),Yi=h(\"ObservableMap\",Ji),Qi={};zi=Symbol.iterator,$i=Symbol.toStringTag;var Zi,tr,nr=function(){function n(n,i,r){var u=this;void 0===i&&(i=M),void 0===r&&(r=\"ObservableSet\"),this.et=void 0,this[Wn]=Qi,this.pt=new Set,this.ht=void 0,this.ct=void 0,this.ft=void 0,this.dehancer=void 0,this.zt=void 0,this.et=r,e(Set)||t(22),this.zt=function(t,n){return i(t,n,r)},xn((function(){u.ht=S(u.et),n&&u.replace(n)}))}var i=n.prototype;return i.Jt=function(t){return void 0!==this.dehancer?this.dehancer(t):t},i.clear=function(){var t=this;zt((function(){ht((function(){for(var n,i=x(t.pt.values());!(n=i()).done;)t.delete(n.value)}))}))},i.forEach=function(t,n){for(var i,r=x(this);!(i=r()).done;){var e=i.value;t.call(n,e,e,this)}},i.add=function(t){var n=this;if(Qt(this)&&!tn(this,{type:Fi,object:this,newValue:t}))return this;if(!this.has(t)){zt((function(){n.pt.add(n.zt(t,void 0)),n.ht.reportChanged()}));var i=nn(this);i&&en(this,i?{observableKind:\"set\",debugObjectName:this.et,type:Fi,object:this,newValue:t}:null)}return this},i.delete=function(t){var n=this;if(Qt(this)&&!tn(this,{type:\"delete\",object:this,oldValue:t}))return!1;if(this.has(t)){var i=nn(this),r=i?{observableKind:\"set\",debugObjectName:this.et,type:\"delete\",object:this,oldValue:t}:null;return zt((function(){n.ht.reportChanged(),n.pt.delete(t)})),i&&en(this,r),!0}return!1},i.has=function(t){return this.ht.reportObserved(),this.pt.has(this.Jt(t))},i.entries=function(){var t=0,n=Array.from(this.keys()),i=Array.from(this.values());return An({next:function(){var r=t;return t+=1,r<i.length?{value:[n[r],i[r]],done:!1}:{done:!0}}})},i.keys=function(){return this.values()},i.values=function(){this.ht.reportObserved();var t=this,n=0,i=Array.from(this.pt.values());return An({next:function(){return n<i.length?{value:t.Jt(i[n++]),done:!1}:{done:!0}}})},i.replace=function(n){var i=this;return ir(n)&&(n=new Set(n)),zt((function(){Array.isArray(n)||l(n)?(i.clear(),n.forEach((function(t){return i.add(t)}))):null!=n&&t(\"Cannot initialize set from \"+n)})),this},i.gt=function(t){return rn(this,t)},i._t=function(t){return Zt(this,t)},i.toJSON=function(){return Array.from(this)},i.toString=function(){return\"[object ObservableSet]\"},i[zi]=function(){return this.values()},y(n,[{key:\"size\",get:function(){return this.ht.reportObserved(),this.pt.size}},{key:$i,get:function(){return\"Set\"}}]),n}(),ir=h(\"ObservableSet\",nr),rr=Object.create(null),er=function(){function n(t,n,i,r){void 0===n&&(n=new Map),void 0===r&&(r=Un),this.h=void 0,this.j=void 0,this.et=void 0,this.rn=void 0,this.bt=void 0,this.ct=void 0,this.ft=void 0,this.l=void 0,this.p=void 0,this.en=void 0,this.un=void 0,this.h=t,this.j=n,this.et=i,this.rn=r,this.bt=new qn(\"ObservableObject.keys\"),this.p=s(this.h)}var i=n.prototype;return i.O=function(t){return this.j.get(t).get()},i.g=function(t,n){var i=this.j.get(t);if(i instanceof bi)return i.set(n),!0;if(Qt(this)){var r=tn(this,{type:Di,object:this.l||this.h,name:t,newValue:n});if(!r)return null;n=r.newValue}if((n=i.Ot(n))!==xi.UNCHANGED){var e=nn(this),u=e?{type:Di,observableKind:\"object\",debugObjectName:this.et,object:this.l||this.h,oldValue:i.jt,name:t,newValue:n}:null;i.xt(n),e&&en(this,u)}return!0},i.lt=function(t){return xi.trackingDerivation&&!b(this.h,t)&&this.ot(t),this.h[t]},i.dt=function(t,n,i){return void 0===i&&(i=!1),b(this.h,t)?this.j.has(t)?this.g(t,n):i?Reflect.set(this.h,t,n):(this.h[t]=n,!0):this.s(t,{value:n,enumerable:!0,writable:!0,configurable:!0},this.rn,i)},i.ot=function(t){if(!xi.trackingDerivation)return t in this.h;this.un||(this.un=new Map);var n=this.un.get(t);return n||(n=new hi(t in this.h,E,\"ObservableObject.key?\",!1),this.un.set(t,n)),n.get()},i.o=function(n,i){if(!0===i&&(i=this.rn),!1!==i){if(!(n in this.h)){var r;if(null!=(r=this.h[Bn])&&r[n])return;t(1,i.i,this.et+\".\"+n.toString())}for(var e=this.h;e&&e!==Rn;){var u=Nn(e,n);if(u){var o=i.o(this,n,u,e);if(0===o)return;if(1===o)break}e=Object.getPrototypeOf(e)}dn(this,0,n)}},i.s=function(t,n,i,r){if(void 0===r&&(r=!1),!0===i&&(i=this.rn),!1===i)return this.v(t,n,r);var e=i.s(this,t,n,r);return e&&dn(this,0,t),e},i.v=function(t,n,i){void 0===i&&(i=!1);try{jt();var r=this.Gt(t);if(!r)return r;if(Qt(this)){var e=tn(this,{object:this.l||this.h,name:t,type:Fi,newValue:n.value});if(!e)return null;var u=e.newValue;n.value!==u&&(n=m({},n,{value:u}))}if(i){if(!Reflect.defineProperty(this.h,t,n))return!1}else Vn(this.h,t,n);this.on(t,n.value)}finally{Ot()}return!0},i._=function(t,n,i,r){void 0===r&&(r=!1);try{jt();var e=this.Gt(t);if(!e)return e;if(Qt(this)){var u=tn(this,{object:this.l||this.h,name:t,type:Fi,newValue:n});if(!u)return null;n=u.newValue}var o=vn(t),s={configurable:!xi.safeDescriptors||this.p,enumerable:!0,get:o.get,set:o.set};if(r){if(!Reflect.defineProperty(this.h,t,s))return!1}else Vn(this.h,t,s);var f=new hi(n,i,\"ObservableObject.key\",!1);this.j.set(t,f),this.on(t,f.jt)}finally{Ot()}return!0},i.m=function(t,n,i){void 0===i&&(i=!1);try{jt();var r=this.Gt(t);if(!r)return r;if(Qt(this)&&!tn(this,{object:this.l||this.h,name:t,type:Fi,newValue:void 0}))return null;n.name||(n.name=\"ObservableObject.key\"),n.context=this.l||this.h;var e=vn(t),u={configurable:!xi.safeDescriptors||this.p,enumerable:!1,get:e.get,set:e.set};if(i){if(!Reflect.defineProperty(this.h,t,u))return!1}else Vn(this.h,t,u);this.j.set(t,new bi(n)),this.on(t,void 0)}finally{Ot()}return!0},i.Gt=function(t,n){if(void 0===n&&(n=!1),!b(this.h,t))return!0;if(Qt(this)&&!tn(this,{object:this.l||this.h,name:t,type:\"remove\"}))return null;try{var i,r;jt();var e,u=nn(this),o=this.j.get(t),s=void 0;if(!o&&u&&(s=null==(e=Nn(this.h,t))?void 0:e.value),n){if(!Reflect.deleteProperty(this.h,t))return!1}else delete this.h[t];o&&(this.j.delete(t),o instanceof hi&&(s=o.jt),_t(o)),this.bt.reportChanged(),null==(i=this.un)||null==(r=i.get(t))||r.set(t in this.h),u&&u&&en(this,{type:\"remove\",observableKind:\"object\",object:this.l||this.h,debugObjectName:this.et,oldValue:s,name:t})}finally{Ot()}return!0},i.gt=function(t){return rn(this,t)},i._t=function(t){return Zt(this,t)},i.on=function(t,n){var i,r,e=nn(this);e&&e&&en(this,e?{type:Fi,observableKind:\"object\",debugObjectName:this.et,object:this.l||this.h,name:t,newValue:n}:null),null==(i=this.un)||null==(r=i.get(t))||r.set(!0),this.bt.reportChanged()},i.st=function(){return this.bt.reportObserved(),Pn(this.h)},i.ut=function(){return this.bt.reportObserved(),Object.keys(this.h)},n}(),ur=h(\"ObservableObjectAdministration\",er),or=bn(0),sr=function(){var t=!1,n={};return Object.defineProperty(n,\"0\",{set:function(){t=!0}}),Object.create(n)[0]=1,!1===t}(),fr=0,cr=function(){};Zi=cr,tr=Array.prototype,Object.setPrototypeOf?Object.setPrototypeOf(Zi.prototype,tr):void 0!==Zi.prototype.__proto__?Zi.prototype.__proto__=tr:Zi.prototype=tr;var ar=function(t,n,i){function r(n,i,r,e){var u;return void 0===r&&(r=\"ObservableArray\"),void 0===e&&(e=!1),u=t.call(this)||this,xn((function(){var t=new Wi(r,i,e,!0);t.l=j(u),a(j(u),Wn,t),n&&n.length&&u.spliceWithArray(0,0,n),sr&&Object.defineProperty(j(u),\"0\",or)})),u}w(r,t);var e=r.prototype;return e.concat=function(){this[Wn].ht.reportObserved();for(var t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];return Array.prototype.concat.apply(this.slice(),n.map((function(t){return an(t)?t.slice():t})))},e[i]=function(){var t=this,n=0;return An({next:function(){return n<t.length?{value:t[n++],done:!1}:{done:!0,value:void 0}}})},y(r,[{key:\"length\",get:function(){return this[Wn].Ht()},set:function(t){this[Wn].Xt(t)}},{key:n,get:function(){return\"Array\"}}]),r}(cr,Symbol.toStringTag,Symbol.iterator);Object.entries(qi).forEach((function(t){var n=t[0];\"concat\"!==n&&c(ar.prototype,n,t[1])})),yn(1e3);var hr=Rn.toString;[\"Symbol\",\"Map\",\"Set\"].forEach((function(i){void 0===n()[i]&&t(\"MobX requires global '\"+i+\"' to be available or polyfilled\")})),\"object\"==typeof __MOBX_DEVTOOLS_GLOBAL_HOOK__&&__MOBX_DEVTOOLS_GLOBAL_HOOK__.injectMobx({spy:St,extras:{getDebugName:On},$mobx:Wn}),exports.$mobx=Wn,exports.FlowCancellationError=Pt,exports.ObservableMap=Ji,exports.ObservableSet=nr,exports.Reaction=_i,exports._allowStateChanges=et,exports._allowStateChangesInsideComputed=Et,exports._allowStateReadsEnd=bt,exports._allowStateReadsStart=dt,exports._autoAction=Ri,exports._endAction=rt,exports._getAdministration=jn,exports._getGlobalState=function(){return xi},exports._interceptReads=function(t,n,i){var r;return Yi(t)||an(t)||vi(t)?r=jn(t):ln(t)&&(r=jn(t,n)),r.dehancer=\"function\"==typeof n?n:i,function(){r.dehancer=void 0}},exports._isComputingDerivation=function(){return null!==xi.trackingDerivation},exports._resetGlobalState=function(){var t=new wi;for(var n in t)-1===mi.indexOf(n)&&(xi[n]=t[n]);xi.allowStateChanges=!xi.enforceActions},exports._startAction=it,exports.action=Vi,exports.autorun=Vt,exports.comparer=Hn,exports.computed=ei,exports.configure=function(i){!0===i.isolateGlobalState&&function(){if((xi.pendingReactions.length||xi.inBatch||xi.isRunningReactions)&&t(36),Oi=!0,ji){var i=n();0==--i.__mobxInstanceCount&&(i.__mobxGlobals=void 0),xi=new wi}}();var r,e,u=i.useProxies,o=i.enforceActions;if(void 0!==u&&(xi.useProxies=\"always\"===u||\"never\"!==u&&\"undefined\"!=typeof Proxy),\"ifavailable\"===u&&(xi.verifyProxies=!0),void 0!==o){var s=\"always\"===o?\"always\":\"observed\"===o;xi.enforceActions=s,xi.allowStateChanges=!0!==s&&\"always\"!==s}[\"computedRequiresReaction\",\"reactionRequiresObservable\",\"observableRequiresReaction\",\"disableErrorBoundaries\",\"safeDescriptors\"].forEach((function(t){t in i&&(xi[t]=!!i[t])})),xi.allowStateReads=!xi.observableRequiresReaction,i.reactionScheduler&&(r=i.reactionScheduler,e=gi,gi=function(t){return r((function(){return e(t)}))})},exports.createAtom=S,exports.defineProperty=function(n,i,r){if(ln(n))return n[Wn].v(i,r);t(39)},exports.entries=function(n){return ln(n)?Ht(n).map((function(t){return[t,n[t]]})):Yi(n)?Ht(n).map((function(t){return[t,n.get(t)]})):ir(n)?Array.from(n.entries()):an(n)?n.map((function(t,n){return[n,t]})):void t(7)},exports.extendObservable=Ct,exports.flow=Ii,exports.flowResult=function(t){return t},exports.get=function(n,i){if(Xt(n,i))return ln(n)?n[Wn].lt(i):Yi(n)?n.get(i):an(n)?n[i]:void t(11)},exports.getAtom=wn,exports.getDebugName=On,exports.getDependencyTree=function(t,n){return It(wn(t,n))},exports.getObserverTree=function(t,n){return Kt(wn(t,n))},exports.has=Xt,exports.intercept=function(t,n,i){return e(i)?function(t,n,i){return jn(t,n)._t(i)}(t,n,i):function(t,n){return jn(t)._t(n)}(t,n)},exports.isAction=Nt,exports.isBoxedObservable=vi,exports.isComputed=function(t){return Wt(t)},exports.isComputedProp=function(t,n){return Wt(t,n)},exports.isFlow=Bt,exports.isFlowCancellationError=function(t){return t instanceof Pt},exports.isObservable=Gt,exports.isObservableArray=an,exports.isObservableMap=Yi,exports.isObservableObject=ln,exports.isObservableProp=function(t,n){return qt(t,n)},exports.isObservableSet=ir,exports.keys=Ht,exports.makeAutoObservable=function(t,n,i){return s(t)?Ct(t,t,n,i):(xn((function(){var r=hn(t,i)[Wn];if(!t[Pi]){var e=Object.getPrototypeOf(t),u=new Set([].concat(Pn(t),Pn(e)));u.delete(\"constructor\"),u.delete(Wn),c(e,Pi,u)}t[Pi].forEach((function(t){return r.o(t,!n||!(t in n)||n[t])}))})),t)},exports.makeObservable=function(t,n,i){return xn((function(){var r=hn(t,i)[Wn];null!=n||(n=function(t){return b(t,Bn)||c(t,Bn,m({},t[Bn])),t[Bn]}(t)),Pn(n).forEach((function(t){return r.o(t,n[t])}))})),t},exports.observable=ni,exports.observe=function(t,n,i,r){return e(i)?function(t,n,i,r){return jn(t,n).gt(i,r)}(t,n,i,r):function(t,n,i){return jn(t).gt(n,i)}(t,n,i)},exports.onBecomeObserved=Tt,exports.onBecomeUnobserved=kt,exports.onReactionError=function(t){return xi.globalReactionErrorHandlers.push(t),function(){var n=xi.globalReactionErrorHandlers.indexOf(t);n>=0&&xi.globalReactionErrorHandlers.splice(n,1)}},exports.override=Xn,exports.ownKeys=Ut,exports.reaction=function(t,n,i){function r(){if(y=!1,!w.nt){var n=!1;w.track((function(){var i=et(!1,(function(){return t(w)}));n=p||!m(a,i),h=a,a=i})),(p&&i.fireImmediately||!p&&n)&&l(a,h,w),p=!1}}var e,u,o,s;void 0===i&&(i=kn);var f,c,a,h,v=null!=(e=i.name)?e:\"Reaction\",l=Vi(v,i.onError?(f=i.onError,c=n,function(){try{return c.apply(this,arguments)}catch(t){f.call(this,t)}}):n),d=!i.scheduler&&!i.delay,b=Rt(i),p=!0,y=!1,m=i.compareStructural?Hn.structural:i.equals||Hn.default,w=new _i(v,(function(){p||d?r():y||(y=!0,b(r))}),i.onError,i.requiresObservable);return null!=(u=i)&&null!=(o=u.signal)&&o.aborted||w.it(),w.rt(null==(s=i)?void 0:s.signal)},exports.remove=function(n,i){ln(n)?n[Wn].Gt(i):Yi(n)||ir(n)?n.delete(i):an(n)?(\"number\"!=typeof i&&(i=parseInt(i,10)),n.splice(i,1)):t(9)},exports.runInAction=Et,exports.set=function n(i,r,e){if(2!==arguments.length||ir(i))ln(i)?i[Wn].dt(r,e):Yi(i)?i.set(r,e):ir(i)?i.add(r):an(i)?(\"number\"!=typeof r&&(r=parseInt(r,10)),r<0&&t(\"Invalid index: '\"+r+\"'\"),jt(),r>=i.length&&(i.length=r+1),i[r]=e,Ot()):t(8);else{jt();var u=r;try{for(var o in u)n(i,o,u[o])}finally{Ot()}}},exports.spy=St,exports.toJS=function(t){return function t(n,i){if(null==n||\"object\"!=typeof n||n instanceof Date||!Gt(n))return n;if(vi(n)||pi(n))return t(n.get(),i);if(i.has(n))return i.get(n);if(an(n)){var r=Ft(i,n,new Array(n.length));return n.forEach((function(n,e){r[e]=t(n,i)})),r}if(ir(n)){var e=Ft(i,n,new Set);return n.forEach((function(n){e.add(t(n,i))})),e}if(Yi(n)){var u=Ft(i,n,new Map);return n.forEach((function(n,r){u.set(r,t(n,i))})),u}var o=Ft(i,n,{});return Ut(n).forEach((function(r){Rn.propertyIsEnumerable.call(n,r)&&(o[r]=t(n[r],i))})),o}(t,new Map)},exports.trace=function(){},exports.transaction=zt,exports.untracked=ht,exports.values=function(n){return ln(n)?Ht(n).map((function(t){return n[t]})):Yi(n)?Ht(n).map((function(t){return n.get(t)})):ir(n)?Array.from(n.values()):an(n)?n.slice():void t(6)},exports.when=function(t,n,i){return 1===arguments.length||n&&\"object\"==typeof n?Jt(t,n):$t(t,n,i||{})};\n//# sourceMappingURL=mobx.cjs.production.min.js.map\n", "\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\nvar niceErrors = {\n  0: \"Invalid value for configuration 'enforceActions', expected 'never', 'always' or 'observed'\",\n  1: function _(annotationType, key) {\n    return \"Cannot apply '\" + annotationType + \"' to '\" + key.toString() + \"': Field not found.\";\n  },\n  /*\n  2(prop) {\n      return `invalid decorator for '${prop.toString()}'`\n  },\n  3(prop) {\n      return `Cannot decorate '${prop.toString()}': action can only be used on properties with a function value.`\n  },\n  4(prop) {\n      return `Cannot decorate '${prop.toString()}': computed can only be used on getter properties.`\n  },\n  */\n  5: \"'keys()' can only be used on observable objects, arrays, sets and maps\",\n  6: \"'values()' can only be used on observable objects, arrays, sets and maps\",\n  7: \"'entries()' can only be used on observable objects, arrays and maps\",\n  8: \"'set()' can only be used on observable objects, arrays and maps\",\n  9: \"'remove()' can only be used on observable objects, arrays and maps\",\n  10: \"'has()' can only be used on observable objects, arrays and maps\",\n  11: \"'get()' can only be used on observable objects, arrays and maps\",\n  12: \"Invalid annotation\",\n  13: \"Dynamic observable objects cannot be frozen. If you're passing observables to 3rd party component/function that calls Object.freeze, pass copy instead: toJS(observable)\",\n  14: \"Intercept handlers should return nothing or a change object\",\n  15: \"Observable arrays cannot be frozen. If you're passing observables to 3rd party component/function that calls Object.freeze, pass copy instead: toJS(observable)\",\n  16: \"Modification exception: the internal structure of an observable array was changed.\",\n  17: function _(index, length) {\n    return \"[mobx.array] Index out of bounds, \" + index + \" is larger than \" + length;\n  },\n  18: \"mobx.map requires Map polyfill for the current browser. Check babel-polyfill or core-js/es6/map.js\",\n  19: function _(other) {\n    return \"Cannot initialize from classes that inherit from Map: \" + other.constructor.name;\n  },\n  20: function _(other) {\n    return \"Cannot initialize map from \" + other;\n  },\n  21: function _(dataStructure) {\n    return \"Cannot convert to map from '\" + dataStructure + \"'\";\n  },\n  22: \"mobx.set requires Set polyfill for the current browser. Check babel-polyfill or core-js/es6/set.js\",\n  23: \"It is not possible to get index atoms from arrays\",\n  24: function _(thing) {\n    return \"Cannot obtain administration from \" + thing;\n  },\n  25: function _(property, name) {\n    return \"the entry '\" + property + \"' does not exist in the observable map '\" + name + \"'\";\n  },\n  26: \"please specify a property\",\n  27: function _(property, name) {\n    return \"no observable property '\" + property.toString() + \"' found on the observable object '\" + name + \"'\";\n  },\n  28: function _(thing) {\n    return \"Cannot obtain atom from \" + thing;\n  },\n  29: \"Expecting some object\",\n  30: \"invalid action stack. did you forget to finish an action?\",\n  31: \"missing option for computed: get\",\n  32: function _(name, derivation) {\n    return \"Cycle detected in computation \" + name + \": \" + derivation;\n  },\n  33: function _(name) {\n    return \"The setter of computed value '\" + name + \"' is trying to update itself. Did you intend to update an _observable_ value, instead of the computed property?\";\n  },\n  34: function _(name) {\n    return \"[ComputedValue '\" + name + \"'] It is not possible to assign a new value to a computed value.\";\n  },\n  35: \"There are multiple, different versions of MobX active. Make sure MobX is loaded only once or use `configure({ isolateGlobalState: true })`\",\n  36: \"isolateGlobalState should be called before MobX is running any reactions\",\n  37: function _(method) {\n    return \"[mobx] `observableArray.\" + method + \"()` mutates the array in-place, which is not allowed inside a derivation. Use `array.slice().\" + method + \"()` instead\";\n  },\n  38: \"'ownKeys()' can only be used on observable objects\",\n  39: \"'defineProperty()' can only be used on observable objects\"\n};\nvar errors =  niceErrors ;\nfunction die(error) {\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  {\n    var e = typeof error === \"string\" ? error : errors[error];\n    if (typeof e === \"function\") e = e.apply(null, args);\n    throw new Error(\"[MobX] \" + e);\n  }\n}\n\nvar mockGlobal = {};\nfunction getGlobal() {\n  if (typeof globalThis !== \"undefined\") {\n    return globalThis;\n  }\n  if (typeof window !== \"undefined\") {\n    return window;\n  }\n  if (typeof global !== \"undefined\") {\n    return global;\n  }\n  if (typeof self !== \"undefined\") {\n    return self;\n  }\n  return mockGlobal;\n}\n\n// We shorten anything used > 5 times\nvar assign = Object.assign;\nvar getDescriptor = Object.getOwnPropertyDescriptor;\nvar defineProperty = Object.defineProperty;\nvar objectPrototype = Object.prototype;\nvar EMPTY_ARRAY = [];\nObject.freeze(EMPTY_ARRAY);\nvar EMPTY_OBJECT = {};\nObject.freeze(EMPTY_OBJECT);\nvar hasProxy = typeof Proxy !== \"undefined\";\nvar plainObjectString = /*#__PURE__*/Object.toString();\nfunction assertProxies() {\n  if (!hasProxy) {\n    die( \"`Proxy` objects are not available in the current environment. Please configure MobX to enable a fallback implementation.`\" );\n  }\n}\nfunction warnAboutProxyRequirement(msg) {\n  if ( globalState.verifyProxies) {\n    die(\"MobX is currently configured to be able to run in ES5 mode, but in ES5 MobX won't be able to \" + msg);\n  }\n}\nfunction getNextId() {\n  return ++globalState.mobxGuid;\n}\n/**\n * Makes sure that the provided function is invoked at most once.\n */\nfunction once(func) {\n  var invoked = false;\n  return function () {\n    if (invoked) {\n      return;\n    }\n    invoked = true;\n    return func.apply(this, arguments);\n  };\n}\nvar noop = function noop() {};\nfunction isFunction(fn) {\n  return typeof fn === \"function\";\n}\nfunction isStringish(value) {\n  var t = typeof value;\n  switch (t) {\n    case \"string\":\n    case \"symbol\":\n    case \"number\":\n      return true;\n  }\n  return false;\n}\nfunction isObject(value) {\n  return value !== null && typeof value === \"object\";\n}\nfunction isPlainObject(value) {\n  if (!isObject(value)) {\n    return false;\n  }\n  var proto = Object.getPrototypeOf(value);\n  if (proto == null) {\n    return true;\n  }\n  var protoConstructor = Object.hasOwnProperty.call(proto, \"constructor\") && proto.constructor;\n  return typeof protoConstructor === \"function\" && protoConstructor.toString() === plainObjectString;\n}\n// https://stackoverflow.com/a/37865170\nfunction isGenerator(obj) {\n  var constructor = obj == null ? void 0 : obj.constructor;\n  if (!constructor) {\n    return false;\n  }\n  if (\"GeneratorFunction\" === constructor.name || \"GeneratorFunction\" === constructor.displayName) {\n    return true;\n  }\n  return false;\n}\nfunction addHiddenProp(object, propName, value) {\n  defineProperty(object, propName, {\n    enumerable: false,\n    writable: true,\n    configurable: true,\n    value: value\n  });\n}\nfunction addHiddenFinalProp(object, propName, value) {\n  defineProperty(object, propName, {\n    enumerable: false,\n    writable: false,\n    configurable: true,\n    value: value\n  });\n}\nfunction createInstanceofPredicate(name, theClass) {\n  var propName = \"isMobX\" + name;\n  theClass.prototype[propName] = true;\n  return function (x) {\n    return isObject(x) && x[propName] === true;\n  };\n}\nfunction isES6Map(thing) {\n  return thing instanceof Map;\n}\nfunction isES6Set(thing) {\n  return thing instanceof Set;\n}\nvar hasGetOwnPropertySymbols = typeof Object.getOwnPropertySymbols !== \"undefined\";\n/**\n * Returns the following: own enumerable keys and symbols.\n */\nfunction getPlainObjectKeys(object) {\n  var keys = Object.keys(object);\n  // Not supported in IE, so there are not going to be symbol props anyway...\n  if (!hasGetOwnPropertySymbols) {\n    return keys;\n  }\n  var symbols = Object.getOwnPropertySymbols(object);\n  if (!symbols.length) {\n    return keys;\n  }\n  return [].concat(keys, symbols.filter(function (s) {\n    return objectPrototype.propertyIsEnumerable.call(object, s);\n  }));\n}\n// From Immer utils\n// Returns all own keys, including non-enumerable and symbolic\nvar ownKeys = typeof Reflect !== \"undefined\" && Reflect.ownKeys ? Reflect.ownKeys : hasGetOwnPropertySymbols ? function (obj) {\n  return Object.getOwnPropertyNames(obj).concat(Object.getOwnPropertySymbols(obj));\n} : /* istanbul ignore next */Object.getOwnPropertyNames;\nfunction stringifyKey(key) {\n  if (typeof key === \"string\") {\n    return key;\n  }\n  if (typeof key === \"symbol\") {\n    return key.toString();\n  }\n  return new String(key).toString();\n}\nfunction toPrimitive(value) {\n  return value === null ? null : typeof value === \"object\" ? \"\" + value : value;\n}\nfunction hasProp(target, prop) {\n  return objectPrototype.hasOwnProperty.call(target, prop);\n}\n// From Immer utils\nvar getOwnPropertyDescriptors = Object.getOwnPropertyDescriptors || function getOwnPropertyDescriptors(target) {\n  // Polyfill needed for Hermes and IE, see https://github.com/facebook/hermes/issues/274\n  var res = {};\n  // Note: without polyfill for ownKeys, symbols won't be picked up\n  ownKeys(target).forEach(function (key) {\n    res[key] = getDescriptor(target, key);\n  });\n  return res;\n};\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _createForOfIteratorHelperLoose(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (it) return (it = it.call(o)).next.bind(it);\n  if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      if (i >= o.length) return {\n        done: true\n      };\n      return {\n        done: false,\n        value: o[i++]\n      };\n    };\n  }\n  throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _toPrimitive(input, hint) {\n  if (typeof input !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\n\nvar storedAnnotationsSymbol = /*#__PURE__*/Symbol(\"mobx-stored-annotations\");\n/**\n * Creates a function that acts as\n * - decorator\n * - annotation object\n */\nfunction createDecoratorAnnotation(annotation) {\n  function decorator(target, property) {\n    if (is20223Decorator(property)) {\n      return annotation.decorate_20223_(target, property);\n    } else {\n      storeAnnotation(target, property, annotation);\n    }\n  }\n  return Object.assign(decorator, annotation);\n}\n/**\n * Stores annotation to prototype,\n * so it can be inspected later by `makeObservable` called from constructor\n */\nfunction storeAnnotation(prototype, key, annotation) {\n  if (!hasProp(prototype, storedAnnotationsSymbol)) {\n    addHiddenProp(prototype, storedAnnotationsSymbol, _extends({}, prototype[storedAnnotationsSymbol]));\n  }\n  // @override must override something\n  if ( isOverride(annotation) && !hasProp(prototype[storedAnnotationsSymbol], key)) {\n    var fieldName = prototype.constructor.name + \".prototype.\" + key.toString();\n    die(\"'\" + fieldName + \"' is decorated with 'override', \" + \"but no such decorated member was found on prototype.\");\n  }\n  // Cannot re-decorate\n  assertNotDecorated(prototype, annotation, key);\n  // Ignore override\n  if (!isOverride(annotation)) {\n    prototype[storedAnnotationsSymbol][key] = annotation;\n  }\n}\nfunction assertNotDecorated(prototype, annotation, key) {\n  if ( !isOverride(annotation) && hasProp(prototype[storedAnnotationsSymbol], key)) {\n    var fieldName = prototype.constructor.name + \".prototype.\" + key.toString();\n    var currentAnnotationType = prototype[storedAnnotationsSymbol][key].annotationType_;\n    var requestedAnnotationType = annotation.annotationType_;\n    die(\"Cannot apply '@\" + requestedAnnotationType + \"' to '\" + fieldName + \"':\" + (\"\\nThe field is already decorated with '@\" + currentAnnotationType + \"'.\") + \"\\nRe-decorating fields is not allowed.\" + \"\\nUse '@override' decorator for methods overridden by subclass.\");\n  }\n}\n/**\n * Collects annotations from prototypes and stores them on target (instance)\n */\nfunction collectStoredAnnotations(target) {\n  if (!hasProp(target, storedAnnotationsSymbol)) {\n    // if (__DEV__ && !target[storedAnnotationsSymbol]) {\n    //     die(\n    //         `No annotations were passed to makeObservable, but no decorated members have been found either`\n    //     )\n    // }\n    // We need a copy as we will remove annotation from the list once it's applied.\n    addHiddenProp(target, storedAnnotationsSymbol, _extends({}, target[storedAnnotationsSymbol]));\n  }\n  return target[storedAnnotationsSymbol];\n}\nfunction is20223Decorator(context) {\n  return typeof context == \"object\" && typeof context[\"kind\"] == \"string\";\n}\nfunction assert20223DecoratorType(context, types) {\n  if ( !types.includes(context.kind)) {\n    die(\"The decorator applied to '\" + String(context.name) + \"' cannot be used on a \" + context.kind + \" element\");\n  }\n}\n\nvar $mobx = /*#__PURE__*/Symbol(\"mobx administration\");\nvar Atom = /*#__PURE__*/function () {\n  // for effective unobserving. BaseAtom has true, for extra optimization, so its onBecomeUnobserved never gets called, because it's not needed\n\n  /**\n   * Create a new atom. For debugging purposes it is recommended to give it a name.\n   * The onBecomeObserved and onBecomeUnobserved callbacks can be used for resource management.\n   */\n  function Atom(name_) {\n    if (name_ === void 0) {\n      name_ =  \"Atom@\" + getNextId() ;\n    }\n    this.name_ = void 0;\n    this.isPendingUnobservation_ = false;\n    this.isBeingObserved_ = false;\n    this.observers_ = new Set();\n    this.diffValue_ = 0;\n    this.lastAccessedBy_ = 0;\n    this.lowestObserverState_ = IDerivationState_.NOT_TRACKING_;\n    this.onBOL = void 0;\n    this.onBUOL = void 0;\n    this.name_ = name_;\n  }\n  // onBecomeObservedListeners\n  var _proto = Atom.prototype;\n  _proto.onBO = function onBO() {\n    if (this.onBOL) {\n      this.onBOL.forEach(function (listener) {\n        return listener();\n      });\n    }\n  };\n  _proto.onBUO = function onBUO() {\n    if (this.onBUOL) {\n      this.onBUOL.forEach(function (listener) {\n        return listener();\n      });\n    }\n  }\n  /**\n   * Invoke this method to notify mobx that your atom has been used somehow.\n   * Returns true if there is currently a reactive context.\n   */;\n  _proto.reportObserved = function reportObserved$1() {\n    return reportObserved(this);\n  }\n  /**\n   * Invoke this method _after_ this method has changed to signal mobx that all its observers should invalidate.\n   */;\n  _proto.reportChanged = function reportChanged() {\n    startBatch();\n    propagateChanged(this);\n    endBatch();\n  };\n  _proto.toString = function toString() {\n    return this.name_;\n  };\n  return Atom;\n}();\nvar isAtom = /*#__PURE__*/createInstanceofPredicate(\"Atom\", Atom);\nfunction createAtom(name, onBecomeObservedHandler, onBecomeUnobservedHandler) {\n  if (onBecomeObservedHandler === void 0) {\n    onBecomeObservedHandler = noop;\n  }\n  if (onBecomeUnobservedHandler === void 0) {\n    onBecomeUnobservedHandler = noop;\n  }\n  var atom = new Atom(name);\n  // default `noop` listener will not initialize the hook Set\n  if (onBecomeObservedHandler !== noop) {\n    onBecomeObserved(atom, onBecomeObservedHandler);\n  }\n  if (onBecomeUnobservedHandler !== noop) {\n    onBecomeUnobserved(atom, onBecomeUnobservedHandler);\n  }\n  return atom;\n}\n\nfunction identityComparer(a, b) {\n  return a === b;\n}\nfunction structuralComparer(a, b) {\n  return deepEqual(a, b);\n}\nfunction shallowComparer(a, b) {\n  return deepEqual(a, b, 1);\n}\nfunction defaultComparer(a, b) {\n  if (Object.is) {\n    return Object.is(a, b);\n  }\n  return a === b ? a !== 0 || 1 / a === 1 / b : a !== a && b !== b;\n}\nvar comparer = {\n  identity: identityComparer,\n  structural: structuralComparer,\n  \"default\": defaultComparer,\n  shallow: shallowComparer\n};\n\nfunction deepEnhancer(v, _, name) {\n  // it is an observable already, done\n  if (isObservable(v)) {\n    return v;\n  }\n  // something that can be converted and mutated?\n  if (Array.isArray(v)) {\n    return observable.array(v, {\n      name: name\n    });\n  }\n  if (isPlainObject(v)) {\n    return observable.object(v, undefined, {\n      name: name\n    });\n  }\n  if (isES6Map(v)) {\n    return observable.map(v, {\n      name: name\n    });\n  }\n  if (isES6Set(v)) {\n    return observable.set(v, {\n      name: name\n    });\n  }\n  if (typeof v === \"function\" && !isAction(v) && !isFlow(v)) {\n    if (isGenerator(v)) {\n      return flow(v);\n    } else {\n      return autoAction(name, v);\n    }\n  }\n  return v;\n}\nfunction shallowEnhancer(v, _, name) {\n  if (v === undefined || v === null) {\n    return v;\n  }\n  if (isObservableObject(v) || isObservableArray(v) || isObservableMap(v) || isObservableSet(v)) {\n    return v;\n  }\n  if (Array.isArray(v)) {\n    return observable.array(v, {\n      name: name,\n      deep: false\n    });\n  }\n  if (isPlainObject(v)) {\n    return observable.object(v, undefined, {\n      name: name,\n      deep: false\n    });\n  }\n  if (isES6Map(v)) {\n    return observable.map(v, {\n      name: name,\n      deep: false\n    });\n  }\n  if (isES6Set(v)) {\n    return observable.set(v, {\n      name: name,\n      deep: false\n    });\n  }\n  {\n    die(\"The shallow modifier / decorator can only used in combination with arrays, objects, maps and sets\");\n  }\n}\nfunction referenceEnhancer(newValue) {\n  // never turn into an observable\n  return newValue;\n}\nfunction refStructEnhancer(v, oldValue) {\n  if ( isObservable(v)) {\n    die(\"observable.struct should not be used with observable values\");\n  }\n  if (deepEqual(v, oldValue)) {\n    return oldValue;\n  }\n  return v;\n}\n\nvar OVERRIDE = \"override\";\nvar override = /*#__PURE__*/createDecoratorAnnotation({\n  annotationType_: OVERRIDE,\n  make_: make_,\n  extend_: extend_,\n  decorate_20223_: decorate_20223_\n});\nfunction isOverride(annotation) {\n  return annotation.annotationType_ === OVERRIDE;\n}\nfunction make_(adm, key) {\n  // Must not be plain object\n  if ( adm.isPlainObject_) {\n    die(\"Cannot apply '\" + this.annotationType_ + \"' to '\" + adm.name_ + \".\" + key.toString() + \"':\" + (\"\\n'\" + this.annotationType_ + \"' cannot be used on plain objects.\"));\n  }\n  // Must override something\n  if ( !hasProp(adm.appliedAnnotations_, key)) {\n    die(\"'\" + adm.name_ + \".\" + key.toString() + \"' is annotated with '\" + this.annotationType_ + \"', \" + \"but no such annotated member was found on prototype.\");\n  }\n  return 0 /* MakeResult.Cancel */;\n}\n\nfunction extend_(adm, key, descriptor, proxyTrap) {\n  die(\"'\" + this.annotationType_ + \"' can only be used with 'makeObservable'\");\n}\nfunction decorate_20223_(desc, context) {\n  console.warn(\"'\" + this.annotationType_ + \"' cannot be used with decorators - this is a no-op\");\n}\n\nfunction createActionAnnotation(name, options) {\n  return {\n    annotationType_: name,\n    options_: options,\n    make_: make_$1,\n    extend_: extend_$1,\n    decorate_20223_: decorate_20223_$1\n  };\n}\nfunction make_$1(adm, key, descriptor, source) {\n  var _this$options_;\n  // bound\n  if ((_this$options_ = this.options_) != null && _this$options_.bound) {\n    return this.extend_(adm, key, descriptor, false) === null ? 0 /* MakeResult.Cancel */ : 1 /* MakeResult.Break */;\n  }\n  // own\n  if (source === adm.target_) {\n    return this.extend_(adm, key, descriptor, false) === null ? 0 /* MakeResult.Cancel */ : 2 /* MakeResult.Continue */;\n  }\n  // prototype\n  if (isAction(descriptor.value)) {\n    // A prototype could have been annotated already by other constructor,\n    // rest of the proto chain must be annotated already\n    return 1 /* MakeResult.Break */;\n  }\n\n  var actionDescriptor = createActionDescriptor(adm, this, key, descriptor, false);\n  defineProperty(source, key, actionDescriptor);\n  return 2 /* MakeResult.Continue */;\n}\n\nfunction extend_$1(adm, key, descriptor, proxyTrap) {\n  var actionDescriptor = createActionDescriptor(adm, this, key, descriptor);\n  return adm.defineProperty_(key, actionDescriptor, proxyTrap);\n}\nfunction decorate_20223_$1(mthd, context) {\n  {\n    assert20223DecoratorType(context, [\"method\", \"field\"]);\n  }\n  var kind = context.kind,\n    name = context.name,\n    addInitializer = context.addInitializer;\n  var ann = this;\n  var _createAction = function _createAction(m) {\n    var _ann$options_$name, _ann$options_, _ann$options_$autoAct, _ann$options_2;\n    return createAction((_ann$options_$name = (_ann$options_ = ann.options_) == null ? void 0 : _ann$options_.name) != null ? _ann$options_$name : name.toString(), m, (_ann$options_$autoAct = (_ann$options_2 = ann.options_) == null ? void 0 : _ann$options_2.autoAction) != null ? _ann$options_$autoAct : false);\n  };\n  // Backwards/Legacy behavior, expects makeObservable(this)\n  if (kind == \"field\") {\n    addInitializer(function () {\n      storeAnnotation(this, name, ann);\n    });\n    return;\n  }\n  if (kind == \"method\") {\n    var _this$options_2;\n    if (!isAction(mthd)) {\n      mthd = _createAction(mthd);\n    }\n    if ((_this$options_2 = this.options_) != null && _this$options_2.bound) {\n      addInitializer(function () {\n        var self = this;\n        var bound = self[name].bind(self);\n        bound.isMobxAction = true;\n        self[name] = bound;\n      });\n    }\n    return mthd;\n  }\n  die(\"Cannot apply '\" + ann.annotationType_ + \"' to '\" + String(name) + \"' (kind: \" + kind + \"):\" + (\"\\n'\" + ann.annotationType_ + \"' can only be used on properties with a function value.\"));\n}\nfunction assertActionDescriptor(adm, _ref, key, _ref2) {\n  var annotationType_ = _ref.annotationType_;\n  var value = _ref2.value;\n  if ( !isFunction(value)) {\n    die(\"Cannot apply '\" + annotationType_ + \"' to '\" + adm.name_ + \".\" + key.toString() + \"':\" + (\"\\n'\" + annotationType_ + \"' can only be used on properties with a function value.\"));\n  }\n}\nfunction createActionDescriptor(adm, annotation, key, descriptor,\n// provides ability to disable safeDescriptors for prototypes\nsafeDescriptors) {\n  var _annotation$options_, _annotation$options_$, _annotation$options_2, _annotation$options_$2, _annotation$options_3, _annotation$options_4, _adm$proxy_2;\n  if (safeDescriptors === void 0) {\n    safeDescriptors = globalState.safeDescriptors;\n  }\n  assertActionDescriptor(adm, annotation, key, descriptor);\n  var value = descriptor.value;\n  if ((_annotation$options_ = annotation.options_) != null && _annotation$options_.bound) {\n    var _adm$proxy_;\n    value = value.bind((_adm$proxy_ = adm.proxy_) != null ? _adm$proxy_ : adm.target_);\n  }\n  return {\n    value: createAction((_annotation$options_$ = (_annotation$options_2 = annotation.options_) == null ? void 0 : _annotation$options_2.name) != null ? _annotation$options_$ : key.toString(), value, (_annotation$options_$2 = (_annotation$options_3 = annotation.options_) == null ? void 0 : _annotation$options_3.autoAction) != null ? _annotation$options_$2 : false,\n    // https://github.com/mobxjs/mobx/discussions/3140\n    (_annotation$options_4 = annotation.options_) != null && _annotation$options_4.bound ? (_adm$proxy_2 = adm.proxy_) != null ? _adm$proxy_2 : adm.target_ : undefined),\n    // Non-configurable for classes\n    // prevents accidental field redefinition in subclass\n    configurable: safeDescriptors ? adm.isPlainObject_ : true,\n    // https://github.com/mobxjs/mobx/pull/2641#issuecomment-737292058\n    enumerable: false,\n    // Non-obsevable, therefore non-writable\n    // Also prevents rewriting in subclass constructor\n    writable: safeDescriptors ? false : true\n  };\n}\n\nfunction createFlowAnnotation(name, options) {\n  return {\n    annotationType_: name,\n    options_: options,\n    make_: make_$2,\n    extend_: extend_$2,\n    decorate_20223_: decorate_20223_$2\n  };\n}\nfunction make_$2(adm, key, descriptor, source) {\n  var _this$options_;\n  // own\n  if (source === adm.target_) {\n    return this.extend_(adm, key, descriptor, false) === null ? 0 /* MakeResult.Cancel */ : 2 /* MakeResult.Continue */;\n  }\n  // prototype\n  // bound - must annotate protos to support super.flow()\n  if ((_this$options_ = this.options_) != null && _this$options_.bound && (!hasProp(adm.target_, key) || !isFlow(adm.target_[key]))) {\n    if (this.extend_(adm, key, descriptor, false) === null) {\n      return 0 /* MakeResult.Cancel */;\n    }\n  }\n\n  if (isFlow(descriptor.value)) {\n    // A prototype could have been annotated already by other constructor,\n    // rest of the proto chain must be annotated already\n    return 1 /* MakeResult.Break */;\n  }\n\n  var flowDescriptor = createFlowDescriptor(adm, this, key, descriptor, false, false);\n  defineProperty(source, key, flowDescriptor);\n  return 2 /* MakeResult.Continue */;\n}\n\nfunction extend_$2(adm, key, descriptor, proxyTrap) {\n  var _this$options_2;\n  var flowDescriptor = createFlowDescriptor(adm, this, key, descriptor, (_this$options_2 = this.options_) == null ? void 0 : _this$options_2.bound);\n  return adm.defineProperty_(key, flowDescriptor, proxyTrap);\n}\nfunction decorate_20223_$2(mthd, context) {\n  var _this$options_3;\n  {\n    assert20223DecoratorType(context, [\"method\"]);\n  }\n  var name = context.name,\n    addInitializer = context.addInitializer;\n  if (!isFlow(mthd)) {\n    mthd = flow(mthd);\n  }\n  if ((_this$options_3 = this.options_) != null && _this$options_3.bound) {\n    addInitializer(function () {\n      var self = this;\n      var bound = self[name].bind(self);\n      bound.isMobXFlow = true;\n      self[name] = bound;\n    });\n  }\n  return mthd;\n}\nfunction assertFlowDescriptor(adm, _ref, key, _ref2) {\n  var annotationType_ = _ref.annotationType_;\n  var value = _ref2.value;\n  if ( !isFunction(value)) {\n    die(\"Cannot apply '\" + annotationType_ + \"' to '\" + adm.name_ + \".\" + key.toString() + \"':\" + (\"\\n'\" + annotationType_ + \"' can only be used on properties with a generator function value.\"));\n  }\n}\nfunction createFlowDescriptor(adm, annotation, key, descriptor, bound,\n// provides ability to disable safeDescriptors for prototypes\nsafeDescriptors) {\n  if (safeDescriptors === void 0) {\n    safeDescriptors = globalState.safeDescriptors;\n  }\n  assertFlowDescriptor(adm, annotation, key, descriptor);\n  var value = descriptor.value;\n  // In case of flow.bound, the descriptor can be from already annotated prototype\n  if (!isFlow(value)) {\n    value = flow(value);\n  }\n  if (bound) {\n    var _adm$proxy_;\n    // We do not keep original function around, so we bind the existing flow\n    value = value.bind((_adm$proxy_ = adm.proxy_) != null ? _adm$proxy_ : adm.target_);\n    // This is normally set by `flow`, but `bind` returns new function...\n    value.isMobXFlow = true;\n  }\n  return {\n    value: value,\n    // Non-configurable for classes\n    // prevents accidental field redefinition in subclass\n    configurable: safeDescriptors ? adm.isPlainObject_ : true,\n    // https://github.com/mobxjs/mobx/pull/2641#issuecomment-737292058\n    enumerable: false,\n    // Non-obsevable, therefore non-writable\n    // Also prevents rewriting in subclass constructor\n    writable: safeDescriptors ? false : true\n  };\n}\n\nfunction createComputedAnnotation(name, options) {\n  return {\n    annotationType_: name,\n    options_: options,\n    make_: make_$3,\n    extend_: extend_$3,\n    decorate_20223_: decorate_20223_$3\n  };\n}\nfunction make_$3(adm, key, descriptor) {\n  return this.extend_(adm, key, descriptor, false) === null ? 0 /* MakeResult.Cancel */ : 1 /* MakeResult.Break */;\n}\n\nfunction extend_$3(adm, key, descriptor, proxyTrap) {\n  assertComputedDescriptor(adm, this, key, descriptor);\n  return adm.defineComputedProperty_(key, _extends({}, this.options_, {\n    get: descriptor.get,\n    set: descriptor.set\n  }), proxyTrap);\n}\nfunction decorate_20223_$3(get, context) {\n  {\n    assert20223DecoratorType(context, [\"getter\"]);\n  }\n  var ann = this;\n  var key = context.name,\n    addInitializer = context.addInitializer;\n  addInitializer(function () {\n    var adm = asObservableObject(this)[$mobx];\n    var options = _extends({}, ann.options_, {\n      get: get,\n      context: this\n    });\n    options.name || (options.name =  adm.name_ + \".\" + key.toString() );\n    adm.values_.set(key, new ComputedValue(options));\n  });\n  return function () {\n    return this[$mobx].getObservablePropValue_(key);\n  };\n}\nfunction assertComputedDescriptor(adm, _ref, key, _ref2) {\n  var annotationType_ = _ref.annotationType_;\n  var get = _ref2.get;\n  if ( !get) {\n    die(\"Cannot apply '\" + annotationType_ + \"' to '\" + adm.name_ + \".\" + key.toString() + \"':\" + (\"\\n'\" + annotationType_ + \"' can only be used on getter(+setter) properties.\"));\n  }\n}\n\nfunction createObservableAnnotation(name, options) {\n  return {\n    annotationType_: name,\n    options_: options,\n    make_: make_$4,\n    extend_: extend_$4,\n    decorate_20223_: decorate_20223_$4\n  };\n}\nfunction make_$4(adm, key, descriptor) {\n  return this.extend_(adm, key, descriptor, false) === null ? 0 /* MakeResult.Cancel */ : 1 /* MakeResult.Break */;\n}\n\nfunction extend_$4(adm, key, descriptor, proxyTrap) {\n  var _this$options_$enhanc, _this$options_;\n  assertObservableDescriptor(adm, this, key, descriptor);\n  return adm.defineObservableProperty_(key, descriptor.value, (_this$options_$enhanc = (_this$options_ = this.options_) == null ? void 0 : _this$options_.enhancer) != null ? _this$options_$enhanc : deepEnhancer, proxyTrap);\n}\nfunction decorate_20223_$4(desc, context) {\n  {\n    if (context.kind === \"field\") {\n      throw die(\"Please use `@observable accessor \" + String(context.name) + \"` instead of `@observable \" + String(context.name) + \"`\");\n    }\n    assert20223DecoratorType(context, [\"accessor\"]);\n  }\n  var ann = this;\n  var kind = context.kind,\n    name = context.name;\n  // The laziness here is not ideal... It's a workaround to how 2022.3 Decorators are implemented:\n  //   `addInitializer` callbacks are executed _before_ any accessors are defined (instead of the ideal-for-us right after each).\n  //   This means that, if we were to do our stuff in an `addInitializer`, we'd attempt to read a private slot\n  //   before it has been initialized. The runtime doesn't like that and throws a `Cannot read private member\n  //   from an object whose class did not declare it` error.\n  // TODO: it seems that this will not be required anymore in the final version of the spec\n  // See TODO: link\n  var initializedObjects = new WeakSet();\n  function initializeObservable(target, value) {\n    var _ann$options_$enhance, _ann$options_;\n    var adm = asObservableObject(target)[$mobx];\n    var observable = new ObservableValue(value, (_ann$options_$enhance = (_ann$options_ = ann.options_) == null ? void 0 : _ann$options_.enhancer) != null ? _ann$options_$enhance : deepEnhancer,  adm.name_ + \".\" + name.toString() , false);\n    adm.values_.set(name, observable);\n    initializedObjects.add(target);\n  }\n  if (kind == \"accessor\") {\n    return {\n      get: function get() {\n        if (!initializedObjects.has(this)) {\n          initializeObservable(this, desc.get.call(this));\n        }\n        return this[$mobx].getObservablePropValue_(name);\n      },\n      set: function set(value) {\n        if (!initializedObjects.has(this)) {\n          initializeObservable(this, value);\n        }\n        return this[$mobx].setObservablePropValue_(name, value);\n      },\n      init: function init(value) {\n        if (!initializedObjects.has(this)) {\n          initializeObservable(this, value);\n        }\n        return value;\n      }\n    };\n  }\n  return;\n}\nfunction assertObservableDescriptor(adm, _ref, key, descriptor) {\n  var annotationType_ = _ref.annotationType_;\n  if ( !(\"value\" in descriptor)) {\n    die(\"Cannot apply '\" + annotationType_ + \"' to '\" + adm.name_ + \".\" + key.toString() + \"':\" + (\"\\n'\" + annotationType_ + \"' cannot be used on getter/setter properties\"));\n  }\n}\n\nvar AUTO = \"true\";\nvar autoAnnotation = /*#__PURE__*/createAutoAnnotation();\nfunction createAutoAnnotation(options) {\n  return {\n    annotationType_: AUTO,\n    options_: options,\n    make_: make_$5,\n    extend_: extend_$5,\n    decorate_20223_: decorate_20223_$5\n  };\n}\nfunction make_$5(adm, key, descriptor, source) {\n  var _this$options_3, _this$options_4;\n  // getter -> computed\n  if (descriptor.get) {\n    return computed.make_(adm, key, descriptor, source);\n  }\n  // lone setter -> action setter\n  if (descriptor.set) {\n    // TODO make action applicable to setter and delegate to action.make_\n    var set = createAction(key.toString(), descriptor.set);\n    // own\n    if (source === adm.target_) {\n      return adm.defineProperty_(key, {\n        configurable: globalState.safeDescriptors ? adm.isPlainObject_ : true,\n        set: set\n      }) === null ? 0 /* MakeResult.Cancel */ : 2 /* MakeResult.Continue */;\n    }\n    // proto\n    defineProperty(source, key, {\n      configurable: true,\n      set: set\n    });\n    return 2 /* MakeResult.Continue */;\n  }\n  // function on proto -> autoAction/flow\n  if (source !== adm.target_ && typeof descriptor.value === \"function\") {\n    var _this$options_2;\n    if (isGenerator(descriptor.value)) {\n      var _this$options_;\n      var flowAnnotation = (_this$options_ = this.options_) != null && _this$options_.autoBind ? flow.bound : flow;\n      return flowAnnotation.make_(adm, key, descriptor, source);\n    }\n    var actionAnnotation = (_this$options_2 = this.options_) != null && _this$options_2.autoBind ? autoAction.bound : autoAction;\n    return actionAnnotation.make_(adm, key, descriptor, source);\n  }\n  // other -> observable\n  // Copy props from proto as well, see test:\n  // \"decorate should work with Object.create\"\n  var observableAnnotation = ((_this$options_3 = this.options_) == null ? void 0 : _this$options_3.deep) === false ? observable.ref : observable;\n  // if function respect autoBind option\n  if (typeof descriptor.value === \"function\" && (_this$options_4 = this.options_) != null && _this$options_4.autoBind) {\n    var _adm$proxy_;\n    descriptor.value = descriptor.value.bind((_adm$proxy_ = adm.proxy_) != null ? _adm$proxy_ : adm.target_);\n  }\n  return observableAnnotation.make_(adm, key, descriptor, source);\n}\nfunction extend_$5(adm, key, descriptor, proxyTrap) {\n  var _this$options_5, _this$options_6;\n  // getter -> computed\n  if (descriptor.get) {\n    return computed.extend_(adm, key, descriptor, proxyTrap);\n  }\n  // lone setter -> action setter\n  if (descriptor.set) {\n    // TODO make action applicable to setter and delegate to action.extend_\n    return adm.defineProperty_(key, {\n      configurable: globalState.safeDescriptors ? adm.isPlainObject_ : true,\n      set: createAction(key.toString(), descriptor.set)\n    }, proxyTrap);\n  }\n  // other -> observable\n  // if function respect autoBind option\n  if (typeof descriptor.value === \"function\" && (_this$options_5 = this.options_) != null && _this$options_5.autoBind) {\n    var _adm$proxy_2;\n    descriptor.value = descriptor.value.bind((_adm$proxy_2 = adm.proxy_) != null ? _adm$proxy_2 : adm.target_);\n  }\n  var observableAnnotation = ((_this$options_6 = this.options_) == null ? void 0 : _this$options_6.deep) === false ? observable.ref : observable;\n  return observableAnnotation.extend_(adm, key, descriptor, proxyTrap);\n}\nfunction decorate_20223_$5(desc, context) {\n  die(\"'\" + this.annotationType_ + \"' cannot be used as a decorator\");\n}\n\nvar OBSERVABLE = \"observable\";\nvar OBSERVABLE_REF = \"observable.ref\";\nvar OBSERVABLE_SHALLOW = \"observable.shallow\";\nvar OBSERVABLE_STRUCT = \"observable.struct\";\n// Predefined bags of create observable options, to avoid allocating temporarily option objects\n// in the majority of cases\nvar defaultCreateObservableOptions = {\n  deep: true,\n  name: undefined,\n  defaultDecorator: undefined,\n  proxy: true\n};\nObject.freeze(defaultCreateObservableOptions);\nfunction asCreateObservableOptions(thing) {\n  return thing || defaultCreateObservableOptions;\n}\nvar observableAnnotation = /*#__PURE__*/createObservableAnnotation(OBSERVABLE);\nvar observableRefAnnotation = /*#__PURE__*/createObservableAnnotation(OBSERVABLE_REF, {\n  enhancer: referenceEnhancer\n});\nvar observableShallowAnnotation = /*#__PURE__*/createObservableAnnotation(OBSERVABLE_SHALLOW, {\n  enhancer: shallowEnhancer\n});\nvar observableStructAnnotation = /*#__PURE__*/createObservableAnnotation(OBSERVABLE_STRUCT, {\n  enhancer: refStructEnhancer\n});\nvar observableDecoratorAnnotation = /*#__PURE__*/createDecoratorAnnotation(observableAnnotation);\nfunction getEnhancerFromOptions(options) {\n  return options.deep === true ? deepEnhancer : options.deep === false ? referenceEnhancer : getEnhancerFromAnnotation(options.defaultDecorator);\n}\nfunction getAnnotationFromOptions(options) {\n  var _options$defaultDecor;\n  return options ? (_options$defaultDecor = options.defaultDecorator) != null ? _options$defaultDecor : createAutoAnnotation(options) : undefined;\n}\nfunction getEnhancerFromAnnotation(annotation) {\n  var _annotation$options_$, _annotation$options_;\n  return !annotation ? deepEnhancer : (_annotation$options_$ = (_annotation$options_ = annotation.options_) == null ? void 0 : _annotation$options_.enhancer) != null ? _annotation$options_$ : deepEnhancer;\n}\n/**\n * Turns an object, array or function into a reactive structure.\n * @param v the value which should become observable.\n */\nfunction createObservable(v, arg2, arg3) {\n  // @observable someProp; (2022.3 Decorators)\n  if (is20223Decorator(arg2)) {\n    return observableAnnotation.decorate_20223_(v, arg2);\n  }\n  // @observable someProp;\n  if (isStringish(arg2)) {\n    storeAnnotation(v, arg2, observableAnnotation);\n    return;\n  }\n  // already observable - ignore\n  if (isObservable(v)) {\n    return v;\n  }\n  // plain object\n  if (isPlainObject(v)) {\n    return observable.object(v, arg2, arg3);\n  }\n  // Array\n  if (Array.isArray(v)) {\n    return observable.array(v, arg2);\n  }\n  // Map\n  if (isES6Map(v)) {\n    return observable.map(v, arg2);\n  }\n  // Set\n  if (isES6Set(v)) {\n    return observable.set(v, arg2);\n  }\n  // other object - ignore\n  if (typeof v === \"object\" && v !== null) {\n    return v;\n  }\n  // anything else\n  return observable.box(v, arg2);\n}\nassign(createObservable, observableDecoratorAnnotation);\nvar observableFactories = {\n  box: function box(value, options) {\n    var o = asCreateObservableOptions(options);\n    return new ObservableValue(value, getEnhancerFromOptions(o), o.name, true, o.equals);\n  },\n  array: function array(initialValues, options) {\n    var o = asCreateObservableOptions(options);\n    return (globalState.useProxies === false || o.proxy === false ? createLegacyArray : createObservableArray)(initialValues, getEnhancerFromOptions(o), o.name);\n  },\n  map: function map(initialValues, options) {\n    var o = asCreateObservableOptions(options);\n    return new ObservableMap(initialValues, getEnhancerFromOptions(o), o.name);\n  },\n  set: function set(initialValues, options) {\n    var o = asCreateObservableOptions(options);\n    return new ObservableSet(initialValues, getEnhancerFromOptions(o), o.name);\n  },\n  object: function object(props, decorators, options) {\n    return initObservable(function () {\n      return extendObservable(globalState.useProxies === false || (options == null ? void 0 : options.proxy) === false ? asObservableObject({}, options) : asDynamicObservableObject({}, options), props, decorators);\n    });\n  },\n  ref: /*#__PURE__*/createDecoratorAnnotation(observableRefAnnotation),\n  shallow: /*#__PURE__*/createDecoratorAnnotation(observableShallowAnnotation),\n  deep: observableDecoratorAnnotation,\n  struct: /*#__PURE__*/createDecoratorAnnotation(observableStructAnnotation)\n};\n// eslint-disable-next-line\nvar observable = /*#__PURE__*/assign(createObservable, observableFactories);\n\nvar COMPUTED = \"computed\";\nvar COMPUTED_STRUCT = \"computed.struct\";\nvar computedAnnotation = /*#__PURE__*/createComputedAnnotation(COMPUTED);\nvar computedStructAnnotation = /*#__PURE__*/createComputedAnnotation(COMPUTED_STRUCT, {\n  equals: comparer.structural\n});\n/**\n * Decorator for class properties: @computed get value() { return expr; }.\n * For legacy purposes also invokable as ES5 observable created: `computed(() => expr)`;\n */\nvar computed = function computed(arg1, arg2) {\n  if (is20223Decorator(arg2)) {\n    // @computed (2022.3 Decorators)\n    return computedAnnotation.decorate_20223_(arg1, arg2);\n  }\n  if (isStringish(arg2)) {\n    // @computed\n    return storeAnnotation(arg1, arg2, computedAnnotation);\n  }\n  if (isPlainObject(arg1)) {\n    // @computed({ options })\n    return createDecoratorAnnotation(createComputedAnnotation(COMPUTED, arg1));\n  }\n  // computed(expr, options?)\n  {\n    if (!isFunction(arg1)) {\n      die(\"First argument to `computed` should be an expression.\");\n    }\n    if (isFunction(arg2)) {\n      die(\"A setter as second argument is no longer supported, use `{ set: fn }` option instead\");\n    }\n  }\n  var opts = isPlainObject(arg2) ? arg2 : {};\n  opts.get = arg1;\n  opts.name || (opts.name = arg1.name || \"\"); /* for generated name */\n  return new ComputedValue(opts);\n};\nObject.assign(computed, computedAnnotation);\ncomputed.struct = /*#__PURE__*/createDecoratorAnnotation(computedStructAnnotation);\n\nvar _getDescriptor$config, _getDescriptor;\n// we don't use globalState for these in order to avoid possible issues with multiple\n// mobx versions\nvar currentActionId = 0;\nvar nextActionId = 1;\nvar isFunctionNameConfigurable = (_getDescriptor$config = (_getDescriptor = /*#__PURE__*/getDescriptor(function () {}, \"name\")) == null ? void 0 : _getDescriptor.configurable) != null ? _getDescriptor$config : false;\n// we can safely recycle this object\nvar tmpNameDescriptor = {\n  value: \"action\",\n  configurable: true,\n  writable: false,\n  enumerable: false\n};\nfunction createAction(actionName, fn, autoAction, ref) {\n  if (autoAction === void 0) {\n    autoAction = false;\n  }\n  {\n    if (!isFunction(fn)) {\n      die(\"`action` can only be invoked on functions\");\n    }\n    if (typeof actionName !== \"string\" || !actionName) {\n      die(\"actions should have valid names, got: '\" + actionName + \"'\");\n    }\n  }\n  function res() {\n    return executeAction(actionName, autoAction, fn, ref || this, arguments);\n  }\n  res.isMobxAction = true;\n  res.toString = function () {\n    return fn.toString();\n  };\n  if (isFunctionNameConfigurable) {\n    tmpNameDescriptor.value = actionName;\n    defineProperty(res, \"name\", tmpNameDescriptor);\n  }\n  return res;\n}\nfunction executeAction(actionName, canRunAsDerivation, fn, scope, args) {\n  var runInfo = _startAction(actionName, canRunAsDerivation, scope, args);\n  try {\n    return fn.apply(scope, args);\n  } catch (err) {\n    runInfo.error_ = err;\n    throw err;\n  } finally {\n    _endAction(runInfo);\n  }\n}\nfunction _startAction(actionName, canRunAsDerivation,\n// true for autoAction\nscope, args) {\n  var notifySpy_ =  isSpyEnabled() && !!actionName;\n  var startTime_ = 0;\n  if ( notifySpy_) {\n    startTime_ = Date.now();\n    var flattenedArgs = args ? Array.from(args) : EMPTY_ARRAY;\n    spyReportStart({\n      type: ACTION,\n      name: actionName,\n      object: scope,\n      arguments: flattenedArgs\n    });\n  }\n  var prevDerivation_ = globalState.trackingDerivation;\n  var runAsAction = !canRunAsDerivation || !prevDerivation_;\n  startBatch();\n  var prevAllowStateChanges_ = globalState.allowStateChanges; // by default preserve previous allow\n  if (runAsAction) {\n    untrackedStart();\n    prevAllowStateChanges_ = allowStateChangesStart(true);\n  }\n  var prevAllowStateReads_ = allowStateReadsStart(true);\n  var runInfo = {\n    runAsAction_: runAsAction,\n    prevDerivation_: prevDerivation_,\n    prevAllowStateChanges_: prevAllowStateChanges_,\n    prevAllowStateReads_: prevAllowStateReads_,\n    notifySpy_: notifySpy_,\n    startTime_: startTime_,\n    actionId_: nextActionId++,\n    parentActionId_: currentActionId\n  };\n  currentActionId = runInfo.actionId_;\n  return runInfo;\n}\nfunction _endAction(runInfo) {\n  if (currentActionId !== runInfo.actionId_) {\n    die(30);\n  }\n  currentActionId = runInfo.parentActionId_;\n  if (runInfo.error_ !== undefined) {\n    globalState.suppressReactionErrors = true;\n  }\n  allowStateChangesEnd(runInfo.prevAllowStateChanges_);\n  allowStateReadsEnd(runInfo.prevAllowStateReads_);\n  endBatch();\n  if (runInfo.runAsAction_) {\n    untrackedEnd(runInfo.prevDerivation_);\n  }\n  if ( runInfo.notifySpy_) {\n    spyReportEnd({\n      time: Date.now() - runInfo.startTime_\n    });\n  }\n  globalState.suppressReactionErrors = false;\n}\nfunction allowStateChanges(allowStateChanges, func) {\n  var prev = allowStateChangesStart(allowStateChanges);\n  try {\n    return func();\n  } finally {\n    allowStateChangesEnd(prev);\n  }\n}\nfunction allowStateChangesStart(allowStateChanges) {\n  var prev = globalState.allowStateChanges;\n  globalState.allowStateChanges = allowStateChanges;\n  return prev;\n}\nfunction allowStateChangesEnd(prev) {\n  globalState.allowStateChanges = prev;\n}\n\nvar _Symbol$toPrimitive;\nvar CREATE = \"create\";\n_Symbol$toPrimitive = Symbol.toPrimitive;\nvar ObservableValue = /*#__PURE__*/function (_Atom) {\n  _inheritsLoose(ObservableValue, _Atom);\n  function ObservableValue(value, enhancer, name_, notifySpy, equals) {\n    var _this;\n    if (name_ === void 0) {\n      name_ =  \"ObservableValue@\" + getNextId() ;\n    }\n    if (notifySpy === void 0) {\n      notifySpy = true;\n    }\n    if (equals === void 0) {\n      equals = comparer[\"default\"];\n    }\n    _this = _Atom.call(this, name_) || this;\n    _this.enhancer = void 0;\n    _this.name_ = void 0;\n    _this.equals = void 0;\n    _this.hasUnreportedChange_ = false;\n    _this.interceptors_ = void 0;\n    _this.changeListeners_ = void 0;\n    _this.value_ = void 0;\n    _this.dehancer = void 0;\n    _this.enhancer = enhancer;\n    _this.name_ = name_;\n    _this.equals = equals;\n    _this.value_ = enhancer(value, undefined, name_);\n    if ( notifySpy && isSpyEnabled()) {\n      // only notify spy if this is a stand-alone observable\n      spyReport({\n        type: CREATE,\n        object: _assertThisInitialized(_this),\n        observableKind: \"value\",\n        debugObjectName: _this.name_,\n        newValue: \"\" + _this.value_\n      });\n    }\n    return _this;\n  }\n  var _proto = ObservableValue.prototype;\n  _proto.dehanceValue = function dehanceValue(value) {\n    if (this.dehancer !== undefined) {\n      return this.dehancer(value);\n    }\n    return value;\n  };\n  _proto.set = function set(newValue) {\n    var oldValue = this.value_;\n    newValue = this.prepareNewValue_(newValue);\n    if (newValue !== globalState.UNCHANGED) {\n      var notifySpy = isSpyEnabled();\n      if ( notifySpy) {\n        spyReportStart({\n          type: UPDATE,\n          object: this,\n          observableKind: \"value\",\n          debugObjectName: this.name_,\n          newValue: newValue,\n          oldValue: oldValue\n        });\n      }\n      this.setNewValue_(newValue);\n      if ( notifySpy) {\n        spyReportEnd();\n      }\n    }\n  };\n  _proto.prepareNewValue_ = function prepareNewValue_(newValue) {\n    checkIfStateModificationsAreAllowed(this);\n    if (hasInterceptors(this)) {\n      var change = interceptChange(this, {\n        object: this,\n        type: UPDATE,\n        newValue: newValue\n      });\n      if (!change) {\n        return globalState.UNCHANGED;\n      }\n      newValue = change.newValue;\n    }\n    // apply modifier\n    newValue = this.enhancer(newValue, this.value_, this.name_);\n    return this.equals(this.value_, newValue) ? globalState.UNCHANGED : newValue;\n  };\n  _proto.setNewValue_ = function setNewValue_(newValue) {\n    var oldValue = this.value_;\n    this.value_ = newValue;\n    this.reportChanged();\n    if (hasListeners(this)) {\n      notifyListeners(this, {\n        type: UPDATE,\n        object: this,\n        newValue: newValue,\n        oldValue: oldValue\n      });\n    }\n  };\n  _proto.get = function get() {\n    this.reportObserved();\n    return this.dehanceValue(this.value_);\n  };\n  _proto.intercept_ = function intercept_(handler) {\n    return registerInterceptor(this, handler);\n  };\n  _proto.observe_ = function observe_(listener, fireImmediately) {\n    if (fireImmediately) {\n      listener({\n        observableKind: \"value\",\n        debugObjectName: this.name_,\n        object: this,\n        type: UPDATE,\n        newValue: this.value_,\n        oldValue: undefined\n      });\n    }\n    return registerListener(this, listener);\n  };\n  _proto.raw = function raw() {\n    // used by MST ot get undehanced value\n    return this.value_;\n  };\n  _proto.toJSON = function toJSON() {\n    return this.get();\n  };\n  _proto.toString = function toString() {\n    return this.name_ + \"[\" + this.value_ + \"]\";\n  };\n  _proto.valueOf = function valueOf() {\n    return toPrimitive(this.get());\n  };\n  _proto[_Symbol$toPrimitive] = function () {\n    return this.valueOf();\n  };\n  return ObservableValue;\n}(Atom);\nvar isObservableValue = /*#__PURE__*/createInstanceofPredicate(\"ObservableValue\", ObservableValue);\n\nvar _Symbol$toPrimitive$1;\n/**\n * A node in the state dependency root that observes other nodes, and can be observed itself.\n *\n * ComputedValue will remember the result of the computation for the duration of the batch, or\n * while being observed.\n *\n * During this time it will recompute only when one of its direct dependencies changed,\n * but only when it is being accessed with `ComputedValue.get()`.\n *\n * Implementation description:\n * 1. First time it's being accessed it will compute and remember result\n *    give back remembered result until 2. happens\n * 2. First time any deep dependency change, propagate POSSIBLY_STALE to all observers, wait for 3.\n * 3. When it's being accessed, recompute if any shallow dependency changed.\n *    if result changed: propagate STALE to all observers, that were POSSIBLY_STALE from the last step.\n *    go to step 2. either way\n *\n * If at any point it's outside batch and it isn't observed: reset everything and go to 1.\n */\n_Symbol$toPrimitive$1 = Symbol.toPrimitive;\nvar ComputedValue = /*#__PURE__*/function () {\n  // nodes we are looking at. Our value depends on these nodes\n  // during tracking it's an array with new observed observers\n\n  // to check for cycles\n\n  // N.B: unminified as it is used by MST\n\n  /**\n   * Create a new computed value based on a function expression.\n   *\n   * The `name` property is for debug purposes only.\n   *\n   * The `equals` property specifies the comparer function to use to determine if a newly produced\n   * value differs from the previous value. Two comparers are provided in the library; `defaultComparer`\n   * compares based on identity comparison (===), and `structuralComparer` deeply compares the structure.\n   * Structural comparison can be convenient if you always produce a new aggregated object and\n   * don't want to notify observers if it is structurally the same.\n   * This is useful for working with vectors, mouse coordinates etc.\n   */\n  function ComputedValue(options) {\n    this.dependenciesState_ = IDerivationState_.NOT_TRACKING_;\n    this.observing_ = [];\n    this.newObserving_ = null;\n    this.isBeingObserved_ = false;\n    this.isPendingUnobservation_ = false;\n    this.observers_ = new Set();\n    this.diffValue_ = 0;\n    this.runId_ = 0;\n    this.lastAccessedBy_ = 0;\n    this.lowestObserverState_ = IDerivationState_.UP_TO_DATE_;\n    this.unboundDepsCount_ = 0;\n    this.value_ = new CaughtException(null);\n    this.name_ = void 0;\n    this.triggeredBy_ = void 0;\n    this.isComputing_ = false;\n    this.isRunningSetter_ = false;\n    this.derivation = void 0;\n    this.setter_ = void 0;\n    this.isTracing_ = TraceMode.NONE;\n    this.scope_ = void 0;\n    this.equals_ = void 0;\n    this.requiresReaction_ = void 0;\n    this.keepAlive_ = void 0;\n    this.onBOL = void 0;\n    this.onBUOL = void 0;\n    if (!options.get) {\n      die(31);\n    }\n    this.derivation = options.get;\n    this.name_ = options.name || ( \"ComputedValue@\" + getNextId() );\n    if (options.set) {\n      this.setter_ = createAction( this.name_ + \"-setter\" , options.set);\n    }\n    this.equals_ = options.equals || (options.compareStructural || options.struct ? comparer.structural : comparer[\"default\"]);\n    this.scope_ = options.context;\n    this.requiresReaction_ = options.requiresReaction;\n    this.keepAlive_ = !!options.keepAlive;\n  }\n  var _proto = ComputedValue.prototype;\n  _proto.onBecomeStale_ = function onBecomeStale_() {\n    propagateMaybeChanged(this);\n  };\n  _proto.onBO = function onBO() {\n    if (this.onBOL) {\n      this.onBOL.forEach(function (listener) {\n        return listener();\n      });\n    }\n  };\n  _proto.onBUO = function onBUO() {\n    if (this.onBUOL) {\n      this.onBUOL.forEach(function (listener) {\n        return listener();\n      });\n    }\n  }\n  /**\n   * Returns the current value of this computed value.\n   * Will evaluate its computation first if needed.\n   */;\n  _proto.get = function get() {\n    if (this.isComputing_) {\n      die(32, this.name_, this.derivation);\n    }\n    if (globalState.inBatch === 0 &&\n    // !globalState.trackingDerivatpion &&\n    this.observers_.size === 0 && !this.keepAlive_) {\n      if (shouldCompute(this)) {\n        this.warnAboutUntrackedRead_();\n        startBatch(); // See perf test 'computed memoization'\n        this.value_ = this.computeValue_(false);\n        endBatch();\n      }\n    } else {\n      reportObserved(this);\n      if (shouldCompute(this)) {\n        var prevTrackingContext = globalState.trackingContext;\n        if (this.keepAlive_ && !prevTrackingContext) {\n          globalState.trackingContext = this;\n        }\n        if (this.trackAndCompute()) {\n          propagateChangeConfirmed(this);\n        }\n        globalState.trackingContext = prevTrackingContext;\n      }\n    }\n    var result = this.value_;\n    if (isCaughtException(result)) {\n      throw result.cause;\n    }\n    return result;\n  };\n  _proto.set = function set(value) {\n    if (this.setter_) {\n      if (this.isRunningSetter_) {\n        die(33, this.name_);\n      }\n      this.isRunningSetter_ = true;\n      try {\n        this.setter_.call(this.scope_, value);\n      } finally {\n        this.isRunningSetter_ = false;\n      }\n    } else {\n      die(34, this.name_);\n    }\n  };\n  _proto.trackAndCompute = function trackAndCompute() {\n    // N.B: unminified as it is used by MST\n    var oldValue = this.value_;\n    var wasSuspended = /* see #1208 */this.dependenciesState_ === IDerivationState_.NOT_TRACKING_;\n    var newValue = this.computeValue_(true);\n    var changed = wasSuspended || isCaughtException(oldValue) || isCaughtException(newValue) || !this.equals_(oldValue, newValue);\n    if (changed) {\n      this.value_ = newValue;\n      if ( isSpyEnabled()) {\n        spyReport({\n          observableKind: \"computed\",\n          debugObjectName: this.name_,\n          object: this.scope_,\n          type: \"update\",\n          oldValue: oldValue,\n          newValue: newValue\n        });\n      }\n    }\n    return changed;\n  };\n  _proto.computeValue_ = function computeValue_(track) {\n    this.isComputing_ = true;\n    // don't allow state changes during computation\n    var prev = allowStateChangesStart(false);\n    var res;\n    if (track) {\n      res = trackDerivedFunction(this, this.derivation, this.scope_);\n    } else {\n      if (globalState.disableErrorBoundaries === true) {\n        res = this.derivation.call(this.scope_);\n      } else {\n        try {\n          res = this.derivation.call(this.scope_);\n        } catch (e) {\n          res = new CaughtException(e);\n        }\n      }\n    }\n    allowStateChangesEnd(prev);\n    this.isComputing_ = false;\n    return res;\n  };\n  _proto.suspend_ = function suspend_() {\n    if (!this.keepAlive_) {\n      clearObserving(this);\n      this.value_ = undefined; // don't hold on to computed value!\n      if ( this.isTracing_ !== TraceMode.NONE) {\n        console.log(\"[mobx.trace] Computed value '\" + this.name_ + \"' was suspended and it will recompute on the next access.\");\n      }\n    }\n  };\n  _proto.observe_ = function observe_(listener, fireImmediately) {\n    var _this = this;\n    var firstTime = true;\n    var prevValue = undefined;\n    return autorun(function () {\n      // TODO: why is this in a different place than the spyReport() function? in all other observables it's called in the same place\n      var newValue = _this.get();\n      if (!firstTime || fireImmediately) {\n        var prevU = untrackedStart();\n        listener({\n          observableKind: \"computed\",\n          debugObjectName: _this.name_,\n          type: UPDATE,\n          object: _this,\n          newValue: newValue,\n          oldValue: prevValue\n        });\n        untrackedEnd(prevU);\n      }\n      firstTime = false;\n      prevValue = newValue;\n    });\n  };\n  _proto.warnAboutUntrackedRead_ = function warnAboutUntrackedRead_() {\n    if (this.isTracing_ !== TraceMode.NONE) {\n      console.log(\"[mobx.trace] Computed value '\" + this.name_ + \"' is being read outside a reactive context. Doing a full recompute.\");\n    }\n    if (typeof this.requiresReaction_ === \"boolean\" ? this.requiresReaction_ : globalState.computedRequiresReaction) {\n      console.warn(\"[mobx] Computed value '\" + this.name_ + \"' is being read outside a reactive context. Doing a full recompute.\");\n    }\n  };\n  _proto.toString = function toString() {\n    return this.name_ + \"[\" + this.derivation.toString() + \"]\";\n  };\n  _proto.valueOf = function valueOf() {\n    return toPrimitive(this.get());\n  };\n  _proto[_Symbol$toPrimitive$1] = function () {\n    return this.valueOf();\n  };\n  return ComputedValue;\n}();\nvar isComputedValue = /*#__PURE__*/createInstanceofPredicate(\"ComputedValue\", ComputedValue);\n\nvar IDerivationState_;\n(function (IDerivationState_) {\n  // before being run or (outside batch and not being observed)\n  // at this point derivation is not holding any data about dependency tree\n  IDerivationState_[IDerivationState_[\"NOT_TRACKING_\"] = -1] = \"NOT_TRACKING_\";\n  // no shallow dependency changed since last computation\n  // won't recalculate derivation\n  // this is what makes mobx fast\n  IDerivationState_[IDerivationState_[\"UP_TO_DATE_\"] = 0] = \"UP_TO_DATE_\";\n  // some deep dependency changed, but don't know if shallow dependency changed\n  // will require to check first if UP_TO_DATE or POSSIBLY_STALE\n  // currently only ComputedValue will propagate POSSIBLY_STALE\n  //\n  // having this state is second big optimization:\n  // don't have to recompute on every dependency change, but only when it's needed\n  IDerivationState_[IDerivationState_[\"POSSIBLY_STALE_\"] = 1] = \"POSSIBLY_STALE_\";\n  // A shallow dependency has changed since last computation and the derivation\n  // will need to recompute when it's needed next.\n  IDerivationState_[IDerivationState_[\"STALE_\"] = 2] = \"STALE_\";\n})(IDerivationState_ || (IDerivationState_ = {}));\nvar TraceMode;\n(function (TraceMode) {\n  TraceMode[TraceMode[\"NONE\"] = 0] = \"NONE\";\n  TraceMode[TraceMode[\"LOG\"] = 1] = \"LOG\";\n  TraceMode[TraceMode[\"BREAK\"] = 2] = \"BREAK\";\n})(TraceMode || (TraceMode = {}));\nvar CaughtException = function CaughtException(cause) {\n  this.cause = void 0;\n  this.cause = cause;\n  // Empty\n};\n\nfunction isCaughtException(e) {\n  return e instanceof CaughtException;\n}\n/**\n * Finds out whether any dependency of the derivation has actually changed.\n * If dependenciesState is 1 then it will recalculate dependencies,\n * if any dependency changed it will propagate it by changing dependenciesState to 2.\n *\n * By iterating over the dependencies in the same order that they were reported and\n * stopping on the first change, all the recalculations are only called for ComputedValues\n * that will be tracked by derivation. That is because we assume that if the first x\n * dependencies of the derivation doesn't change then the derivation should run the same way\n * up until accessing x-th dependency.\n */\nfunction shouldCompute(derivation) {\n  switch (derivation.dependenciesState_) {\n    case IDerivationState_.UP_TO_DATE_:\n      return false;\n    case IDerivationState_.NOT_TRACKING_:\n    case IDerivationState_.STALE_:\n      return true;\n    case IDerivationState_.POSSIBLY_STALE_:\n      {\n        // state propagation can occur outside of action/reactive context #2195\n        var prevAllowStateReads = allowStateReadsStart(true);\n        var prevUntracked = untrackedStart(); // no need for those computeds to be reported, they will be picked up in trackDerivedFunction.\n        var obs = derivation.observing_,\n          l = obs.length;\n        for (var i = 0; i < l; i++) {\n          var obj = obs[i];\n          if (isComputedValue(obj)) {\n            if (globalState.disableErrorBoundaries) {\n              obj.get();\n            } else {\n              try {\n                obj.get();\n              } catch (e) {\n                // we are not interested in the value *or* exception at this moment, but if there is one, notify all\n                untrackedEnd(prevUntracked);\n                allowStateReadsEnd(prevAllowStateReads);\n                return true;\n              }\n            }\n            // if ComputedValue `obj` actually changed it will be computed and propagated to its observers.\n            // and `derivation` is an observer of `obj`\n            // invariantShouldCompute(derivation)\n            if (derivation.dependenciesState_ === IDerivationState_.STALE_) {\n              untrackedEnd(prevUntracked);\n              allowStateReadsEnd(prevAllowStateReads);\n              return true;\n            }\n          }\n        }\n        changeDependenciesStateTo0(derivation);\n        untrackedEnd(prevUntracked);\n        allowStateReadsEnd(prevAllowStateReads);\n        return false;\n      }\n  }\n}\nfunction isComputingDerivation() {\n  return globalState.trackingDerivation !== null; // filter out actions inside computations\n}\n\nfunction checkIfStateModificationsAreAllowed(atom) {\n  var hasObservers = atom.observers_.size > 0;\n  // Should not be possible to change observed state outside strict mode, except during initialization, see #563\n  if (!globalState.allowStateChanges && (hasObservers || globalState.enforceActions === \"always\")) {\n    console.warn(\"[MobX] \" + (globalState.enforceActions ? \"Since strict-mode is enabled, changing (observed) observable values without using an action is not allowed. Tried to modify: \" : \"Side effects like changing state are not allowed at this point. Are you trying to modify state from, for example, a computed value or the render function of a React component? You can wrap side effects in 'runInAction' (or decorate functions with 'action') if needed. Tried to modify: \") + atom.name_);\n  }\n}\nfunction checkIfStateReadsAreAllowed(observable) {\n  if ( !globalState.allowStateReads && globalState.observableRequiresReaction) {\n    console.warn(\"[mobx] Observable '\" + observable.name_ + \"' being read outside a reactive context.\");\n  }\n}\n/**\n * Executes the provided function `f` and tracks which observables are being accessed.\n * The tracking information is stored on the `derivation` object and the derivation is registered\n * as observer of any of the accessed observables.\n */\nfunction trackDerivedFunction(derivation, f, context) {\n  var prevAllowStateReads = allowStateReadsStart(true);\n  // pre allocate array allocation + room for variation in deps\n  // array will be trimmed by bindDependencies\n  changeDependenciesStateTo0(derivation);\n  derivation.newObserving_ = new Array(derivation.observing_.length + 100);\n  derivation.unboundDepsCount_ = 0;\n  derivation.runId_ = ++globalState.runId;\n  var prevTracking = globalState.trackingDerivation;\n  globalState.trackingDerivation = derivation;\n  globalState.inBatch++;\n  var result;\n  if (globalState.disableErrorBoundaries === true) {\n    result = f.call(context);\n  } else {\n    try {\n      result = f.call(context);\n    } catch (e) {\n      result = new CaughtException(e);\n    }\n  }\n  globalState.inBatch--;\n  globalState.trackingDerivation = prevTracking;\n  bindDependencies(derivation);\n  warnAboutDerivationWithoutDependencies(derivation);\n  allowStateReadsEnd(prevAllowStateReads);\n  return result;\n}\nfunction warnAboutDerivationWithoutDependencies(derivation) {\n  if (derivation.observing_.length !== 0) {\n    return;\n  }\n  if (typeof derivation.requiresObservable_ === \"boolean\" ? derivation.requiresObservable_ : globalState.reactionRequiresObservable) {\n    console.warn(\"[mobx] Derivation '\" + derivation.name_ + \"' is created/updated without reading any observable value.\");\n  }\n}\n/**\n * diffs newObserving with observing.\n * update observing to be newObserving with unique observables\n * notify observers that become observed/unobserved\n */\nfunction bindDependencies(derivation) {\n  // invariant(derivation.dependenciesState !== IDerivationState.NOT_TRACKING, \"INTERNAL ERROR bindDependencies expects derivation.dependenciesState !== -1\");\n  var prevObserving = derivation.observing_;\n  var observing = derivation.observing_ = derivation.newObserving_;\n  var lowestNewObservingDerivationState = IDerivationState_.UP_TO_DATE_;\n  // Go through all new observables and check diffValue: (this list can contain duplicates):\n  //   0: first occurrence, change to 1 and keep it\n  //   1: extra occurrence, drop it\n  var i0 = 0,\n    l = derivation.unboundDepsCount_;\n  for (var i = 0; i < l; i++) {\n    var dep = observing[i];\n    if (dep.diffValue_ === 0) {\n      dep.diffValue_ = 1;\n      if (i0 !== i) {\n        observing[i0] = dep;\n      }\n      i0++;\n    }\n    // Upcast is 'safe' here, because if dep is IObservable, `dependenciesState` will be undefined,\n    // not hitting the condition\n    if (dep.dependenciesState_ > lowestNewObservingDerivationState) {\n      lowestNewObservingDerivationState = dep.dependenciesState_;\n    }\n  }\n  observing.length = i0;\n  derivation.newObserving_ = null; // newObserving shouldn't be needed outside tracking (statement moved down to work around FF bug, see #614)\n  // Go through all old observables and check diffValue: (it is unique after last bindDependencies)\n  //   0: it's not in new observables, unobserve it\n  //   1: it keeps being observed, don't want to notify it. change to 0\n  l = prevObserving.length;\n  while (l--) {\n    var _dep = prevObserving[l];\n    if (_dep.diffValue_ === 0) {\n      removeObserver(_dep, derivation);\n    }\n    _dep.diffValue_ = 0;\n  }\n  // Go through all new observables and check diffValue: (now it should be unique)\n  //   0: it was set to 0 in last loop. don't need to do anything.\n  //   1: it wasn't observed, let's observe it. set back to 0\n  while (i0--) {\n    var _dep2 = observing[i0];\n    if (_dep2.diffValue_ === 1) {\n      _dep2.diffValue_ = 0;\n      addObserver(_dep2, derivation);\n    }\n  }\n  // Some new observed derivations may become stale during this derivation computation\n  // so they have had no chance to propagate staleness (#916)\n  if (lowestNewObservingDerivationState !== IDerivationState_.UP_TO_DATE_) {\n    derivation.dependenciesState_ = lowestNewObservingDerivationState;\n    derivation.onBecomeStale_();\n  }\n}\nfunction clearObserving(derivation) {\n  // invariant(globalState.inBatch > 0, \"INTERNAL ERROR clearObserving should be called only inside batch\");\n  var obs = derivation.observing_;\n  derivation.observing_ = [];\n  var i = obs.length;\n  while (i--) {\n    removeObserver(obs[i], derivation);\n  }\n  derivation.dependenciesState_ = IDerivationState_.NOT_TRACKING_;\n}\nfunction untracked(action) {\n  var prev = untrackedStart();\n  try {\n    return action();\n  } finally {\n    untrackedEnd(prev);\n  }\n}\nfunction untrackedStart() {\n  var prev = globalState.trackingDerivation;\n  globalState.trackingDerivation = null;\n  return prev;\n}\nfunction untrackedEnd(prev) {\n  globalState.trackingDerivation = prev;\n}\nfunction allowStateReadsStart(allowStateReads) {\n  var prev = globalState.allowStateReads;\n  globalState.allowStateReads = allowStateReads;\n  return prev;\n}\nfunction allowStateReadsEnd(prev) {\n  globalState.allowStateReads = prev;\n}\n/**\n * needed to keep `lowestObserverState` correct. when changing from (2 or 1) to 0\n *\n */\nfunction changeDependenciesStateTo0(derivation) {\n  if (derivation.dependenciesState_ === IDerivationState_.UP_TO_DATE_) {\n    return;\n  }\n  derivation.dependenciesState_ = IDerivationState_.UP_TO_DATE_;\n  var obs = derivation.observing_;\n  var i = obs.length;\n  while (i--) {\n    obs[i].lowestObserverState_ = IDerivationState_.UP_TO_DATE_;\n  }\n}\n\n/**\n * These values will persist if global state is reset\n */\nvar persistentKeys = [\"mobxGuid\", \"spyListeners\", \"enforceActions\", \"computedRequiresReaction\", \"reactionRequiresObservable\", \"observableRequiresReaction\", \"allowStateReads\", \"disableErrorBoundaries\", \"runId\", \"UNCHANGED\", \"useProxies\"];\nvar MobXGlobals = function MobXGlobals() {\n  this.version = 6;\n  this.UNCHANGED = {};\n  this.trackingDerivation = null;\n  this.trackingContext = null;\n  this.runId = 0;\n  this.mobxGuid = 0;\n  this.inBatch = 0;\n  this.pendingUnobservations = [];\n  this.pendingReactions = [];\n  this.isRunningReactions = false;\n  this.allowStateChanges = false;\n  this.allowStateReads = true;\n  this.enforceActions = true;\n  this.spyListeners = [];\n  this.globalReactionErrorHandlers = [];\n  this.computedRequiresReaction = false;\n  this.reactionRequiresObservable = false;\n  this.observableRequiresReaction = false;\n  this.disableErrorBoundaries = false;\n  this.suppressReactionErrors = false;\n  this.useProxies = true;\n  this.verifyProxies = false;\n  this.safeDescriptors = true;\n};\nvar canMergeGlobalState = true;\nvar isolateCalled = false;\nvar globalState = /*#__PURE__*/function () {\n  var global = /*#__PURE__*/getGlobal();\n  if (global.__mobxInstanceCount > 0 && !global.__mobxGlobals) {\n    canMergeGlobalState = false;\n  }\n  if (global.__mobxGlobals && global.__mobxGlobals.version !== new MobXGlobals().version) {\n    canMergeGlobalState = false;\n  }\n  if (!canMergeGlobalState) {\n    // Because this is a IIFE we need to let isolateCalled a chance to change\n    // so we run it after the event loop completed at least 1 iteration\n    setTimeout(function () {\n      if (!isolateCalled) {\n        die(35);\n      }\n    }, 1);\n    return new MobXGlobals();\n  } else if (global.__mobxGlobals) {\n    global.__mobxInstanceCount += 1;\n    if (!global.__mobxGlobals.UNCHANGED) {\n      global.__mobxGlobals.UNCHANGED = {};\n    } // make merge backward compatible\n    return global.__mobxGlobals;\n  } else {\n    global.__mobxInstanceCount = 1;\n    return global.__mobxGlobals = /*#__PURE__*/new MobXGlobals();\n  }\n}();\nfunction isolateGlobalState() {\n  if (globalState.pendingReactions.length || globalState.inBatch || globalState.isRunningReactions) {\n    die(36);\n  }\n  isolateCalled = true;\n  if (canMergeGlobalState) {\n    var global = getGlobal();\n    if (--global.__mobxInstanceCount === 0) {\n      global.__mobxGlobals = undefined;\n    }\n    globalState = new MobXGlobals();\n  }\n}\nfunction getGlobalState() {\n  return globalState;\n}\n/**\n * For testing purposes only; this will break the internal state of existing observables,\n * but can be used to get back at a stable state after throwing errors\n */\nfunction resetGlobalState() {\n  var defaultGlobals = new MobXGlobals();\n  for (var key in defaultGlobals) {\n    if (persistentKeys.indexOf(key) === -1) {\n      globalState[key] = defaultGlobals[key];\n    }\n  }\n  globalState.allowStateChanges = !globalState.enforceActions;\n}\n\nfunction hasObservers(observable) {\n  return observable.observers_ && observable.observers_.size > 0;\n}\nfunction getObservers(observable) {\n  return observable.observers_;\n}\n// function invariantObservers(observable: IObservable) {\n//     const list = observable.observers\n//     const map = observable.observersIndexes\n//     const l = list.length\n//     for (let i = 0; i < l; i++) {\n//         const id = list[i].__mapid\n//         if (i) {\n//             invariant(map[id] === i, \"INTERNAL ERROR maps derivation.__mapid to index in list\") // for performance\n//         } else {\n//             invariant(!(id in map), \"INTERNAL ERROR observer on index 0 shouldn't be held in map.\") // for performance\n//         }\n//     }\n//     invariant(\n//         list.length === 0 || Object.keys(map).length === list.length - 1,\n//         \"INTERNAL ERROR there is no junk in map\"\n//     )\n// }\nfunction addObserver(observable, node) {\n  // invariant(node.dependenciesState !== -1, \"INTERNAL ERROR, can add only dependenciesState !== -1\");\n  // invariant(observable._observers.indexOf(node) === -1, \"INTERNAL ERROR add already added node\");\n  // invariantObservers(observable);\n  observable.observers_.add(node);\n  if (observable.lowestObserverState_ > node.dependenciesState_) {\n    observable.lowestObserverState_ = node.dependenciesState_;\n  }\n  // invariantObservers(observable);\n  // invariant(observable._observers.indexOf(node) !== -1, \"INTERNAL ERROR didn't add node\");\n}\n\nfunction removeObserver(observable, node) {\n  // invariant(globalState.inBatch > 0, \"INTERNAL ERROR, remove should be called only inside batch\");\n  // invariant(observable._observers.indexOf(node) !== -1, \"INTERNAL ERROR remove already removed node\");\n  // invariantObservers(observable);\n  observable.observers_[\"delete\"](node);\n  if (observable.observers_.size === 0) {\n    // deleting last observer\n    queueForUnobservation(observable);\n  }\n  // invariantObservers(observable);\n  // invariant(observable._observers.indexOf(node) === -1, \"INTERNAL ERROR remove already removed node2\");\n}\n\nfunction queueForUnobservation(observable) {\n  if (observable.isPendingUnobservation_ === false) {\n    // invariant(observable._observers.length === 0, \"INTERNAL ERROR, should only queue for unobservation unobserved observables\");\n    observable.isPendingUnobservation_ = true;\n    globalState.pendingUnobservations.push(observable);\n  }\n}\n/**\n * Batch starts a transaction, at least for purposes of memoizing ComputedValues when nothing else does.\n * During a batch `onBecomeUnobserved` will be called at most once per observable.\n * Avoids unnecessary recalculations.\n */\nfunction startBatch() {\n  globalState.inBatch++;\n}\nfunction endBatch() {\n  if (--globalState.inBatch === 0) {\n    runReactions();\n    // the batch is actually about to finish, all unobserving should happen here.\n    var list = globalState.pendingUnobservations;\n    for (var i = 0; i < list.length; i++) {\n      var observable = list[i];\n      observable.isPendingUnobservation_ = false;\n      if (observable.observers_.size === 0) {\n        if (observable.isBeingObserved_) {\n          // if this observable had reactive observers, trigger the hooks\n          observable.isBeingObserved_ = false;\n          observable.onBUO();\n        }\n        if (observable instanceof ComputedValue) {\n          // computed values are automatically teared down when the last observer leaves\n          // this process happens recursively, this computed might be the last observabe of another, etc..\n          observable.suspend_();\n        }\n      }\n    }\n    globalState.pendingUnobservations = [];\n  }\n}\nfunction reportObserved(observable) {\n  checkIfStateReadsAreAllowed(observable);\n  var derivation = globalState.trackingDerivation;\n  if (derivation !== null) {\n    /**\n     * Simple optimization, give each derivation run an unique id (runId)\n     * Check if last time this observable was accessed the same runId is used\n     * if this is the case, the relation is already known\n     */\n    if (derivation.runId_ !== observable.lastAccessedBy_) {\n      observable.lastAccessedBy_ = derivation.runId_;\n      // Tried storing newObserving, or observing, or both as Set, but performance didn't come close...\n      derivation.newObserving_[derivation.unboundDepsCount_++] = observable;\n      if (!observable.isBeingObserved_ && globalState.trackingContext) {\n        observable.isBeingObserved_ = true;\n        observable.onBO();\n      }\n    }\n    return observable.isBeingObserved_;\n  } else if (observable.observers_.size === 0 && globalState.inBatch > 0) {\n    queueForUnobservation(observable);\n  }\n  return false;\n}\n// function invariantLOS(observable: IObservable, msg: string) {\n//     // it's expensive so better not run it in produciton. but temporarily helpful for testing\n//     const min = getObservers(observable).reduce((a, b) => Math.min(a, b.dependenciesState), 2)\n//     if (min >= observable.lowestObserverState) return // <- the only assumption about `lowestObserverState`\n//     throw new Error(\n//         \"lowestObserverState is wrong for \" +\n//             msg +\n//             \" because \" +\n//             min +\n//             \" < \" +\n//             observable.lowestObserverState\n//     )\n// }\n/**\n * NOTE: current propagation mechanism will in case of self reruning autoruns behave unexpectedly\n * It will propagate changes to observers from previous run\n * It's hard or maybe impossible (with reasonable perf) to get it right with current approach\n * Hopefully self reruning autoruns aren't a feature people should depend on\n * Also most basic use cases should be ok\n */\n// Called by Atom when its value changes\nfunction propagateChanged(observable) {\n  // invariantLOS(observable, \"changed start\");\n  if (observable.lowestObserverState_ === IDerivationState_.STALE_) {\n    return;\n  }\n  observable.lowestObserverState_ = IDerivationState_.STALE_;\n  // Ideally we use for..of here, but the downcompiled version is really slow...\n  observable.observers_.forEach(function (d) {\n    if (d.dependenciesState_ === IDerivationState_.UP_TO_DATE_) {\n      if ( d.isTracing_ !== TraceMode.NONE) {\n        logTraceInfo(d, observable);\n      }\n      d.onBecomeStale_();\n    }\n    d.dependenciesState_ = IDerivationState_.STALE_;\n  });\n  // invariantLOS(observable, \"changed end\");\n}\n// Called by ComputedValue when it recalculate and its value changed\nfunction propagateChangeConfirmed(observable) {\n  // invariantLOS(observable, \"confirmed start\");\n  if (observable.lowestObserverState_ === IDerivationState_.STALE_) {\n    return;\n  }\n  observable.lowestObserverState_ = IDerivationState_.STALE_;\n  observable.observers_.forEach(function (d) {\n    if (d.dependenciesState_ === IDerivationState_.POSSIBLY_STALE_) {\n      d.dependenciesState_ = IDerivationState_.STALE_;\n      if ( d.isTracing_ !== TraceMode.NONE) {\n        logTraceInfo(d, observable);\n      }\n    } else if (d.dependenciesState_ === IDerivationState_.UP_TO_DATE_ // this happens during computing of `d`, just keep lowestObserverState up to date.\n    ) {\n      observable.lowestObserverState_ = IDerivationState_.UP_TO_DATE_;\n    }\n  });\n  // invariantLOS(observable, \"confirmed end\");\n}\n// Used by computed when its dependency changed, but we don't wan't to immediately recompute.\nfunction propagateMaybeChanged(observable) {\n  // invariantLOS(observable, \"maybe start\");\n  if (observable.lowestObserverState_ !== IDerivationState_.UP_TO_DATE_) {\n    return;\n  }\n  observable.lowestObserverState_ = IDerivationState_.POSSIBLY_STALE_;\n  observable.observers_.forEach(function (d) {\n    if (d.dependenciesState_ === IDerivationState_.UP_TO_DATE_) {\n      d.dependenciesState_ = IDerivationState_.POSSIBLY_STALE_;\n      d.onBecomeStale_();\n    }\n  });\n  // invariantLOS(observable, \"maybe end\");\n}\n\nfunction logTraceInfo(derivation, observable) {\n  console.log(\"[mobx.trace] '\" + derivation.name_ + \"' is invalidated due to a change in: '\" + observable.name_ + \"'\");\n  if (derivation.isTracing_ === TraceMode.BREAK) {\n    var lines = [];\n    printDepTree(getDependencyTree(derivation), lines, 1);\n    // prettier-ignore\n    new Function(\"debugger;\\n/*\\nTracing '\" + derivation.name_ + \"'\\n\\nYou are entering this break point because derivation '\" + derivation.name_ + \"' is being traced and '\" + observable.name_ + \"' is now forcing it to update.\\nJust follow the stacktrace you should now see in the devtools to see precisely what piece of your code is causing this update\\nThe stackframe you are looking for is at least ~6-8 stack-frames up.\\n\\n\" + (derivation instanceof ComputedValue ? derivation.derivation.toString().replace(/[*]\\//g, \"/\") : \"\") + \"\\n\\nThe dependencies for this derivation are:\\n\\n\" + lines.join(\"\\n\") + \"\\n*/\\n    \")();\n  }\n}\nfunction printDepTree(tree, lines, depth) {\n  if (lines.length >= 1000) {\n    lines.push(\"(and many more)\");\n    return;\n  }\n  lines.push(\"\" + \"\\t\".repeat(depth - 1) + tree.name);\n  if (tree.dependencies) {\n    tree.dependencies.forEach(function (child) {\n      return printDepTree(child, lines, depth + 1);\n    });\n  }\n}\n\nvar Reaction = /*#__PURE__*/function () {\n  // nodes we are looking at. Our value depends on these nodes\n\n  function Reaction(name_, onInvalidate_, errorHandler_, requiresObservable_) {\n    if (name_ === void 0) {\n      name_ =  \"Reaction@\" + getNextId() ;\n    }\n    this.name_ = void 0;\n    this.onInvalidate_ = void 0;\n    this.errorHandler_ = void 0;\n    this.requiresObservable_ = void 0;\n    this.observing_ = [];\n    this.newObserving_ = [];\n    this.dependenciesState_ = IDerivationState_.NOT_TRACKING_;\n    this.diffValue_ = 0;\n    this.runId_ = 0;\n    this.unboundDepsCount_ = 0;\n    this.isDisposed_ = false;\n    this.isScheduled_ = false;\n    this.isTrackPending_ = false;\n    this.isRunning_ = false;\n    this.isTracing_ = TraceMode.NONE;\n    this.name_ = name_;\n    this.onInvalidate_ = onInvalidate_;\n    this.errorHandler_ = errorHandler_;\n    this.requiresObservable_ = requiresObservable_;\n  }\n  var _proto = Reaction.prototype;\n  _proto.onBecomeStale_ = function onBecomeStale_() {\n    this.schedule_();\n  };\n  _proto.schedule_ = function schedule_() {\n    if (!this.isScheduled_) {\n      this.isScheduled_ = true;\n      globalState.pendingReactions.push(this);\n      runReactions();\n    }\n  };\n  _proto.isScheduled = function isScheduled() {\n    return this.isScheduled_;\n  }\n  /**\n   * internal, use schedule() if you intend to kick off a reaction\n   */;\n  _proto.runReaction_ = function runReaction_() {\n    if (!this.isDisposed_) {\n      startBatch();\n      this.isScheduled_ = false;\n      var prev = globalState.trackingContext;\n      globalState.trackingContext = this;\n      if (shouldCompute(this)) {\n        this.isTrackPending_ = true;\n        try {\n          this.onInvalidate_();\n          if (\"development\" !== \"production\" && this.isTrackPending_ && isSpyEnabled()) {\n            // onInvalidate didn't trigger track right away..\n            spyReport({\n              name: this.name_,\n              type: \"scheduled-reaction\"\n            });\n          }\n        } catch (e) {\n          this.reportExceptionInDerivation_(e);\n        }\n      }\n      globalState.trackingContext = prev;\n      endBatch();\n    }\n  };\n  _proto.track = function track(fn) {\n    if (this.isDisposed_) {\n      return;\n      // console.warn(\"Reaction already disposed\") // Note: Not a warning / error in mobx 4 either\n    }\n\n    startBatch();\n    var notify = isSpyEnabled();\n    var startTime;\n    if ( notify) {\n      startTime = Date.now();\n      spyReportStart({\n        name: this.name_,\n        type: \"reaction\"\n      });\n    }\n    this.isRunning_ = true;\n    var prevReaction = globalState.trackingContext; // reactions could create reactions...\n    globalState.trackingContext = this;\n    var result = trackDerivedFunction(this, fn, undefined);\n    globalState.trackingContext = prevReaction;\n    this.isRunning_ = false;\n    this.isTrackPending_ = false;\n    if (this.isDisposed_) {\n      // disposed during last run. Clean up everything that was bound after the dispose call.\n      clearObserving(this);\n    }\n    if (isCaughtException(result)) {\n      this.reportExceptionInDerivation_(result.cause);\n    }\n    if ( notify) {\n      spyReportEnd({\n        time: Date.now() - startTime\n      });\n    }\n    endBatch();\n  };\n  _proto.reportExceptionInDerivation_ = function reportExceptionInDerivation_(error) {\n    var _this = this;\n    if (this.errorHandler_) {\n      this.errorHandler_(error, this);\n      return;\n    }\n    if (globalState.disableErrorBoundaries) {\n      throw error;\n    }\n    var message =  \"[mobx] Encountered an uncaught exception that was thrown by a reaction or observer component, in: '\" + this + \"'\" ;\n    if (!globalState.suppressReactionErrors) {\n      console.error(message, error);\n      /** If debugging brought you here, please, read the above message :-). Tnx! */\n    } else {\n      console.warn(\"[mobx] (error in reaction '\" + this.name_ + \"' suppressed, fix error of causing action below)\");\n    } // prettier-ignore\n    if ( isSpyEnabled()) {\n      spyReport({\n        type: \"error\",\n        name: this.name_,\n        message: message,\n        error: \"\" + error\n      });\n    }\n    globalState.globalReactionErrorHandlers.forEach(function (f) {\n      return f(error, _this);\n    });\n  };\n  _proto.dispose = function dispose() {\n    if (!this.isDisposed_) {\n      this.isDisposed_ = true;\n      if (!this.isRunning_) {\n        // if disposed while running, clean up later. Maybe not optimal, but rare case\n        startBatch();\n        clearObserving(this);\n        endBatch();\n      }\n    }\n  };\n  _proto.getDisposer_ = function getDisposer_(abortSignal) {\n    var _this2 = this;\n    var dispose = function dispose() {\n      _this2.dispose();\n      abortSignal == null ? void 0 : abortSignal.removeEventListener == null ? void 0 : abortSignal.removeEventListener(\"abort\", dispose);\n    };\n    abortSignal == null ? void 0 : abortSignal.addEventListener == null ? void 0 : abortSignal.addEventListener(\"abort\", dispose);\n    dispose[$mobx] = this;\n    return dispose;\n  };\n  _proto.toString = function toString() {\n    return \"Reaction[\" + this.name_ + \"]\";\n  };\n  _proto.trace = function trace$1(enterBreakPoint) {\n    if (enterBreakPoint === void 0) {\n      enterBreakPoint = false;\n    }\n    trace(this, enterBreakPoint);\n  };\n  return Reaction;\n}();\nfunction onReactionError(handler) {\n  globalState.globalReactionErrorHandlers.push(handler);\n  return function () {\n    var idx = globalState.globalReactionErrorHandlers.indexOf(handler);\n    if (idx >= 0) {\n      globalState.globalReactionErrorHandlers.splice(idx, 1);\n    }\n  };\n}\n/**\n * Magic number alert!\n * Defines within how many times a reaction is allowed to re-trigger itself\n * until it is assumed that this is gonna be a never ending loop...\n */\nvar MAX_REACTION_ITERATIONS = 100;\nvar reactionScheduler = function reactionScheduler(f) {\n  return f();\n};\nfunction runReactions() {\n  // Trampolining, if runReactions are already running, new reactions will be picked up\n  if (globalState.inBatch > 0 || globalState.isRunningReactions) {\n    return;\n  }\n  reactionScheduler(runReactionsHelper);\n}\nfunction runReactionsHelper() {\n  globalState.isRunningReactions = true;\n  var allReactions = globalState.pendingReactions;\n  var iterations = 0;\n  // While running reactions, new reactions might be triggered.\n  // Hence we work with two variables and check whether\n  // we converge to no remaining reactions after a while.\n  while (allReactions.length > 0) {\n    if (++iterations === MAX_REACTION_ITERATIONS) {\n      console.error( \"Reaction doesn't converge to a stable state after \" + MAX_REACTION_ITERATIONS + \" iterations.\" + (\" Probably there is a cycle in the reactive function: \" + allReactions[0]) );\n      allReactions.splice(0); // clear reactions\n    }\n\n    var remainingReactions = allReactions.splice(0);\n    for (var i = 0, l = remainingReactions.length; i < l; i++) {\n      remainingReactions[i].runReaction_();\n    }\n  }\n  globalState.isRunningReactions = false;\n}\nvar isReaction = /*#__PURE__*/createInstanceofPredicate(\"Reaction\", Reaction);\nfunction setReactionScheduler(fn) {\n  var baseScheduler = reactionScheduler;\n  reactionScheduler = function reactionScheduler(f) {\n    return fn(function () {\n      return baseScheduler(f);\n    });\n  };\n}\n\nfunction isSpyEnabled() {\n  return  !!globalState.spyListeners.length;\n}\nfunction spyReport(event) {\n  if (!globalState.spyListeners.length) {\n    return;\n  }\n  var listeners = globalState.spyListeners;\n  for (var i = 0, l = listeners.length; i < l; i++) {\n    listeners[i](event);\n  }\n}\nfunction spyReportStart(event) {\n  var change = _extends({}, event, {\n    spyReportStart: true\n  });\n  spyReport(change);\n}\nvar END_EVENT = {\n  type: \"report-end\",\n  spyReportEnd: true\n};\nfunction spyReportEnd(change) {\n  if (change) {\n    spyReport(_extends({}, change, {\n      type: \"report-end\",\n      spyReportEnd: true\n    }));\n  } else {\n    spyReport(END_EVENT);\n  }\n}\nfunction spy(listener) {\n  {\n    globalState.spyListeners.push(listener);\n    return once(function () {\n      globalState.spyListeners = globalState.spyListeners.filter(function (l) {\n        return l !== listener;\n      });\n    });\n  }\n}\n\nvar ACTION = \"action\";\nvar ACTION_BOUND = \"action.bound\";\nvar AUTOACTION = \"autoAction\";\nvar AUTOACTION_BOUND = \"autoAction.bound\";\nvar DEFAULT_ACTION_NAME = \"<unnamed action>\";\nvar actionAnnotation = /*#__PURE__*/createActionAnnotation(ACTION);\nvar actionBoundAnnotation = /*#__PURE__*/createActionAnnotation(ACTION_BOUND, {\n  bound: true\n});\nvar autoActionAnnotation = /*#__PURE__*/createActionAnnotation(AUTOACTION, {\n  autoAction: true\n});\nvar autoActionBoundAnnotation = /*#__PURE__*/createActionAnnotation(AUTOACTION_BOUND, {\n  autoAction: true,\n  bound: true\n});\nfunction createActionFactory(autoAction) {\n  var res = function action(arg1, arg2) {\n    // action(fn() {})\n    if (isFunction(arg1)) {\n      return createAction(arg1.name || DEFAULT_ACTION_NAME, arg1, autoAction);\n    }\n    // action(\"name\", fn() {})\n    if (isFunction(arg2)) {\n      return createAction(arg1, arg2, autoAction);\n    }\n    // @action (2022.3 Decorators)\n    if (is20223Decorator(arg2)) {\n      return (autoAction ? autoActionAnnotation : actionAnnotation).decorate_20223_(arg1, arg2);\n    }\n    // @action\n    if (isStringish(arg2)) {\n      return storeAnnotation(arg1, arg2, autoAction ? autoActionAnnotation : actionAnnotation);\n    }\n    // action(\"name\") & @action(\"name\")\n    if (isStringish(arg1)) {\n      return createDecoratorAnnotation(createActionAnnotation(autoAction ? AUTOACTION : ACTION, {\n        name: arg1,\n        autoAction: autoAction\n      }));\n    }\n    {\n      die(\"Invalid arguments for `action`\");\n    }\n  };\n  return res;\n}\nvar action = /*#__PURE__*/createActionFactory(false);\nObject.assign(action, actionAnnotation);\nvar autoAction = /*#__PURE__*/createActionFactory(true);\nObject.assign(autoAction, autoActionAnnotation);\naction.bound = /*#__PURE__*/createDecoratorAnnotation(actionBoundAnnotation);\nautoAction.bound = /*#__PURE__*/createDecoratorAnnotation(autoActionBoundAnnotation);\nfunction runInAction(fn) {\n  return executeAction(fn.name || DEFAULT_ACTION_NAME, false, fn, this, undefined);\n}\nfunction isAction(thing) {\n  return isFunction(thing) && thing.isMobxAction === true;\n}\n\n/**\n * Creates a named reactive view and keeps it alive, so that the view is always\n * updated if one of the dependencies changes, even when the view is not further used by something else.\n * @param view The reactive view\n * @returns disposer function, which can be used to stop the view from being updated in the future.\n */\nfunction autorun(view, opts) {\n  var _opts$name, _opts, _opts2, _opts2$signal, _opts3;\n  if (opts === void 0) {\n    opts = EMPTY_OBJECT;\n  }\n  {\n    if (!isFunction(view)) {\n      die(\"Autorun expects a function as first argument\");\n    }\n    if (isAction(view)) {\n      die(\"Autorun does not accept actions since actions are untrackable\");\n    }\n  }\n  var name = (_opts$name = (_opts = opts) == null ? void 0 : _opts.name) != null ? _opts$name :  view.name || \"Autorun@\" + getNextId() ;\n  var runSync = !opts.scheduler && !opts.delay;\n  var reaction;\n  if (runSync) {\n    // normal autorun\n    reaction = new Reaction(name, function () {\n      this.track(reactionRunner);\n    }, opts.onError, opts.requiresObservable);\n  } else {\n    var scheduler = createSchedulerFromOptions(opts);\n    // debounced autorun\n    var isScheduled = false;\n    reaction = new Reaction(name, function () {\n      if (!isScheduled) {\n        isScheduled = true;\n        scheduler(function () {\n          isScheduled = false;\n          if (!reaction.isDisposed_) {\n            reaction.track(reactionRunner);\n          }\n        });\n      }\n    }, opts.onError, opts.requiresObservable);\n  }\n  function reactionRunner() {\n    view(reaction);\n  }\n  if (!((_opts2 = opts) != null && (_opts2$signal = _opts2.signal) != null && _opts2$signal.aborted)) {\n    reaction.schedule_();\n  }\n  return reaction.getDisposer_((_opts3 = opts) == null ? void 0 : _opts3.signal);\n}\nvar run = function run(f) {\n  return f();\n};\nfunction createSchedulerFromOptions(opts) {\n  return opts.scheduler ? opts.scheduler : opts.delay ? function (f) {\n    return setTimeout(f, opts.delay);\n  } : run;\n}\nfunction reaction(expression, effect, opts) {\n  var _opts$name2, _opts4, _opts4$signal, _opts5;\n  if (opts === void 0) {\n    opts = EMPTY_OBJECT;\n  }\n  {\n    if (!isFunction(expression) || !isFunction(effect)) {\n      die(\"First and second argument to reaction should be functions\");\n    }\n    if (!isPlainObject(opts)) {\n      die(\"Third argument of reactions should be an object\");\n    }\n  }\n  var name = (_opts$name2 = opts.name) != null ? _opts$name2 :  \"Reaction@\" + getNextId() ;\n  var effectAction = action(name, opts.onError ? wrapErrorHandler(opts.onError, effect) : effect);\n  var runSync = !opts.scheduler && !opts.delay;\n  var scheduler = createSchedulerFromOptions(opts);\n  var firstTime = true;\n  var isScheduled = false;\n  var value;\n  var oldValue;\n  var equals = opts.compareStructural ? comparer.structural : opts.equals || comparer[\"default\"];\n  var r = new Reaction(name, function () {\n    if (firstTime || runSync) {\n      reactionRunner();\n    } else if (!isScheduled) {\n      isScheduled = true;\n      scheduler(reactionRunner);\n    }\n  }, opts.onError, opts.requiresObservable);\n  function reactionRunner() {\n    isScheduled = false;\n    if (r.isDisposed_) {\n      return;\n    }\n    var changed = false;\n    r.track(function () {\n      var nextValue = allowStateChanges(false, function () {\n        return expression(r);\n      });\n      changed = firstTime || !equals(value, nextValue);\n      oldValue = value;\n      value = nextValue;\n    });\n    if (firstTime && opts.fireImmediately) {\n      effectAction(value, oldValue, r);\n    } else if (!firstTime && changed) {\n      effectAction(value, oldValue, r);\n    }\n    firstTime = false;\n  }\n  if (!((_opts4 = opts) != null && (_opts4$signal = _opts4.signal) != null && _opts4$signal.aborted)) {\n    r.schedule_();\n  }\n  return r.getDisposer_((_opts5 = opts) == null ? void 0 : _opts5.signal);\n}\nfunction wrapErrorHandler(errorHandler, baseFn) {\n  return function () {\n    try {\n      return baseFn.apply(this, arguments);\n    } catch (e) {\n      errorHandler.call(this, e);\n    }\n  };\n}\n\nvar ON_BECOME_OBSERVED = \"onBO\";\nvar ON_BECOME_UNOBSERVED = \"onBUO\";\nfunction onBecomeObserved(thing, arg2, arg3) {\n  return interceptHook(ON_BECOME_OBSERVED, thing, arg2, arg3);\n}\nfunction onBecomeUnobserved(thing, arg2, arg3) {\n  return interceptHook(ON_BECOME_UNOBSERVED, thing, arg2, arg3);\n}\nfunction interceptHook(hook, thing, arg2, arg3) {\n  var atom = typeof arg3 === \"function\" ? getAtom(thing, arg2) : getAtom(thing);\n  var cb = isFunction(arg3) ? arg3 : arg2;\n  var listenersKey = hook + \"L\";\n  if (atom[listenersKey]) {\n    atom[listenersKey].add(cb);\n  } else {\n    atom[listenersKey] = new Set([cb]);\n  }\n  return function () {\n    var hookListeners = atom[listenersKey];\n    if (hookListeners) {\n      hookListeners[\"delete\"](cb);\n      if (hookListeners.size === 0) {\n        delete atom[listenersKey];\n      }\n    }\n  };\n}\n\nvar NEVER = \"never\";\nvar ALWAYS = \"always\";\nvar OBSERVED = \"observed\";\n// const IF_AVAILABLE = \"ifavailable\"\nfunction configure(options) {\n  if (options.isolateGlobalState === true) {\n    isolateGlobalState();\n  }\n  var useProxies = options.useProxies,\n    enforceActions = options.enforceActions;\n  if (useProxies !== undefined) {\n    globalState.useProxies = useProxies === ALWAYS ? true : useProxies === NEVER ? false : typeof Proxy !== \"undefined\";\n  }\n  if (useProxies === \"ifavailable\") {\n    globalState.verifyProxies = true;\n  }\n  if (enforceActions !== undefined) {\n    var ea = enforceActions === ALWAYS ? ALWAYS : enforceActions === OBSERVED;\n    globalState.enforceActions = ea;\n    globalState.allowStateChanges = ea === true || ea === ALWAYS ? false : true;\n  }\n  [\"computedRequiresReaction\", \"reactionRequiresObservable\", \"observableRequiresReaction\", \"disableErrorBoundaries\", \"safeDescriptors\"].forEach(function (key) {\n    if (key in options) {\n      globalState[key] = !!options[key];\n    }\n  });\n  globalState.allowStateReads = !globalState.observableRequiresReaction;\n  if ( globalState.disableErrorBoundaries === true) {\n    console.warn(\"WARNING: Debug feature only. MobX will NOT recover from errors when `disableErrorBoundaries` is enabled.\");\n  }\n  if (options.reactionScheduler) {\n    setReactionScheduler(options.reactionScheduler);\n  }\n}\n\nfunction extendObservable(target, properties, annotations, options) {\n  {\n    if (arguments.length > 4) {\n      die(\"'extendObservable' expected 2-4 arguments\");\n    }\n    if (typeof target !== \"object\") {\n      die(\"'extendObservable' expects an object as first argument\");\n    }\n    if (isObservableMap(target)) {\n      die(\"'extendObservable' should not be used on maps, use map.merge instead\");\n    }\n    if (!isPlainObject(properties)) {\n      die(\"'extendObservable' only accepts plain objects as second argument\");\n    }\n    if (isObservable(properties) || isObservable(annotations)) {\n      die(\"Extending an object with another observable (object) is not supported\");\n    }\n  }\n  // Pull descriptors first, so we don't have to deal with props added by administration ($mobx)\n  var descriptors = getOwnPropertyDescriptors(properties);\n  initObservable(function () {\n    var adm = asObservableObject(target, options)[$mobx];\n    ownKeys(descriptors).forEach(function (key) {\n      adm.extend_(key, descriptors[key],\n      // must pass \"undefined\" for { key: undefined }\n      !annotations ? true : key in annotations ? annotations[key] : true);\n    });\n  });\n  return target;\n}\n\nfunction getDependencyTree(thing, property) {\n  return nodeToDependencyTree(getAtom(thing, property));\n}\nfunction nodeToDependencyTree(node) {\n  var result = {\n    name: node.name_\n  };\n  if (node.observing_ && node.observing_.length > 0) {\n    result.dependencies = unique(node.observing_).map(nodeToDependencyTree);\n  }\n  return result;\n}\nfunction getObserverTree(thing, property) {\n  return nodeToObserverTree(getAtom(thing, property));\n}\nfunction nodeToObserverTree(node) {\n  var result = {\n    name: node.name_\n  };\n  if (hasObservers(node)) {\n    result.observers = Array.from(getObservers(node)).map(nodeToObserverTree);\n  }\n  return result;\n}\nfunction unique(list) {\n  return Array.from(new Set(list));\n}\n\nvar generatorId = 0;\nfunction FlowCancellationError() {\n  this.message = \"FLOW_CANCELLED\";\n}\nFlowCancellationError.prototype = /*#__PURE__*/Object.create(Error.prototype);\nfunction isFlowCancellationError(error) {\n  return error instanceof FlowCancellationError;\n}\nvar flowAnnotation = /*#__PURE__*/createFlowAnnotation(\"flow\");\nvar flowBoundAnnotation = /*#__PURE__*/createFlowAnnotation(\"flow.bound\", {\n  bound: true\n});\nvar flow = /*#__PURE__*/Object.assign(function flow(arg1, arg2) {\n  // @flow (2022.3 Decorators)\n  if (is20223Decorator(arg2)) {\n    return flowAnnotation.decorate_20223_(arg1, arg2);\n  }\n  // @flow\n  if (isStringish(arg2)) {\n    return storeAnnotation(arg1, arg2, flowAnnotation);\n  }\n  // flow(fn)\n  if ( arguments.length !== 1) {\n    die(\"Flow expects single argument with generator function\");\n  }\n  var generator = arg1;\n  var name = generator.name || \"<unnamed flow>\";\n  // Implementation based on https://github.com/tj/co/blob/master/index.js\n  var res = function res() {\n    var ctx = this;\n    var args = arguments;\n    var runId = ++generatorId;\n    var gen = action(name + \" - runid: \" + runId + \" - init\", generator).apply(ctx, args);\n    var rejector;\n    var pendingPromise = undefined;\n    var promise = new Promise(function (resolve, reject) {\n      var stepId = 0;\n      rejector = reject;\n      function onFulfilled(res) {\n        pendingPromise = undefined;\n        var ret;\n        try {\n          ret = action(name + \" - runid: \" + runId + \" - yield \" + stepId++, gen.next).call(gen, res);\n        } catch (e) {\n          return reject(e);\n        }\n        next(ret);\n      }\n      function onRejected(err) {\n        pendingPromise = undefined;\n        var ret;\n        try {\n          ret = action(name + \" - runid: \" + runId + \" - yield \" + stepId++, gen[\"throw\"]).call(gen, err);\n        } catch (e) {\n          return reject(e);\n        }\n        next(ret);\n      }\n      function next(ret) {\n        if (isFunction(ret == null ? void 0 : ret.then)) {\n          // an async iterator\n          ret.then(next, reject);\n          return;\n        }\n        if (ret.done) {\n          return resolve(ret.value);\n        }\n        pendingPromise = Promise.resolve(ret.value);\n        return pendingPromise.then(onFulfilled, onRejected);\n      }\n      onFulfilled(undefined); // kick off the process\n    });\n\n    promise.cancel = action(name + \" - runid: \" + runId + \" - cancel\", function () {\n      try {\n        if (pendingPromise) {\n          cancelPromise(pendingPromise);\n        }\n        // Finally block can return (or yield) stuff..\n        var _res = gen[\"return\"](undefined);\n        // eat anything that promise would do, it's cancelled!\n        var yieldedPromise = Promise.resolve(_res.value);\n        yieldedPromise.then(noop, noop);\n        cancelPromise(yieldedPromise); // maybe it can be cancelled :)\n        // reject our original promise\n        rejector(new FlowCancellationError());\n      } catch (e) {\n        rejector(e); // there could be a throwing finally block\n      }\n    });\n\n    return promise;\n  };\n  res.isMobXFlow = true;\n  return res;\n}, flowAnnotation);\nflow.bound = /*#__PURE__*/createDecoratorAnnotation(flowBoundAnnotation);\nfunction cancelPromise(promise) {\n  if (isFunction(promise.cancel)) {\n    promise.cancel();\n  }\n}\nfunction flowResult(result) {\n  return result; // just tricking TypeScript :)\n}\n\nfunction isFlow(fn) {\n  return (fn == null ? void 0 : fn.isMobXFlow) === true;\n}\n\nfunction interceptReads(thing, propOrHandler, handler) {\n  var target;\n  if (isObservableMap(thing) || isObservableArray(thing) || isObservableValue(thing)) {\n    target = getAdministration(thing);\n  } else if (isObservableObject(thing)) {\n    if ( !isStringish(propOrHandler)) {\n      return die(\"InterceptReads can only be used with a specific property, not with an object in general\");\n    }\n    target = getAdministration(thing, propOrHandler);\n  } else {\n    return die(\"Expected observable map, object or array as first array\");\n  }\n  if ( target.dehancer !== undefined) {\n    return die(\"An intercept reader was already established\");\n  }\n  target.dehancer = typeof propOrHandler === \"function\" ? propOrHandler : handler;\n  return function () {\n    target.dehancer = undefined;\n  };\n}\n\nfunction intercept(thing, propOrHandler, handler) {\n  if (isFunction(handler)) {\n    return interceptProperty(thing, propOrHandler, handler);\n  } else {\n    return interceptInterceptable(thing, propOrHandler);\n  }\n}\nfunction interceptInterceptable(thing, handler) {\n  return getAdministration(thing).intercept_(handler);\n}\nfunction interceptProperty(thing, property, handler) {\n  return getAdministration(thing, property).intercept_(handler);\n}\n\nfunction _isComputed(value, property) {\n  if (property === undefined) {\n    return isComputedValue(value);\n  }\n  if (isObservableObject(value) === false) {\n    return false;\n  }\n  if (!value[$mobx].values_.has(property)) {\n    return false;\n  }\n  var atom = getAtom(value, property);\n  return isComputedValue(atom);\n}\nfunction isComputed(value) {\n  if ( arguments.length > 1) {\n    return die(\"isComputed expects only 1 argument. Use isComputedProp to inspect the observability of a property\");\n  }\n  return _isComputed(value);\n}\nfunction isComputedProp(value, propName) {\n  if ( !isStringish(propName)) {\n    return die(\"isComputed expected a property name as second argument\");\n  }\n  return _isComputed(value, propName);\n}\n\nfunction _isObservable(value, property) {\n  if (!value) {\n    return false;\n  }\n  if (property !== undefined) {\n    if ( (isObservableMap(value) || isObservableArray(value))) {\n      return die(\"isObservable(object, propertyName) is not supported for arrays and maps. Use map.has or array.length instead.\");\n    }\n    if (isObservableObject(value)) {\n      return value[$mobx].values_.has(property);\n    }\n    return false;\n  }\n  // For first check, see #701\n  return isObservableObject(value) || !!value[$mobx] || isAtom(value) || isReaction(value) || isComputedValue(value);\n}\nfunction isObservable(value) {\n  if ( arguments.length !== 1) {\n    die(\"isObservable expects only 1 argument. Use isObservableProp to inspect the observability of a property\");\n  }\n  return _isObservable(value);\n}\nfunction isObservableProp(value, propName) {\n  if ( !isStringish(propName)) {\n    return die(\"expected a property name as second argument\");\n  }\n  return _isObservable(value, propName);\n}\n\nfunction keys(obj) {\n  if (isObservableObject(obj)) {\n    return obj[$mobx].keys_();\n  }\n  if (isObservableMap(obj) || isObservableSet(obj)) {\n    return Array.from(obj.keys());\n  }\n  if (isObservableArray(obj)) {\n    return obj.map(function (_, index) {\n      return index;\n    });\n  }\n  die(5);\n}\nfunction values(obj) {\n  if (isObservableObject(obj)) {\n    return keys(obj).map(function (key) {\n      return obj[key];\n    });\n  }\n  if (isObservableMap(obj)) {\n    return keys(obj).map(function (key) {\n      return obj.get(key);\n    });\n  }\n  if (isObservableSet(obj)) {\n    return Array.from(obj.values());\n  }\n  if (isObservableArray(obj)) {\n    return obj.slice();\n  }\n  die(6);\n}\nfunction entries(obj) {\n  if (isObservableObject(obj)) {\n    return keys(obj).map(function (key) {\n      return [key, obj[key]];\n    });\n  }\n  if (isObservableMap(obj)) {\n    return keys(obj).map(function (key) {\n      return [key, obj.get(key)];\n    });\n  }\n  if (isObservableSet(obj)) {\n    return Array.from(obj.entries());\n  }\n  if (isObservableArray(obj)) {\n    return obj.map(function (key, index) {\n      return [index, key];\n    });\n  }\n  die(7);\n}\nfunction set(obj, key, value) {\n  if (arguments.length === 2 && !isObservableSet(obj)) {\n    startBatch();\n    var _values = key;\n    try {\n      for (var _key in _values) {\n        set(obj, _key, _values[_key]);\n      }\n    } finally {\n      endBatch();\n    }\n    return;\n  }\n  if (isObservableObject(obj)) {\n    obj[$mobx].set_(key, value);\n  } else if (isObservableMap(obj)) {\n    obj.set(key, value);\n  } else if (isObservableSet(obj)) {\n    obj.add(key);\n  } else if (isObservableArray(obj)) {\n    if (typeof key !== \"number\") {\n      key = parseInt(key, 10);\n    }\n    if (key < 0) {\n      die(\"Invalid index: '\" + key + \"'\");\n    }\n    startBatch();\n    if (key >= obj.length) {\n      obj.length = key + 1;\n    }\n    obj[key] = value;\n    endBatch();\n  } else {\n    die(8);\n  }\n}\nfunction remove(obj, key) {\n  if (isObservableObject(obj)) {\n    obj[$mobx].delete_(key);\n  } else if (isObservableMap(obj)) {\n    obj[\"delete\"](key);\n  } else if (isObservableSet(obj)) {\n    obj[\"delete\"](key);\n  } else if (isObservableArray(obj)) {\n    if (typeof key !== \"number\") {\n      key = parseInt(key, 10);\n    }\n    obj.splice(key, 1);\n  } else {\n    die(9);\n  }\n}\nfunction has(obj, key) {\n  if (isObservableObject(obj)) {\n    return obj[$mobx].has_(key);\n  } else if (isObservableMap(obj)) {\n    return obj.has(key);\n  } else if (isObservableSet(obj)) {\n    return obj.has(key);\n  } else if (isObservableArray(obj)) {\n    return key >= 0 && key < obj.length;\n  }\n  die(10);\n}\nfunction get(obj, key) {\n  if (!has(obj, key)) {\n    return undefined;\n  }\n  if (isObservableObject(obj)) {\n    return obj[$mobx].get_(key);\n  } else if (isObservableMap(obj)) {\n    return obj.get(key);\n  } else if (isObservableArray(obj)) {\n    return obj[key];\n  }\n  die(11);\n}\nfunction apiDefineProperty(obj, key, descriptor) {\n  if (isObservableObject(obj)) {\n    return obj[$mobx].defineProperty_(key, descriptor);\n  }\n  die(39);\n}\nfunction apiOwnKeys(obj) {\n  if (isObservableObject(obj)) {\n    return obj[$mobx].ownKeys_();\n  }\n  die(38);\n}\n\nfunction observe(thing, propOrCb, cbOrFire, fireImmediately) {\n  if (isFunction(cbOrFire)) {\n    return observeObservableProperty(thing, propOrCb, cbOrFire, fireImmediately);\n  } else {\n    return observeObservable(thing, propOrCb, cbOrFire);\n  }\n}\nfunction observeObservable(thing, listener, fireImmediately) {\n  return getAdministration(thing).observe_(listener, fireImmediately);\n}\nfunction observeObservableProperty(thing, property, listener, fireImmediately) {\n  return getAdministration(thing, property).observe_(listener, fireImmediately);\n}\n\nfunction cache(map, key, value) {\n  map.set(key, value);\n  return value;\n}\nfunction toJSHelper(source, __alreadySeen) {\n  if (source == null || typeof source !== \"object\" || source instanceof Date || !isObservable(source)) {\n    return source;\n  }\n  if (isObservableValue(source) || isComputedValue(source)) {\n    return toJSHelper(source.get(), __alreadySeen);\n  }\n  if (__alreadySeen.has(source)) {\n    return __alreadySeen.get(source);\n  }\n  if (isObservableArray(source)) {\n    var res = cache(__alreadySeen, source, new Array(source.length));\n    source.forEach(function (value, idx) {\n      res[idx] = toJSHelper(value, __alreadySeen);\n    });\n    return res;\n  }\n  if (isObservableSet(source)) {\n    var _res = cache(__alreadySeen, source, new Set());\n    source.forEach(function (value) {\n      _res.add(toJSHelper(value, __alreadySeen));\n    });\n    return _res;\n  }\n  if (isObservableMap(source)) {\n    var _res2 = cache(__alreadySeen, source, new Map());\n    source.forEach(function (value, key) {\n      _res2.set(key, toJSHelper(value, __alreadySeen));\n    });\n    return _res2;\n  } else {\n    // must be observable object\n    var _res3 = cache(__alreadySeen, source, {});\n    apiOwnKeys(source).forEach(function (key) {\n      if (objectPrototype.propertyIsEnumerable.call(source, key)) {\n        _res3[key] = toJSHelper(source[key], __alreadySeen);\n      }\n    });\n    return _res3;\n  }\n}\n/**\n * Recursively converts an observable to it's non-observable native counterpart.\n * It does NOT recurse into non-observables, these are left as they are, even if they contain observables.\n * Computed and other non-enumerable properties are completely ignored.\n * Complex scenarios require custom solution, eg implementing `toJSON` or using `serializr` lib.\n */\nfunction toJS(source, options) {\n  if ( options) {\n    die(\"toJS no longer supports options\");\n  }\n  return toJSHelper(source, new Map());\n}\n\nfunction trace() {\n  var enterBreakPoint = false;\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  if (typeof args[args.length - 1] === \"boolean\") {\n    enterBreakPoint = args.pop();\n  }\n  var derivation = getAtomFromArgs(args);\n  if (!derivation) {\n    return die(\"'trace(break?)' can only be used inside a tracked computed value or a Reaction. Consider passing in the computed value or reaction explicitly\");\n  }\n  if (derivation.isTracing_ === TraceMode.NONE) {\n    console.log(\"[mobx.trace] '\" + derivation.name_ + \"' tracing enabled\");\n  }\n  derivation.isTracing_ = enterBreakPoint ? TraceMode.BREAK : TraceMode.LOG;\n}\nfunction getAtomFromArgs(args) {\n  switch (args.length) {\n    case 0:\n      return globalState.trackingDerivation;\n    case 1:\n      return getAtom(args[0]);\n    case 2:\n      return getAtom(args[0], args[1]);\n  }\n}\n\n/**\n * During a transaction no views are updated until the end of the transaction.\n * The transaction will be run synchronously nonetheless.\n *\n * @param action a function that updates some reactive state\n * @returns any value that was returned by the 'action' parameter.\n */\nfunction transaction(action, thisArg) {\n  if (thisArg === void 0) {\n    thisArg = undefined;\n  }\n  startBatch();\n  try {\n    return action.apply(thisArg);\n  } finally {\n    endBatch();\n  }\n}\n\nfunction when(predicate, arg1, arg2) {\n  if (arguments.length === 1 || arg1 && typeof arg1 === \"object\") {\n    return whenPromise(predicate, arg1);\n  }\n  return _when(predicate, arg1, arg2 || {});\n}\nfunction _when(predicate, effect, opts) {\n  var timeoutHandle;\n  if (typeof opts.timeout === \"number\") {\n    var error = new Error(\"WHEN_TIMEOUT\");\n    timeoutHandle = setTimeout(function () {\n      if (!disposer[$mobx].isDisposed_) {\n        disposer();\n        if (opts.onError) {\n          opts.onError(error);\n        } else {\n          throw error;\n        }\n      }\n    }, opts.timeout);\n  }\n  opts.name =  opts.name || \"When@\" + getNextId() ;\n  var effectAction = createAction( opts.name + \"-effect\" , effect);\n  // eslint-disable-next-line\n  var disposer = autorun(function (r) {\n    // predicate should not change state\n    var cond = allowStateChanges(false, predicate);\n    if (cond) {\n      r.dispose();\n      if (timeoutHandle) {\n        clearTimeout(timeoutHandle);\n      }\n      effectAction();\n    }\n  }, opts);\n  return disposer;\n}\nfunction whenPromise(predicate, opts) {\n  var _opts$signal;\n  if ( opts && opts.onError) {\n    return die(\"the options 'onError' and 'promise' cannot be combined\");\n  }\n  if (opts != null && (_opts$signal = opts.signal) != null && _opts$signal.aborted) {\n    return Object.assign(Promise.reject(new Error(\"WHEN_ABORTED\")), {\n      cancel: function cancel() {\n        return null;\n      }\n    });\n  }\n  var cancel;\n  var abort;\n  var res = new Promise(function (resolve, reject) {\n    var _opts$signal2;\n    var disposer = _when(predicate, resolve, _extends({}, opts, {\n      onError: reject\n    }));\n    cancel = function cancel() {\n      disposer();\n      reject(new Error(\"WHEN_CANCELLED\"));\n    };\n    abort = function abort() {\n      disposer();\n      reject(new Error(\"WHEN_ABORTED\"));\n    };\n    opts == null ? void 0 : (_opts$signal2 = opts.signal) == null ? void 0 : _opts$signal2.addEventListener == null ? void 0 : _opts$signal2.addEventListener(\"abort\", abort);\n  })[\"finally\"](function () {\n    var _opts$signal3;\n    return opts == null ? void 0 : (_opts$signal3 = opts.signal) == null ? void 0 : _opts$signal3.removeEventListener == null ? void 0 : _opts$signal3.removeEventListener(\"abort\", abort);\n  });\n  res.cancel = cancel;\n  return res;\n}\n\nfunction getAdm(target) {\n  return target[$mobx];\n}\n// Optimization: we don't need the intermediate objects and could have a completely custom administration for DynamicObjects,\n// and skip either the internal values map, or the base object with its property descriptors!\nvar objectProxyTraps = {\n  has: function has(target, name) {\n    if ( globalState.trackingDerivation) {\n      warnAboutProxyRequirement(\"detect new properties using the 'in' operator. Use 'has' from 'mobx' instead.\");\n    }\n    return getAdm(target).has_(name);\n  },\n  get: function get(target, name) {\n    return getAdm(target).get_(name);\n  },\n  set: function set(target, name, value) {\n    var _getAdm$set_;\n    if (!isStringish(name)) {\n      return false;\n    }\n    if ( !getAdm(target).values_.has(name)) {\n      warnAboutProxyRequirement(\"add a new observable property through direct assignment. Use 'set' from 'mobx' instead.\");\n    }\n    // null (intercepted) -> true (success)\n    return (_getAdm$set_ = getAdm(target).set_(name, value, true)) != null ? _getAdm$set_ : true;\n  },\n  deleteProperty: function deleteProperty(target, name) {\n    var _getAdm$delete_;\n    {\n      warnAboutProxyRequirement(\"delete properties from an observable object. Use 'remove' from 'mobx' instead.\");\n    }\n    if (!isStringish(name)) {\n      return false;\n    }\n    // null (intercepted) -> true (success)\n    return (_getAdm$delete_ = getAdm(target).delete_(name, true)) != null ? _getAdm$delete_ : true;\n  },\n  defineProperty: function defineProperty(target, name, descriptor) {\n    var _getAdm$definePropert;\n    {\n      warnAboutProxyRequirement(\"define property on an observable object. Use 'defineProperty' from 'mobx' instead.\");\n    }\n    // null (intercepted) -> true (success)\n    return (_getAdm$definePropert = getAdm(target).defineProperty_(name, descriptor)) != null ? _getAdm$definePropert : true;\n  },\n  ownKeys: function ownKeys(target) {\n    if ( globalState.trackingDerivation) {\n      warnAboutProxyRequirement(\"iterate keys to detect added / removed properties. Use 'keys' from 'mobx' instead.\");\n    }\n    return getAdm(target).ownKeys_();\n  },\n  preventExtensions: function preventExtensions(target) {\n    die(13);\n  }\n};\nfunction asDynamicObservableObject(target, options) {\n  var _target$$mobx, _target$$mobx$proxy_;\n  assertProxies();\n  target = asObservableObject(target, options);\n  return (_target$$mobx$proxy_ = (_target$$mobx = target[$mobx]).proxy_) != null ? _target$$mobx$proxy_ : _target$$mobx.proxy_ = new Proxy(target, objectProxyTraps);\n}\n\nfunction hasInterceptors(interceptable) {\n  return interceptable.interceptors_ !== undefined && interceptable.interceptors_.length > 0;\n}\nfunction registerInterceptor(interceptable, handler) {\n  var interceptors = interceptable.interceptors_ || (interceptable.interceptors_ = []);\n  interceptors.push(handler);\n  return once(function () {\n    var idx = interceptors.indexOf(handler);\n    if (idx !== -1) {\n      interceptors.splice(idx, 1);\n    }\n  });\n}\nfunction interceptChange(interceptable, change) {\n  var prevU = untrackedStart();\n  try {\n    // Interceptor can modify the array, copy it to avoid concurrent modification, see #1950\n    var interceptors = [].concat(interceptable.interceptors_ || []);\n    for (var i = 0, l = interceptors.length; i < l; i++) {\n      change = interceptors[i](change);\n      if (change && !change.type) {\n        die(14);\n      }\n      if (!change) {\n        break;\n      }\n    }\n    return change;\n  } finally {\n    untrackedEnd(prevU);\n  }\n}\n\nfunction hasListeners(listenable) {\n  return listenable.changeListeners_ !== undefined && listenable.changeListeners_.length > 0;\n}\nfunction registerListener(listenable, handler) {\n  var listeners = listenable.changeListeners_ || (listenable.changeListeners_ = []);\n  listeners.push(handler);\n  return once(function () {\n    var idx = listeners.indexOf(handler);\n    if (idx !== -1) {\n      listeners.splice(idx, 1);\n    }\n  });\n}\nfunction notifyListeners(listenable, change) {\n  var prevU = untrackedStart();\n  var listeners = listenable.changeListeners_;\n  if (!listeners) {\n    return;\n  }\n  listeners = listeners.slice();\n  for (var i = 0, l = listeners.length; i < l; i++) {\n    listeners[i](change);\n  }\n  untrackedEnd(prevU);\n}\n\nfunction makeObservable(target, annotations, options) {\n  initObservable(function () {\n    var _annotations;\n    var adm = asObservableObject(target, options)[$mobx];\n    if (\"development\" !== \"production\" && annotations && target[storedAnnotationsSymbol]) {\n      die(\"makeObservable second arg must be nullish when using decorators. Mixing @decorator syntax with annotations is not supported.\");\n    }\n    // Default to decorators\n    (_annotations = annotations) != null ? _annotations : annotations = collectStoredAnnotations(target);\n    // Annotate\n    ownKeys(annotations).forEach(function (key) {\n      return adm.make_(key, annotations[key]);\n    });\n  });\n  return target;\n}\n// proto[keysSymbol] = new Set<PropertyKey>()\nvar keysSymbol = /*#__PURE__*/Symbol(\"mobx-keys\");\nfunction makeAutoObservable(target, overrides, options) {\n  {\n    if (!isPlainObject(target) && !isPlainObject(Object.getPrototypeOf(target))) {\n      die(\"'makeAutoObservable' can only be used for classes that don't have a superclass\");\n    }\n    if (isObservableObject(target)) {\n      die(\"makeAutoObservable can only be used on objects not already made observable\");\n    }\n  }\n  // Optimization: avoid visiting protos\n  // Assumes that annotation.make_/.extend_ works the same for plain objects\n  if (isPlainObject(target)) {\n    return extendObservable(target, target, overrides, options);\n  }\n  initObservable(function () {\n    var adm = asObservableObject(target, options)[$mobx];\n    // Optimization: cache keys on proto\n    // Assumes makeAutoObservable can be called only once per object and can't be used in subclass\n    if (!target[keysSymbol]) {\n      var proto = Object.getPrototypeOf(target);\n      var keys = new Set([].concat(ownKeys(target), ownKeys(proto)));\n      keys[\"delete\"](\"constructor\");\n      keys[\"delete\"]($mobx);\n      addHiddenProp(proto, keysSymbol, keys);\n    }\n    target[keysSymbol].forEach(function (key) {\n      return adm.make_(key,\n      // must pass \"undefined\" for { key: undefined }\n      !overrides ? true : key in overrides ? overrides[key] : true);\n    });\n  });\n  return target;\n}\n\nvar SPLICE = \"splice\";\nvar UPDATE = \"update\";\nvar MAX_SPLICE_SIZE = 10000; // See e.g. https://github.com/mobxjs/mobx/issues/859\nvar arrayTraps = {\n  get: function get(target, name) {\n    var adm = target[$mobx];\n    if (name === $mobx) {\n      return adm;\n    }\n    if (name === \"length\") {\n      return adm.getArrayLength_();\n    }\n    if (typeof name === \"string\" && !isNaN(name)) {\n      return adm.get_(parseInt(name));\n    }\n    if (hasProp(arrayExtensions, name)) {\n      return arrayExtensions[name];\n    }\n    return target[name];\n  },\n  set: function set(target, name, value) {\n    var adm = target[$mobx];\n    if (name === \"length\") {\n      adm.setArrayLength_(value);\n    }\n    if (typeof name === \"symbol\" || isNaN(name)) {\n      target[name] = value;\n    } else {\n      // numeric string\n      adm.set_(parseInt(name), value);\n    }\n    return true;\n  },\n  preventExtensions: function preventExtensions() {\n    die(15);\n  }\n};\nvar ObservableArrayAdministration = /*#__PURE__*/function () {\n  // this is the prop that gets proxied, so can't replace it!\n\n  function ObservableArrayAdministration(name, enhancer, owned_, legacyMode_) {\n    if (name === void 0) {\n      name =  \"ObservableArray@\" + getNextId() ;\n    }\n    this.owned_ = void 0;\n    this.legacyMode_ = void 0;\n    this.atom_ = void 0;\n    this.values_ = [];\n    this.interceptors_ = void 0;\n    this.changeListeners_ = void 0;\n    this.enhancer_ = void 0;\n    this.dehancer = void 0;\n    this.proxy_ = void 0;\n    this.lastKnownLength_ = 0;\n    this.owned_ = owned_;\n    this.legacyMode_ = legacyMode_;\n    this.atom_ = new Atom(name);\n    this.enhancer_ = function (newV, oldV) {\n      return enhancer(newV, oldV,  name + \"[..]\" );\n    };\n  }\n  var _proto = ObservableArrayAdministration.prototype;\n  _proto.dehanceValue_ = function dehanceValue_(value) {\n    if (this.dehancer !== undefined) {\n      return this.dehancer(value);\n    }\n    return value;\n  };\n  _proto.dehanceValues_ = function dehanceValues_(values) {\n    if (this.dehancer !== undefined && values.length > 0) {\n      return values.map(this.dehancer);\n    }\n    return values;\n  };\n  _proto.intercept_ = function intercept_(handler) {\n    return registerInterceptor(this, handler);\n  };\n  _proto.observe_ = function observe_(listener, fireImmediately) {\n    if (fireImmediately === void 0) {\n      fireImmediately = false;\n    }\n    if (fireImmediately) {\n      listener({\n        observableKind: \"array\",\n        object: this.proxy_,\n        debugObjectName: this.atom_.name_,\n        type: \"splice\",\n        index: 0,\n        added: this.values_.slice(),\n        addedCount: this.values_.length,\n        removed: [],\n        removedCount: 0\n      });\n    }\n    return registerListener(this, listener);\n  };\n  _proto.getArrayLength_ = function getArrayLength_() {\n    this.atom_.reportObserved();\n    return this.values_.length;\n  };\n  _proto.setArrayLength_ = function setArrayLength_(newLength) {\n    if (typeof newLength !== \"number\" || isNaN(newLength) || newLength < 0) {\n      die(\"Out of range: \" + newLength);\n    }\n    var currentLength = this.values_.length;\n    if (newLength === currentLength) {\n      return;\n    } else if (newLength > currentLength) {\n      var newItems = new Array(newLength - currentLength);\n      for (var i = 0; i < newLength - currentLength; i++) {\n        newItems[i] = undefined;\n      } // No Array.fill everywhere...\n      this.spliceWithArray_(currentLength, 0, newItems);\n    } else {\n      this.spliceWithArray_(newLength, currentLength - newLength);\n    }\n  };\n  _proto.updateArrayLength_ = function updateArrayLength_(oldLength, delta) {\n    if (oldLength !== this.lastKnownLength_) {\n      die(16);\n    }\n    this.lastKnownLength_ += delta;\n    if (this.legacyMode_ && delta > 0) {\n      reserveArrayBuffer(oldLength + delta + 1);\n    }\n  };\n  _proto.spliceWithArray_ = function spliceWithArray_(index, deleteCount, newItems) {\n    var _this = this;\n    checkIfStateModificationsAreAllowed(this.atom_);\n    var length = this.values_.length;\n    if (index === undefined) {\n      index = 0;\n    } else if (index > length) {\n      index = length;\n    } else if (index < 0) {\n      index = Math.max(0, length + index);\n    }\n    if (arguments.length === 1) {\n      deleteCount = length - index;\n    } else if (deleteCount === undefined || deleteCount === null) {\n      deleteCount = 0;\n    } else {\n      deleteCount = Math.max(0, Math.min(deleteCount, length - index));\n    }\n    if (newItems === undefined) {\n      newItems = EMPTY_ARRAY;\n    }\n    if (hasInterceptors(this)) {\n      var change = interceptChange(this, {\n        object: this.proxy_,\n        type: SPLICE,\n        index: index,\n        removedCount: deleteCount,\n        added: newItems\n      });\n      if (!change) {\n        return EMPTY_ARRAY;\n      }\n      deleteCount = change.removedCount;\n      newItems = change.added;\n    }\n    newItems = newItems.length === 0 ? newItems : newItems.map(function (v) {\n      return _this.enhancer_(v, undefined);\n    });\n    if (this.legacyMode_ || \"development\" !== \"production\") {\n      var lengthDelta = newItems.length - deleteCount;\n      this.updateArrayLength_(length, lengthDelta); // checks if internal array wasn't modified\n    }\n\n    var res = this.spliceItemsIntoValues_(index, deleteCount, newItems);\n    if (deleteCount !== 0 || newItems.length !== 0) {\n      this.notifyArraySplice_(index, newItems, res);\n    }\n    return this.dehanceValues_(res);\n  };\n  _proto.spliceItemsIntoValues_ = function spliceItemsIntoValues_(index, deleteCount, newItems) {\n    if (newItems.length < MAX_SPLICE_SIZE) {\n      var _this$values_;\n      return (_this$values_ = this.values_).splice.apply(_this$values_, [index, deleteCount].concat(newItems));\n    } else {\n      // The items removed by the splice\n      var res = this.values_.slice(index, index + deleteCount);\n      // The items that that should remain at the end of the array\n      var oldItems = this.values_.slice(index + deleteCount);\n      // New length is the previous length + addition count - deletion count\n      this.values_.length += newItems.length - deleteCount;\n      for (var i = 0; i < newItems.length; i++) {\n        this.values_[index + i] = newItems[i];\n      }\n      for (var _i = 0; _i < oldItems.length; _i++) {\n        this.values_[index + newItems.length + _i] = oldItems[_i];\n      }\n      return res;\n    }\n  };\n  _proto.notifyArrayChildUpdate_ = function notifyArrayChildUpdate_(index, newValue, oldValue) {\n    var notifySpy = !this.owned_ && isSpyEnabled();\n    var notify = hasListeners(this);\n    var change = notify || notifySpy ? {\n      observableKind: \"array\",\n      object: this.proxy_,\n      type: UPDATE,\n      debugObjectName: this.atom_.name_,\n      index: index,\n      newValue: newValue,\n      oldValue: oldValue\n    } : null;\n    // The reason why this is on right hand side here (and not above), is this way the uglifier will drop it, but it won't\n    // cause any runtime overhead in development mode without NODE_ENV set, unless spying is enabled\n    if ( notifySpy) {\n      spyReportStart(change);\n    }\n    this.atom_.reportChanged();\n    if (notify) {\n      notifyListeners(this, change);\n    }\n    if ( notifySpy) {\n      spyReportEnd();\n    }\n  };\n  _proto.notifyArraySplice_ = function notifyArraySplice_(index, added, removed) {\n    var notifySpy = !this.owned_ && isSpyEnabled();\n    var notify = hasListeners(this);\n    var change = notify || notifySpy ? {\n      observableKind: \"array\",\n      object: this.proxy_,\n      debugObjectName: this.atom_.name_,\n      type: SPLICE,\n      index: index,\n      removed: removed,\n      added: added,\n      removedCount: removed.length,\n      addedCount: added.length\n    } : null;\n    if ( notifySpy) {\n      spyReportStart(change);\n    }\n    this.atom_.reportChanged();\n    // conform: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/observe\n    if (notify) {\n      notifyListeners(this, change);\n    }\n    if ( notifySpy) {\n      spyReportEnd();\n    }\n  };\n  _proto.get_ = function get_(index) {\n    if (this.legacyMode_ && index >= this.values_.length) {\n      console.warn( \"[mobx.array] Attempt to read an array index (\" + index + \") that is out of bounds (\" + this.values_.length + \"). Please check length first. Out of bound indices will not be tracked by MobX\" );\n      return undefined;\n    }\n    this.atom_.reportObserved();\n    return this.dehanceValue_(this.values_[index]);\n  };\n  _proto.set_ = function set_(index, newValue) {\n    var values = this.values_;\n    if (this.legacyMode_ && index > values.length) {\n      // out of bounds\n      die(17, index, values.length);\n    }\n    if (index < values.length) {\n      // update at index in range\n      checkIfStateModificationsAreAllowed(this.atom_);\n      var oldValue = values[index];\n      if (hasInterceptors(this)) {\n        var change = interceptChange(this, {\n          type: UPDATE,\n          object: this.proxy_,\n          index: index,\n          newValue: newValue\n        });\n        if (!change) {\n          return;\n        }\n        newValue = change.newValue;\n      }\n      newValue = this.enhancer_(newValue, oldValue);\n      var changed = newValue !== oldValue;\n      if (changed) {\n        values[index] = newValue;\n        this.notifyArrayChildUpdate_(index, newValue, oldValue);\n      }\n    } else {\n      // For out of bound index, we don't create an actual sparse array,\n      // but rather fill the holes with undefined (same as setArrayLength_).\n      // This could be considered a bug.\n      var newItems = new Array(index + 1 - values.length);\n      for (var i = 0; i < newItems.length - 1; i++) {\n        newItems[i] = undefined;\n      } // No Array.fill everywhere...\n      newItems[newItems.length - 1] = newValue;\n      this.spliceWithArray_(values.length, 0, newItems);\n    }\n  };\n  return ObservableArrayAdministration;\n}();\nfunction createObservableArray(initialValues, enhancer, name, owned) {\n  if (name === void 0) {\n    name =  \"ObservableArray@\" + getNextId() ;\n  }\n  if (owned === void 0) {\n    owned = false;\n  }\n  assertProxies();\n  return initObservable(function () {\n    var adm = new ObservableArrayAdministration(name, enhancer, owned, false);\n    addHiddenFinalProp(adm.values_, $mobx, adm);\n    var proxy = new Proxy(adm.values_, arrayTraps);\n    adm.proxy_ = proxy;\n    if (initialValues && initialValues.length) {\n      adm.spliceWithArray_(0, 0, initialValues);\n    }\n    return proxy;\n  });\n}\n// eslint-disable-next-line\nvar arrayExtensions = {\n  clear: function clear() {\n    return this.splice(0);\n  },\n  replace: function replace(newItems) {\n    var adm = this[$mobx];\n    return adm.spliceWithArray_(0, adm.values_.length, newItems);\n  },\n  // Used by JSON.stringify\n  toJSON: function toJSON() {\n    return this.slice();\n  },\n  /*\n   * functions that do alter the internal structure of the array, (based on lib.es6.d.js)\n   * since these functions alter the inner structure of the array, the have side effects.\n   * Because the have side effects, they should not be used in computed function,\n   * and for that reason the do not call dependencyState.notifyObserved\n   */\n  splice: function splice(index, deleteCount) {\n    for (var _len = arguments.length, newItems = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n      newItems[_key - 2] = arguments[_key];\n    }\n    var adm = this[$mobx];\n    switch (arguments.length) {\n      case 0:\n        return [];\n      case 1:\n        return adm.spliceWithArray_(index);\n      case 2:\n        return adm.spliceWithArray_(index, deleteCount);\n    }\n    return adm.spliceWithArray_(index, deleteCount, newItems);\n  },\n  spliceWithArray: function spliceWithArray(index, deleteCount, newItems) {\n    return this[$mobx].spliceWithArray_(index, deleteCount, newItems);\n  },\n  push: function push() {\n    var adm = this[$mobx];\n    for (var _len2 = arguments.length, items = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      items[_key2] = arguments[_key2];\n    }\n    adm.spliceWithArray_(adm.values_.length, 0, items);\n    return adm.values_.length;\n  },\n  pop: function pop() {\n    return this.splice(Math.max(this[$mobx].values_.length - 1, 0), 1)[0];\n  },\n  shift: function shift() {\n    return this.splice(0, 1)[0];\n  },\n  unshift: function unshift() {\n    var adm = this[$mobx];\n    for (var _len3 = arguments.length, items = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      items[_key3] = arguments[_key3];\n    }\n    adm.spliceWithArray_(0, 0, items);\n    return adm.values_.length;\n  },\n  reverse: function reverse() {\n    // reverse by default mutates in place before returning the result\n    // which makes it both a 'derivation' and a 'mutation'.\n    if (globalState.trackingDerivation) {\n      die(37, \"reverse\");\n    }\n    this.replace(this.slice().reverse());\n    return this;\n  },\n  sort: function sort() {\n    // sort by default mutates in place before returning the result\n    // which goes against all good practices. Let's not change the array in place!\n    if (globalState.trackingDerivation) {\n      die(37, \"sort\");\n    }\n    var copy = this.slice();\n    copy.sort.apply(copy, arguments);\n    this.replace(copy);\n    return this;\n  },\n  remove: function remove(value) {\n    var adm = this[$mobx];\n    var idx = adm.dehanceValues_(adm.values_).indexOf(value);\n    if (idx > -1) {\n      this.splice(idx, 1);\n      return true;\n    }\n    return false;\n  }\n};\n/**\n * Wrap function from prototype\n * Without this, everything works as well, but this works\n * faster as everything works on unproxied values\n */\naddArrayExtension(\"at\", simpleFunc);\naddArrayExtension(\"concat\", simpleFunc);\naddArrayExtension(\"flat\", simpleFunc);\naddArrayExtension(\"includes\", simpleFunc);\naddArrayExtension(\"indexOf\", simpleFunc);\naddArrayExtension(\"join\", simpleFunc);\naddArrayExtension(\"lastIndexOf\", simpleFunc);\naddArrayExtension(\"slice\", simpleFunc);\naddArrayExtension(\"toString\", simpleFunc);\naddArrayExtension(\"toLocaleString\", simpleFunc);\naddArrayExtension(\"toSorted\", simpleFunc);\naddArrayExtension(\"toSpliced\", simpleFunc);\naddArrayExtension(\"with\", simpleFunc);\n// map\naddArrayExtension(\"every\", mapLikeFunc);\naddArrayExtension(\"filter\", mapLikeFunc);\naddArrayExtension(\"find\", mapLikeFunc);\naddArrayExtension(\"findIndex\", mapLikeFunc);\naddArrayExtension(\"findLast\", mapLikeFunc);\naddArrayExtension(\"findLastIndex\", mapLikeFunc);\naddArrayExtension(\"flatMap\", mapLikeFunc);\naddArrayExtension(\"forEach\", mapLikeFunc);\naddArrayExtension(\"map\", mapLikeFunc);\naddArrayExtension(\"some\", mapLikeFunc);\naddArrayExtension(\"toReversed\", mapLikeFunc);\n// reduce\naddArrayExtension(\"reduce\", reduceLikeFunc);\naddArrayExtension(\"reduceRight\", reduceLikeFunc);\nfunction addArrayExtension(funcName, funcFactory) {\n  if (typeof Array.prototype[funcName] === \"function\") {\n    arrayExtensions[funcName] = funcFactory(funcName);\n  }\n}\n// Report and delegate to dehanced array\nfunction simpleFunc(funcName) {\n  return function () {\n    var adm = this[$mobx];\n    adm.atom_.reportObserved();\n    var dehancedValues = adm.dehanceValues_(adm.values_);\n    return dehancedValues[funcName].apply(dehancedValues, arguments);\n  };\n}\n// Make sure callbacks recieve correct array arg #2326\nfunction mapLikeFunc(funcName) {\n  return function (callback, thisArg) {\n    var _this2 = this;\n    var adm = this[$mobx];\n    adm.atom_.reportObserved();\n    var dehancedValues = adm.dehanceValues_(adm.values_);\n    return dehancedValues[funcName](function (element, index) {\n      return callback.call(thisArg, element, index, _this2);\n    });\n  };\n}\n// Make sure callbacks recieve correct array arg #2326\nfunction reduceLikeFunc(funcName) {\n  return function () {\n    var _this3 = this;\n    var adm = this[$mobx];\n    adm.atom_.reportObserved();\n    var dehancedValues = adm.dehanceValues_(adm.values_);\n    // #2432 - reduce behavior depends on arguments.length\n    var callback = arguments[0];\n    arguments[0] = function (accumulator, currentValue, index) {\n      return callback(accumulator, currentValue, index, _this3);\n    };\n    return dehancedValues[funcName].apply(dehancedValues, arguments);\n  };\n}\nvar isObservableArrayAdministration = /*#__PURE__*/createInstanceofPredicate(\"ObservableArrayAdministration\", ObservableArrayAdministration);\nfunction isObservableArray(thing) {\n  return isObject(thing) && isObservableArrayAdministration(thing[$mobx]);\n}\n\nvar _Symbol$iterator, _Symbol$toStringTag;\nvar ObservableMapMarker = {};\nvar ADD = \"add\";\nvar DELETE = \"delete\";\n// just extend Map? See also https://gist.github.com/nestharus/13b4d74f2ef4a2f4357dbd3fc23c1e54\n// But: https://github.com/mobxjs/mobx/issues/1556\n_Symbol$iterator = Symbol.iterator;\n_Symbol$toStringTag = Symbol.toStringTag;\nvar ObservableMap = /*#__PURE__*/function () {\n  // hasMap, not hashMap >-).\n\n  function ObservableMap(initialData, enhancer_, name_) {\n    var _this = this;\n    if (enhancer_ === void 0) {\n      enhancer_ = deepEnhancer;\n    }\n    if (name_ === void 0) {\n      name_ =  \"ObservableMap@\" + getNextId() ;\n    }\n    this.enhancer_ = void 0;\n    this.name_ = void 0;\n    this[$mobx] = ObservableMapMarker;\n    this.data_ = void 0;\n    this.hasMap_ = void 0;\n    this.keysAtom_ = void 0;\n    this.interceptors_ = void 0;\n    this.changeListeners_ = void 0;\n    this.dehancer = void 0;\n    this.enhancer_ = enhancer_;\n    this.name_ = name_;\n    if (!isFunction(Map)) {\n      die(18);\n    }\n    initObservable(function () {\n      _this.keysAtom_ = createAtom(\"development\" !== \"production\" ? _this.name_ + \".keys()\" : \"ObservableMap.keys()\");\n      _this.data_ = new Map();\n      _this.hasMap_ = new Map();\n      if (initialData) {\n        _this.merge(initialData);\n      }\n    });\n  }\n  var _proto = ObservableMap.prototype;\n  _proto.has_ = function has_(key) {\n    return this.data_.has(key);\n  };\n  _proto.has = function has(key) {\n    var _this2 = this;\n    if (!globalState.trackingDerivation) {\n      return this.has_(key);\n    }\n    var entry = this.hasMap_.get(key);\n    if (!entry) {\n      var newEntry = entry = new ObservableValue(this.has_(key), referenceEnhancer,  this.name_ + \".\" + stringifyKey(key) + \"?\" , false);\n      this.hasMap_.set(key, newEntry);\n      onBecomeUnobserved(newEntry, function () {\n        return _this2.hasMap_[\"delete\"](key);\n      });\n    }\n    return entry.get();\n  };\n  _proto.set = function set(key, value) {\n    var hasKey = this.has_(key);\n    if (hasInterceptors(this)) {\n      var change = interceptChange(this, {\n        type: hasKey ? UPDATE : ADD,\n        object: this,\n        newValue: value,\n        name: key\n      });\n      if (!change) {\n        return this;\n      }\n      value = change.newValue;\n    }\n    if (hasKey) {\n      this.updateValue_(key, value);\n    } else {\n      this.addValue_(key, value);\n    }\n    return this;\n  };\n  _proto[\"delete\"] = function _delete(key) {\n    var _this3 = this;\n    checkIfStateModificationsAreAllowed(this.keysAtom_);\n    if (hasInterceptors(this)) {\n      var change = interceptChange(this, {\n        type: DELETE,\n        object: this,\n        name: key\n      });\n      if (!change) {\n        return false;\n      }\n    }\n    if (this.has_(key)) {\n      var notifySpy = isSpyEnabled();\n      var notify = hasListeners(this);\n      var _change = notify || notifySpy ? {\n        observableKind: \"map\",\n        debugObjectName: this.name_,\n        type: DELETE,\n        object: this,\n        oldValue: this.data_.get(key).value_,\n        name: key\n      } : null;\n      if ( notifySpy) {\n        spyReportStart(_change);\n      } // TODO fix type\n      transaction(function () {\n        var _this3$hasMap_$get;\n        _this3.keysAtom_.reportChanged();\n        (_this3$hasMap_$get = _this3.hasMap_.get(key)) == null ? void 0 : _this3$hasMap_$get.setNewValue_(false);\n        var observable = _this3.data_.get(key);\n        observable.setNewValue_(undefined);\n        _this3.data_[\"delete\"](key);\n      });\n      if (notify) {\n        notifyListeners(this, _change);\n      }\n      if ( notifySpy) {\n        spyReportEnd();\n      }\n      return true;\n    }\n    return false;\n  };\n  _proto.updateValue_ = function updateValue_(key, newValue) {\n    var observable = this.data_.get(key);\n    newValue = observable.prepareNewValue_(newValue);\n    if (newValue !== globalState.UNCHANGED) {\n      var notifySpy = isSpyEnabled();\n      var notify = hasListeners(this);\n      var change = notify || notifySpy ? {\n        observableKind: \"map\",\n        debugObjectName: this.name_,\n        type: UPDATE,\n        object: this,\n        oldValue: observable.value_,\n        name: key,\n        newValue: newValue\n      } : null;\n      if ( notifySpy) {\n        spyReportStart(change);\n      } // TODO fix type\n      observable.setNewValue_(newValue);\n      if (notify) {\n        notifyListeners(this, change);\n      }\n      if ( notifySpy) {\n        spyReportEnd();\n      }\n    }\n  };\n  _proto.addValue_ = function addValue_(key, newValue) {\n    var _this4 = this;\n    checkIfStateModificationsAreAllowed(this.keysAtom_);\n    transaction(function () {\n      var _this4$hasMap_$get;\n      var observable = new ObservableValue(newValue, _this4.enhancer_,  _this4.name_ + \".\" + stringifyKey(key) , false);\n      _this4.data_.set(key, observable);\n      newValue = observable.value_; // value might have been changed\n      (_this4$hasMap_$get = _this4.hasMap_.get(key)) == null ? void 0 : _this4$hasMap_$get.setNewValue_(true);\n      _this4.keysAtom_.reportChanged();\n    });\n    var notifySpy = isSpyEnabled();\n    var notify = hasListeners(this);\n    var change = notify || notifySpy ? {\n      observableKind: \"map\",\n      debugObjectName: this.name_,\n      type: ADD,\n      object: this,\n      name: key,\n      newValue: newValue\n    } : null;\n    if ( notifySpy) {\n      spyReportStart(change);\n    } // TODO fix type\n    if (notify) {\n      notifyListeners(this, change);\n    }\n    if ( notifySpy) {\n      spyReportEnd();\n    }\n  };\n  _proto.get = function get(key) {\n    if (this.has(key)) {\n      return this.dehanceValue_(this.data_.get(key).get());\n    }\n    return this.dehanceValue_(undefined);\n  };\n  _proto.dehanceValue_ = function dehanceValue_(value) {\n    if (this.dehancer !== undefined) {\n      return this.dehancer(value);\n    }\n    return value;\n  };\n  _proto.keys = function keys() {\n    this.keysAtom_.reportObserved();\n    return this.data_.keys();\n  };\n  _proto.values = function values() {\n    var self = this;\n    var keys = this.keys();\n    return makeIterable({\n      next: function next() {\n        var _keys$next = keys.next(),\n          done = _keys$next.done,\n          value = _keys$next.value;\n        return {\n          done: done,\n          value: done ? undefined : self.get(value)\n        };\n      }\n    });\n  };\n  _proto.entries = function entries() {\n    var self = this;\n    var keys = this.keys();\n    return makeIterable({\n      next: function next() {\n        var _keys$next2 = keys.next(),\n          done = _keys$next2.done,\n          value = _keys$next2.value;\n        return {\n          done: done,\n          value: done ? undefined : [value, self.get(value)]\n        };\n      }\n    });\n  };\n  _proto[_Symbol$iterator] = function () {\n    return this.entries();\n  };\n  _proto.forEach = function forEach(callback, thisArg) {\n    for (var _iterator = _createForOfIteratorHelperLoose(this), _step; !(_step = _iterator()).done;) {\n      var _step$value = _step.value,\n        key = _step$value[0],\n        value = _step$value[1];\n      callback.call(thisArg, value, key, this);\n    }\n  }\n  /** Merge another object into this object, returns this. */;\n  _proto.merge = function merge(other) {\n    var _this5 = this;\n    if (isObservableMap(other)) {\n      other = new Map(other);\n    }\n    transaction(function () {\n      if (isPlainObject(other)) {\n        getPlainObjectKeys(other).forEach(function (key) {\n          return _this5.set(key, other[key]);\n        });\n      } else if (Array.isArray(other)) {\n        other.forEach(function (_ref) {\n          var key = _ref[0],\n            value = _ref[1];\n          return _this5.set(key, value);\n        });\n      } else if (isES6Map(other)) {\n        if (other.constructor !== Map) {\n          die(19, other);\n        }\n        other.forEach(function (value, key) {\n          return _this5.set(key, value);\n        });\n      } else if (other !== null && other !== undefined) {\n        die(20, other);\n      }\n    });\n    return this;\n  };\n  _proto.clear = function clear() {\n    var _this6 = this;\n    transaction(function () {\n      untracked(function () {\n        for (var _iterator2 = _createForOfIteratorHelperLoose(_this6.keys()), _step2; !(_step2 = _iterator2()).done;) {\n          var key = _step2.value;\n          _this6[\"delete\"](key);\n        }\n      });\n    });\n  };\n  _proto.replace = function replace(values) {\n    var _this7 = this;\n    // Implementation requirements:\n    // - respect ordering of replacement map\n    // - allow interceptors to run and potentially prevent individual operations\n    // - don't recreate observables that already exist in original map (so we don't destroy existing subscriptions)\n    // - don't _keysAtom.reportChanged if the keys of resulting map are indentical (order matters!)\n    // - note that result map may differ from replacement map due to the interceptors\n    transaction(function () {\n      // Convert to map so we can do quick key lookups\n      var replacementMap = convertToMap(values);\n      var orderedData = new Map();\n      // Used for optimization\n      var keysReportChangedCalled = false;\n      // Delete keys that don't exist in replacement map\n      // if the key deletion is prevented by interceptor\n      // add entry at the beginning of the result map\n      for (var _iterator3 = _createForOfIteratorHelperLoose(_this7.data_.keys()), _step3; !(_step3 = _iterator3()).done;) {\n        var key = _step3.value;\n        // Concurrently iterating/deleting keys\n        // iterator should handle this correctly\n        if (!replacementMap.has(key)) {\n          var deleted = _this7[\"delete\"](key);\n          // Was the key removed?\n          if (deleted) {\n            // _keysAtom.reportChanged() was already called\n            keysReportChangedCalled = true;\n          } else {\n            // Delete prevented by interceptor\n            var value = _this7.data_.get(key);\n            orderedData.set(key, value);\n          }\n        }\n      }\n      // Merge entries\n      for (var _iterator4 = _createForOfIteratorHelperLoose(replacementMap.entries()), _step4; !(_step4 = _iterator4()).done;) {\n        var _step4$value = _step4.value,\n          _key = _step4$value[0],\n          _value = _step4$value[1];\n        // We will want to know whether a new key is added\n        var keyExisted = _this7.data_.has(_key);\n        // Add or update value\n        _this7.set(_key, _value);\n        // The addition could have been prevent by interceptor\n        if (_this7.data_.has(_key)) {\n          // The update could have been prevented by interceptor\n          // and also we want to preserve existing values\n          // so use value from _data map (instead of replacement map)\n          var _value2 = _this7.data_.get(_key);\n          orderedData.set(_key, _value2);\n          // Was a new key added?\n          if (!keyExisted) {\n            // _keysAtom.reportChanged() was already called\n            keysReportChangedCalled = true;\n          }\n        }\n      }\n      // Check for possible key order change\n      if (!keysReportChangedCalled) {\n        if (_this7.data_.size !== orderedData.size) {\n          // If size differs, keys are definitely modified\n          _this7.keysAtom_.reportChanged();\n        } else {\n          var iter1 = _this7.data_.keys();\n          var iter2 = orderedData.keys();\n          var next1 = iter1.next();\n          var next2 = iter2.next();\n          while (!next1.done) {\n            if (next1.value !== next2.value) {\n              _this7.keysAtom_.reportChanged();\n              break;\n            }\n            next1 = iter1.next();\n            next2 = iter2.next();\n          }\n        }\n      }\n      // Use correctly ordered map\n      _this7.data_ = orderedData;\n    });\n    return this;\n  };\n  _proto.toString = function toString() {\n    return \"[object ObservableMap]\";\n  };\n  _proto.toJSON = function toJSON() {\n    return Array.from(this);\n  };\n  /**\n   * Observes this object. Triggers for the events 'add', 'update' and 'delete'.\n   * See: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/observe\n   * for callback details\n   */\n  _proto.observe_ = function observe_(listener, fireImmediately) {\n    if ( fireImmediately === true) {\n      die(\"`observe` doesn't support fireImmediately=true in combination with maps.\");\n    }\n    return registerListener(this, listener);\n  };\n  _proto.intercept_ = function intercept_(handler) {\n    return registerInterceptor(this, handler);\n  };\n  _createClass(ObservableMap, [{\n    key: \"size\",\n    get: function get() {\n      this.keysAtom_.reportObserved();\n      return this.data_.size;\n    }\n  }, {\n    key: _Symbol$toStringTag,\n    get: function get() {\n      return \"Map\";\n    }\n  }]);\n  return ObservableMap;\n}();\n// eslint-disable-next-line\nvar isObservableMap = /*#__PURE__*/createInstanceofPredicate(\"ObservableMap\", ObservableMap);\nfunction convertToMap(dataStructure) {\n  if (isES6Map(dataStructure) || isObservableMap(dataStructure)) {\n    return dataStructure;\n  } else if (Array.isArray(dataStructure)) {\n    return new Map(dataStructure);\n  } else if (isPlainObject(dataStructure)) {\n    var map = new Map();\n    for (var key in dataStructure) {\n      map.set(key, dataStructure[key]);\n    }\n    return map;\n  } else {\n    return die(21, dataStructure);\n  }\n}\n\nvar _Symbol$iterator$1, _Symbol$toStringTag$1;\nvar ObservableSetMarker = {};\n_Symbol$iterator$1 = Symbol.iterator;\n_Symbol$toStringTag$1 = Symbol.toStringTag;\nvar ObservableSet = /*#__PURE__*/function () {\n  function ObservableSet(initialData, enhancer, name_) {\n    var _this = this;\n    if (enhancer === void 0) {\n      enhancer = deepEnhancer;\n    }\n    if (name_ === void 0) {\n      name_ =  \"ObservableSet@\" + getNextId() ;\n    }\n    this.name_ = void 0;\n    this[$mobx] = ObservableSetMarker;\n    this.data_ = new Set();\n    this.atom_ = void 0;\n    this.changeListeners_ = void 0;\n    this.interceptors_ = void 0;\n    this.dehancer = void 0;\n    this.enhancer_ = void 0;\n    this.name_ = name_;\n    if (!isFunction(Set)) {\n      die(22);\n    }\n    this.enhancer_ = function (newV, oldV) {\n      return enhancer(newV, oldV, name_);\n    };\n    initObservable(function () {\n      _this.atom_ = createAtom(_this.name_);\n      if (initialData) {\n        _this.replace(initialData);\n      }\n    });\n  }\n  var _proto = ObservableSet.prototype;\n  _proto.dehanceValue_ = function dehanceValue_(value) {\n    if (this.dehancer !== undefined) {\n      return this.dehancer(value);\n    }\n    return value;\n  };\n  _proto.clear = function clear() {\n    var _this2 = this;\n    transaction(function () {\n      untracked(function () {\n        for (var _iterator = _createForOfIteratorHelperLoose(_this2.data_.values()), _step; !(_step = _iterator()).done;) {\n          var value = _step.value;\n          _this2[\"delete\"](value);\n        }\n      });\n    });\n  };\n  _proto.forEach = function forEach(callbackFn, thisArg) {\n    for (var _iterator2 = _createForOfIteratorHelperLoose(this), _step2; !(_step2 = _iterator2()).done;) {\n      var value = _step2.value;\n      callbackFn.call(thisArg, value, value, this);\n    }\n  };\n  _proto.add = function add(value) {\n    var _this3 = this;\n    checkIfStateModificationsAreAllowed(this.atom_);\n    if (hasInterceptors(this)) {\n      var change = interceptChange(this, {\n        type: ADD,\n        object: this,\n        newValue: value\n      });\n      if (!change) {\n        return this;\n      }\n      // ideally, value = change.value would be done here, so that values can be\n      // changed by interceptor. Same applies for other Set and Map api's.\n    }\n\n    if (!this.has(value)) {\n      transaction(function () {\n        _this3.data_.add(_this3.enhancer_(value, undefined));\n        _this3.atom_.reportChanged();\n      });\n      var notifySpy =  isSpyEnabled();\n      var notify = hasListeners(this);\n      var _change = notify || notifySpy ? {\n        observableKind: \"set\",\n        debugObjectName: this.name_,\n        type: ADD,\n        object: this,\n        newValue: value\n      } : null;\n      if (notifySpy && \"development\" !== \"production\") {\n        spyReportStart(_change);\n      }\n      if (notify) {\n        notifyListeners(this, _change);\n      }\n      if (notifySpy && \"development\" !== \"production\") {\n        spyReportEnd();\n      }\n    }\n    return this;\n  };\n  _proto[\"delete\"] = function _delete(value) {\n    var _this4 = this;\n    if (hasInterceptors(this)) {\n      var change = interceptChange(this, {\n        type: DELETE,\n        object: this,\n        oldValue: value\n      });\n      if (!change) {\n        return false;\n      }\n    }\n    if (this.has(value)) {\n      var notifySpy =  isSpyEnabled();\n      var notify = hasListeners(this);\n      var _change2 = notify || notifySpy ? {\n        observableKind: \"set\",\n        debugObjectName: this.name_,\n        type: DELETE,\n        object: this,\n        oldValue: value\n      } : null;\n      if (notifySpy && \"development\" !== \"production\") {\n        spyReportStart(_change2);\n      }\n      transaction(function () {\n        _this4.atom_.reportChanged();\n        _this4.data_[\"delete\"](value);\n      });\n      if (notify) {\n        notifyListeners(this, _change2);\n      }\n      if (notifySpy && \"development\" !== \"production\") {\n        spyReportEnd();\n      }\n      return true;\n    }\n    return false;\n  };\n  _proto.has = function has(value) {\n    this.atom_.reportObserved();\n    return this.data_.has(this.dehanceValue_(value));\n  };\n  _proto.entries = function entries() {\n    var nextIndex = 0;\n    var keys = Array.from(this.keys());\n    var values = Array.from(this.values());\n    return makeIterable({\n      next: function next() {\n        var index = nextIndex;\n        nextIndex += 1;\n        return index < values.length ? {\n          value: [keys[index], values[index]],\n          done: false\n        } : {\n          done: true\n        };\n      }\n    });\n  };\n  _proto.keys = function keys() {\n    return this.values();\n  };\n  _proto.values = function values() {\n    this.atom_.reportObserved();\n    var self = this;\n    var nextIndex = 0;\n    var observableValues = Array.from(this.data_.values());\n    return makeIterable({\n      next: function next() {\n        return nextIndex < observableValues.length ? {\n          value: self.dehanceValue_(observableValues[nextIndex++]),\n          done: false\n        } : {\n          done: true\n        };\n      }\n    });\n  };\n  _proto.replace = function replace(other) {\n    var _this5 = this;\n    if (isObservableSet(other)) {\n      other = new Set(other);\n    }\n    transaction(function () {\n      if (Array.isArray(other)) {\n        _this5.clear();\n        other.forEach(function (value) {\n          return _this5.add(value);\n        });\n      } else if (isES6Set(other)) {\n        _this5.clear();\n        other.forEach(function (value) {\n          return _this5.add(value);\n        });\n      } else if (other !== null && other !== undefined) {\n        die(\"Cannot initialize set from \" + other);\n      }\n    });\n    return this;\n  };\n  _proto.observe_ = function observe_(listener, fireImmediately) {\n    // ... 'fireImmediately' could also be true?\n    if ( fireImmediately === true) {\n      die(\"`observe` doesn't support fireImmediately=true in combination with sets.\");\n    }\n    return registerListener(this, listener);\n  };\n  _proto.intercept_ = function intercept_(handler) {\n    return registerInterceptor(this, handler);\n  };\n  _proto.toJSON = function toJSON() {\n    return Array.from(this);\n  };\n  _proto.toString = function toString() {\n    return \"[object ObservableSet]\";\n  };\n  _proto[_Symbol$iterator$1] = function () {\n    return this.values();\n  };\n  _createClass(ObservableSet, [{\n    key: \"size\",\n    get: function get() {\n      this.atom_.reportObserved();\n      return this.data_.size;\n    }\n  }, {\n    key: _Symbol$toStringTag$1,\n    get: function get() {\n      return \"Set\";\n    }\n  }]);\n  return ObservableSet;\n}();\n// eslint-disable-next-line\nvar isObservableSet = /*#__PURE__*/createInstanceofPredicate(\"ObservableSet\", ObservableSet);\n\nvar descriptorCache = /*#__PURE__*/Object.create(null);\nvar REMOVE = \"remove\";\nvar ObservableObjectAdministration = /*#__PURE__*/function () {\n  function ObservableObjectAdministration(target_, values_, name_,\n  // Used anytime annotation is not explicitely provided\n  defaultAnnotation_) {\n    if (values_ === void 0) {\n      values_ = new Map();\n    }\n    if (defaultAnnotation_ === void 0) {\n      defaultAnnotation_ = autoAnnotation;\n    }\n    this.target_ = void 0;\n    this.values_ = void 0;\n    this.name_ = void 0;\n    this.defaultAnnotation_ = void 0;\n    this.keysAtom_ = void 0;\n    this.changeListeners_ = void 0;\n    this.interceptors_ = void 0;\n    this.proxy_ = void 0;\n    this.isPlainObject_ = void 0;\n    this.appliedAnnotations_ = void 0;\n    this.pendingKeys_ = void 0;\n    this.target_ = target_;\n    this.values_ = values_;\n    this.name_ = name_;\n    this.defaultAnnotation_ = defaultAnnotation_;\n    this.keysAtom_ = new Atom( this.name_ + \".keys\" );\n    // Optimization: we use this frequently\n    this.isPlainObject_ = isPlainObject(this.target_);\n    if ( !isAnnotation(this.defaultAnnotation_)) {\n      die(\"defaultAnnotation must be valid annotation\");\n    }\n    {\n      // Prepare structure for tracking which fields were already annotated\n      this.appliedAnnotations_ = {};\n    }\n  }\n  var _proto = ObservableObjectAdministration.prototype;\n  _proto.getObservablePropValue_ = function getObservablePropValue_(key) {\n    return this.values_.get(key).get();\n  };\n  _proto.setObservablePropValue_ = function setObservablePropValue_(key, newValue) {\n    var observable = this.values_.get(key);\n    if (observable instanceof ComputedValue) {\n      observable.set(newValue);\n      return true;\n    }\n    // intercept\n    if (hasInterceptors(this)) {\n      var change = interceptChange(this, {\n        type: UPDATE,\n        object: this.proxy_ || this.target_,\n        name: key,\n        newValue: newValue\n      });\n      if (!change) {\n        return null;\n      }\n      newValue = change.newValue;\n    }\n    newValue = observable.prepareNewValue_(newValue);\n    // notify spy & observers\n    if (newValue !== globalState.UNCHANGED) {\n      var notify = hasListeners(this);\n      var notifySpy =  isSpyEnabled();\n      var _change = notify || notifySpy ? {\n        type: UPDATE,\n        observableKind: \"object\",\n        debugObjectName: this.name_,\n        object: this.proxy_ || this.target_,\n        oldValue: observable.value_,\n        name: key,\n        newValue: newValue\n      } : null;\n      if ( notifySpy) {\n        spyReportStart(_change);\n      }\n      observable.setNewValue_(newValue);\n      if (notify) {\n        notifyListeners(this, _change);\n      }\n      if ( notifySpy) {\n        spyReportEnd();\n      }\n    }\n    return true;\n  };\n  _proto.get_ = function get_(key) {\n    if (globalState.trackingDerivation && !hasProp(this.target_, key)) {\n      // Key doesn't exist yet, subscribe for it in case it's added later\n      this.has_(key);\n    }\n    return this.target_[key];\n  }\n  /**\n   * @param {PropertyKey} key\n   * @param {any} value\n   * @param {Annotation|boolean} annotation true - use default annotation, false - copy as is\n   * @param {boolean} proxyTrap whether it's called from proxy trap\n   * @returns {boolean|null} true on success, false on failure (proxyTrap + non-configurable), null when cancelled by interceptor\n   */;\n  _proto.set_ = function set_(key, value, proxyTrap) {\n    if (proxyTrap === void 0) {\n      proxyTrap = false;\n    }\n    // Don't use .has(key) - we care about own\n    if (hasProp(this.target_, key)) {\n      // Existing prop\n      if (this.values_.has(key)) {\n        // Observable (can be intercepted)\n        return this.setObservablePropValue_(key, value);\n      } else if (proxyTrap) {\n        // Non-observable - proxy\n        return Reflect.set(this.target_, key, value);\n      } else {\n        // Non-observable\n        this.target_[key] = value;\n        return true;\n      }\n    } else {\n      // New prop\n      return this.extend_(key, {\n        value: value,\n        enumerable: true,\n        writable: true,\n        configurable: true\n      }, this.defaultAnnotation_, proxyTrap);\n    }\n  }\n  // Trap for \"in\"\n  ;\n  _proto.has_ = function has_(key) {\n    if (!globalState.trackingDerivation) {\n      // Skip key subscription outside derivation\n      return key in this.target_;\n    }\n    this.pendingKeys_ || (this.pendingKeys_ = new Map());\n    var entry = this.pendingKeys_.get(key);\n    if (!entry) {\n      entry = new ObservableValue(key in this.target_, referenceEnhancer,  this.name_ + \".\" + stringifyKey(key) + \"?\" , false);\n      this.pendingKeys_.set(key, entry);\n    }\n    return entry.get();\n  }\n  /**\n   * @param {PropertyKey} key\n   * @param {Annotation|boolean} annotation true - use default annotation, false - ignore prop\n   */;\n  _proto.make_ = function make_(key, annotation) {\n    if (annotation === true) {\n      annotation = this.defaultAnnotation_;\n    }\n    if (annotation === false) {\n      return;\n    }\n    assertAnnotable(this, annotation, key);\n    if (!(key in this.target_)) {\n      var _this$target_$storedA;\n      // Throw on missing key, except for decorators:\n      // Decorator annotations are collected from whole prototype chain.\n      // When called from super() some props may not exist yet.\n      // However we don't have to worry about missing prop,\n      // because the decorator must have been applied to something.\n      if ((_this$target_$storedA = this.target_[storedAnnotationsSymbol]) != null && _this$target_$storedA[key]) {\n        return; // will be annotated by subclass constructor\n      } else {\n        die(1, annotation.annotationType_, this.name_ + \".\" + key.toString());\n      }\n    }\n    var source = this.target_;\n    while (source && source !== objectPrototype) {\n      var descriptor = getDescriptor(source, key);\n      if (descriptor) {\n        var outcome = annotation.make_(this, key, descriptor, source);\n        if (outcome === 0 /* MakeResult.Cancel */) {\n          return;\n        }\n        if (outcome === 1 /* MakeResult.Break */) {\n          break;\n        }\n      }\n      source = Object.getPrototypeOf(source);\n    }\n    recordAnnotationApplied(this, annotation, key);\n  }\n  /**\n   * @param {PropertyKey} key\n   * @param {PropertyDescriptor} descriptor\n   * @param {Annotation|boolean} annotation true - use default annotation, false - copy as is\n   * @param {boolean} proxyTrap whether it's called from proxy trap\n   * @returns {boolean|null} true on success, false on failure (proxyTrap + non-configurable), null when cancelled by interceptor\n   */;\n  _proto.extend_ = function extend_(key, descriptor, annotation, proxyTrap) {\n    if (proxyTrap === void 0) {\n      proxyTrap = false;\n    }\n    if (annotation === true) {\n      annotation = this.defaultAnnotation_;\n    }\n    if (annotation === false) {\n      return this.defineProperty_(key, descriptor, proxyTrap);\n    }\n    assertAnnotable(this, annotation, key);\n    var outcome = annotation.extend_(this, key, descriptor, proxyTrap);\n    if (outcome) {\n      recordAnnotationApplied(this, annotation, key);\n    }\n    return outcome;\n  }\n  /**\n   * @param {PropertyKey} key\n   * @param {PropertyDescriptor} descriptor\n   * @param {boolean} proxyTrap whether it's called from proxy trap\n   * @returns {boolean|null} true on success, false on failure (proxyTrap + non-configurable), null when cancelled by interceptor\n   */;\n  _proto.defineProperty_ = function defineProperty_(key, descriptor, proxyTrap) {\n    if (proxyTrap === void 0) {\n      proxyTrap = false;\n    }\n    checkIfStateModificationsAreAllowed(this.keysAtom_);\n    try {\n      startBatch();\n      // Delete\n      var deleteOutcome = this.delete_(key);\n      if (!deleteOutcome) {\n        // Failure or intercepted\n        return deleteOutcome;\n      }\n      // ADD interceptor\n      if (hasInterceptors(this)) {\n        var change = interceptChange(this, {\n          object: this.proxy_ || this.target_,\n          name: key,\n          type: ADD,\n          newValue: descriptor.value\n        });\n        if (!change) {\n          return null;\n        }\n        var newValue = change.newValue;\n        if (descriptor.value !== newValue) {\n          descriptor = _extends({}, descriptor, {\n            value: newValue\n          });\n        }\n      }\n      // Define\n      if (proxyTrap) {\n        if (!Reflect.defineProperty(this.target_, key, descriptor)) {\n          return false;\n        }\n      } else {\n        defineProperty(this.target_, key, descriptor);\n      }\n      // Notify\n      this.notifyPropertyAddition_(key, descriptor.value);\n    } finally {\n      endBatch();\n    }\n    return true;\n  }\n  // If original descriptor becomes relevant, move this to annotation directly\n  ;\n  _proto.defineObservableProperty_ = function defineObservableProperty_(key, value, enhancer, proxyTrap) {\n    if (proxyTrap === void 0) {\n      proxyTrap = false;\n    }\n    checkIfStateModificationsAreAllowed(this.keysAtom_);\n    try {\n      startBatch();\n      // Delete\n      var deleteOutcome = this.delete_(key);\n      if (!deleteOutcome) {\n        // Failure or intercepted\n        return deleteOutcome;\n      }\n      // ADD interceptor\n      if (hasInterceptors(this)) {\n        var change = interceptChange(this, {\n          object: this.proxy_ || this.target_,\n          name: key,\n          type: ADD,\n          newValue: value\n        });\n        if (!change) {\n          return null;\n        }\n        value = change.newValue;\n      }\n      var cachedDescriptor = getCachedObservablePropDescriptor(key);\n      var descriptor = {\n        configurable: globalState.safeDescriptors ? this.isPlainObject_ : true,\n        enumerable: true,\n        get: cachedDescriptor.get,\n        set: cachedDescriptor.set\n      };\n      // Define\n      if (proxyTrap) {\n        if (!Reflect.defineProperty(this.target_, key, descriptor)) {\n          return false;\n        }\n      } else {\n        defineProperty(this.target_, key, descriptor);\n      }\n      var observable = new ObservableValue(value, enhancer, \"development\" !== \"production\" ? this.name_ + \".\" + key.toString() : \"ObservableObject.key\", false);\n      this.values_.set(key, observable);\n      // Notify (value possibly changed by ObservableValue)\n      this.notifyPropertyAddition_(key, observable.value_);\n    } finally {\n      endBatch();\n    }\n    return true;\n  }\n  // If original descriptor becomes relevant, move this to annotation directly\n  ;\n  _proto.defineComputedProperty_ = function defineComputedProperty_(key, options, proxyTrap) {\n    if (proxyTrap === void 0) {\n      proxyTrap = false;\n    }\n    checkIfStateModificationsAreAllowed(this.keysAtom_);\n    try {\n      startBatch();\n      // Delete\n      var deleteOutcome = this.delete_(key);\n      if (!deleteOutcome) {\n        // Failure or intercepted\n        return deleteOutcome;\n      }\n      // ADD interceptor\n      if (hasInterceptors(this)) {\n        var change = interceptChange(this, {\n          object: this.proxy_ || this.target_,\n          name: key,\n          type: ADD,\n          newValue: undefined\n        });\n        if (!change) {\n          return null;\n        }\n      }\n      options.name || (options.name = \"development\" !== \"production\" ? this.name_ + \".\" + key.toString() : \"ObservableObject.key\");\n      options.context = this.proxy_ || this.target_;\n      var cachedDescriptor = getCachedObservablePropDescriptor(key);\n      var descriptor = {\n        configurable: globalState.safeDescriptors ? this.isPlainObject_ : true,\n        enumerable: false,\n        get: cachedDescriptor.get,\n        set: cachedDescriptor.set\n      };\n      // Define\n      if (proxyTrap) {\n        if (!Reflect.defineProperty(this.target_, key, descriptor)) {\n          return false;\n        }\n      } else {\n        defineProperty(this.target_, key, descriptor);\n      }\n      this.values_.set(key, new ComputedValue(options));\n      // Notify\n      this.notifyPropertyAddition_(key, undefined);\n    } finally {\n      endBatch();\n    }\n    return true;\n  }\n  /**\n   * @param {PropertyKey} key\n   * @param {PropertyDescriptor} descriptor\n   * @param {boolean} proxyTrap whether it's called from proxy trap\n   * @returns {boolean|null} true on success, false on failure (proxyTrap + non-configurable), null when cancelled by interceptor\n   */;\n  _proto.delete_ = function delete_(key, proxyTrap) {\n    if (proxyTrap === void 0) {\n      proxyTrap = false;\n    }\n    checkIfStateModificationsAreAllowed(this.keysAtom_);\n    // No such prop\n    if (!hasProp(this.target_, key)) {\n      return true;\n    }\n    // Intercept\n    if (hasInterceptors(this)) {\n      var change = interceptChange(this, {\n        object: this.proxy_ || this.target_,\n        name: key,\n        type: REMOVE\n      });\n      // Cancelled\n      if (!change) {\n        return null;\n      }\n    }\n    // Delete\n    try {\n      var _this$pendingKeys_, _this$pendingKeys_$ge;\n      startBatch();\n      var notify = hasListeners(this);\n      var notifySpy = \"development\" !== \"production\" && isSpyEnabled();\n      var observable = this.values_.get(key);\n      // Value needed for spies/listeners\n      var value = undefined;\n      // Optimization: don't pull the value unless we will need it\n      if (!observable && (notify || notifySpy)) {\n        var _getDescriptor;\n        value = (_getDescriptor = getDescriptor(this.target_, key)) == null ? void 0 : _getDescriptor.value;\n      }\n      // delete prop (do first, may fail)\n      if (proxyTrap) {\n        if (!Reflect.deleteProperty(this.target_, key)) {\n          return false;\n        }\n      } else {\n        delete this.target_[key];\n      }\n      // Allow re-annotating this field\n      if (\"development\" !== \"production\") {\n        delete this.appliedAnnotations_[key];\n      }\n      // Clear observable\n      if (observable) {\n        this.values_[\"delete\"](key);\n        // for computed, value is undefined\n        if (observable instanceof ObservableValue) {\n          value = observable.value_;\n        }\n        // Notify: autorun(() => obj[key]), see #1796\n        propagateChanged(observable);\n      }\n      // Notify \"keys/entries/values\" observers\n      this.keysAtom_.reportChanged();\n      // Notify \"has\" observers\n      // \"in\" as it may still exist in proto\n      (_this$pendingKeys_ = this.pendingKeys_) == null ? void 0 : (_this$pendingKeys_$ge = _this$pendingKeys_.get(key)) == null ? void 0 : _this$pendingKeys_$ge.set(key in this.target_);\n      // Notify spies/listeners\n      if (notify || notifySpy) {\n        var _change2 = {\n          type: REMOVE,\n          observableKind: \"object\",\n          object: this.proxy_ || this.target_,\n          debugObjectName: this.name_,\n          oldValue: value,\n          name: key\n        };\n        if (\"development\" !== \"production\" && notifySpy) {\n          spyReportStart(_change2);\n        }\n        if (notify) {\n          notifyListeners(this, _change2);\n        }\n        if (\"development\" !== \"production\" && notifySpy) {\n          spyReportEnd();\n        }\n      }\n    } finally {\n      endBatch();\n    }\n    return true;\n  }\n  /**\n   * Observes this object. Triggers for the events 'add', 'update' and 'delete'.\n   * See: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/observe\n   * for callback details\n   */;\n  _proto.observe_ = function observe_(callback, fireImmediately) {\n    if ( fireImmediately === true) {\n      die(\"`observe` doesn't support the fire immediately property for observable objects.\");\n    }\n    return registerListener(this, callback);\n  };\n  _proto.intercept_ = function intercept_(handler) {\n    return registerInterceptor(this, handler);\n  };\n  _proto.notifyPropertyAddition_ = function notifyPropertyAddition_(key, value) {\n    var _this$pendingKeys_2, _this$pendingKeys_2$g;\n    var notify = hasListeners(this);\n    var notifySpy =  isSpyEnabled();\n    if (notify || notifySpy) {\n      var change = notify || notifySpy ? {\n        type: ADD,\n        observableKind: \"object\",\n        debugObjectName: this.name_,\n        object: this.proxy_ || this.target_,\n        name: key,\n        newValue: value\n      } : null;\n      if ( notifySpy) {\n        spyReportStart(change);\n      }\n      if (notify) {\n        notifyListeners(this, change);\n      }\n      if ( notifySpy) {\n        spyReportEnd();\n      }\n    }\n    (_this$pendingKeys_2 = this.pendingKeys_) == null ? void 0 : (_this$pendingKeys_2$g = _this$pendingKeys_2.get(key)) == null ? void 0 : _this$pendingKeys_2$g.set(true);\n    // Notify \"keys/entries/values\" observers\n    this.keysAtom_.reportChanged();\n  };\n  _proto.ownKeys_ = function ownKeys_() {\n    this.keysAtom_.reportObserved();\n    return ownKeys(this.target_);\n  };\n  _proto.keys_ = function keys_() {\n    // Returns enumerable && own, but unfortunately keysAtom will report on ANY key change.\n    // There is no way to distinguish between Object.keys(object) and Reflect.ownKeys(object) - both are handled by ownKeys trap.\n    // We can either over-report in Object.keys(object) or under-report in Reflect.ownKeys(object)\n    // We choose to over-report in Object.keys(object), because:\n    // - typically it's used with simple data objects\n    // - when symbolic/non-enumerable keys are relevant Reflect.ownKeys works as expected\n    this.keysAtom_.reportObserved();\n    return Object.keys(this.target_);\n  };\n  return ObservableObjectAdministration;\n}();\nfunction asObservableObject(target, options) {\n  var _options$name;\n  if ( options && isObservableObject(target)) {\n    die(\"Options can't be provided for already observable objects.\");\n  }\n  if (hasProp(target, $mobx)) {\n    if ( !(getAdministration(target) instanceof ObservableObjectAdministration)) {\n      die(\"Cannot convert '\" + getDebugName(target) + \"' into observable object:\" + \"\\nThe target is already observable of different type.\" + \"\\nExtending builtins is not supported.\");\n    }\n    return target;\n  }\n  if ( !Object.isExtensible(target)) {\n    die(\"Cannot make the designated object observable; it is not extensible\");\n  }\n  var name = (_options$name = options == null ? void 0 : options.name) != null ? _options$name :  (isPlainObject(target) ? \"ObservableObject\" : target.constructor.name) + \"@\" + getNextId() ;\n  var adm = new ObservableObjectAdministration(target, new Map(), String(name), getAnnotationFromOptions(options));\n  addHiddenProp(target, $mobx, adm);\n  return target;\n}\nvar isObservableObjectAdministration = /*#__PURE__*/createInstanceofPredicate(\"ObservableObjectAdministration\", ObservableObjectAdministration);\nfunction getCachedObservablePropDescriptor(key) {\n  return descriptorCache[key] || (descriptorCache[key] = {\n    get: function get() {\n      return this[$mobx].getObservablePropValue_(key);\n    },\n    set: function set(value) {\n      return this[$mobx].setObservablePropValue_(key, value);\n    }\n  });\n}\nfunction isObservableObject(thing) {\n  if (isObject(thing)) {\n    return isObservableObjectAdministration(thing[$mobx]);\n  }\n  return false;\n}\nfunction recordAnnotationApplied(adm, annotation, key) {\n  var _adm$target_$storedAn;\n  {\n    adm.appliedAnnotations_[key] = annotation;\n  }\n  // Remove applied decorator annotation so we don't try to apply it again in subclass constructor\n  (_adm$target_$storedAn = adm.target_[storedAnnotationsSymbol]) == null ? true : delete _adm$target_$storedAn[key];\n}\nfunction assertAnnotable(adm, annotation, key) {\n  // Valid annotation\n  if ( !isAnnotation(annotation)) {\n    die(\"Cannot annotate '\" + adm.name_ + \".\" + key.toString() + \"': Invalid annotation.\");\n  }\n  /*\n  // Configurable, not sealed, not frozen\n  // Possibly not needed, just a little better error then the one thrown by engine.\n  // Cases where this would be useful the most (subclass field initializer) are not interceptable by this.\n  if (__DEV__) {\n      const configurable = getDescriptor(adm.target_, key)?.configurable\n      const frozen = Object.isFrozen(adm.target_)\n      const sealed = Object.isSealed(adm.target_)\n      if (!configurable || frozen || sealed) {\n          const fieldName = `${adm.name_}.${key.toString()}`\n          const requestedAnnotationType = annotation.annotationType_\n          let error = `Cannot apply '${requestedAnnotationType}' to '${fieldName}':`\n          if (frozen) {\n              error += `\\nObject is frozen.`\n          }\n          if (sealed) {\n              error += `\\nObject is sealed.`\n          }\n          if (!configurable) {\n              error += `\\nproperty is not configurable.`\n              // Mention only if caused by us to avoid confusion\n              if (hasProp(adm.appliedAnnotations!, key)) {\n                  error += `\\nTo prevent accidental re-definition of a field by a subclass, `\n                  error += `all annotated fields of non-plain objects (classes) are not configurable.`\n              }\n          }\n          die(error)\n      }\n  }\n  */\n  // Not annotated\n  if ( !isOverride(annotation) && hasProp(adm.appliedAnnotations_, key)) {\n    var fieldName = adm.name_ + \".\" + key.toString();\n    var currentAnnotationType = adm.appliedAnnotations_[key].annotationType_;\n    var requestedAnnotationType = annotation.annotationType_;\n    die(\"Cannot apply '\" + requestedAnnotationType + \"' to '\" + fieldName + \"':\" + (\"\\nThe field is already annotated with '\" + currentAnnotationType + \"'.\") + \"\\nRe-annotating fields is not allowed.\" + \"\\nUse 'override' annotation for methods overridden by subclass.\");\n  }\n}\n\n// Bug in safari 9.* (or iOS 9 safari mobile). See #364\nvar ENTRY_0 = /*#__PURE__*/createArrayEntryDescriptor(0);\nvar safariPrototypeSetterInheritanceBug = /*#__PURE__*/function () {\n  var v = false;\n  var p = {};\n  Object.defineProperty(p, \"0\", {\n    set: function set() {\n      v = true;\n    }\n  });\n  /*#__PURE__*/Object.create(p)[\"0\"] = 1;\n  return v === false;\n}();\n/**\n * This array buffer contains two lists of properties, so that all arrays\n * can recycle their property definitions, which significantly improves performance of creating\n * properties on the fly.\n */\nvar OBSERVABLE_ARRAY_BUFFER_SIZE = 0;\n// Typescript workaround to make sure ObservableArray extends Array\nvar StubArray = function StubArray() {};\nfunction inherit(ctor, proto) {\n  if (Object.setPrototypeOf) {\n    Object.setPrototypeOf(ctor.prototype, proto);\n  } else if (ctor.prototype.__proto__ !== undefined) {\n    ctor.prototype.__proto__ = proto;\n  } else {\n    ctor.prototype = proto;\n  }\n}\ninherit(StubArray, Array.prototype);\n// Weex proto freeze protection was here,\n// but it is unclear why the hack is need as MobX never changed the prototype\n// anyway, so removed it in V6\nvar LegacyObservableArray = /*#__PURE__*/function (_StubArray, _Symbol$toStringTag, _Symbol$iterator) {\n  _inheritsLoose(LegacyObservableArray, _StubArray);\n  function LegacyObservableArray(initialValues, enhancer, name, owned) {\n    var _this;\n    if (name === void 0) {\n      name =  \"ObservableArray@\" + getNextId() ;\n    }\n    if (owned === void 0) {\n      owned = false;\n    }\n    _this = _StubArray.call(this) || this;\n    initObservable(function () {\n      var adm = new ObservableArrayAdministration(name, enhancer, owned, true);\n      adm.proxy_ = _assertThisInitialized(_this);\n      addHiddenFinalProp(_assertThisInitialized(_this), $mobx, adm);\n      if (initialValues && initialValues.length) {\n        // @ts-ignore\n        _this.spliceWithArray(0, 0, initialValues);\n      }\n      if (safariPrototypeSetterInheritanceBug) {\n        // Seems that Safari won't use numeric prototype setter untill any * numeric property is\n        // defined on the instance. After that it works fine, even if this property is deleted.\n        Object.defineProperty(_assertThisInitialized(_this), \"0\", ENTRY_0);\n      }\n    });\n    return _this;\n  }\n  var _proto = LegacyObservableArray.prototype;\n  _proto.concat = function concat() {\n    this[$mobx].atom_.reportObserved();\n    for (var _len = arguments.length, arrays = new Array(_len), _key = 0; _key < _len; _key++) {\n      arrays[_key] = arguments[_key];\n    }\n    return Array.prototype.concat.apply(this.slice(),\n    //@ts-ignore\n    arrays.map(function (a) {\n      return isObservableArray(a) ? a.slice() : a;\n    }));\n  };\n  _proto[_Symbol$iterator] = function () {\n    var self = this;\n    var nextIndex = 0;\n    return makeIterable({\n      next: function next() {\n        return nextIndex < self.length ? {\n          value: self[nextIndex++],\n          done: false\n        } : {\n          done: true,\n          value: undefined\n        };\n      }\n    });\n  };\n  _createClass(LegacyObservableArray, [{\n    key: \"length\",\n    get: function get() {\n      return this[$mobx].getArrayLength_();\n    },\n    set: function set(newLength) {\n      this[$mobx].setArrayLength_(newLength);\n    }\n  }, {\n    key: _Symbol$toStringTag,\n    get: function get() {\n      return \"Array\";\n    }\n  }]);\n  return LegacyObservableArray;\n}(StubArray, Symbol.toStringTag, Symbol.iterator);\nObject.entries(arrayExtensions).forEach(function (_ref) {\n  var prop = _ref[0],\n    fn = _ref[1];\n  if (prop !== \"concat\") {\n    addHiddenProp(LegacyObservableArray.prototype, prop, fn);\n  }\n});\nfunction createArrayEntryDescriptor(index) {\n  return {\n    enumerable: false,\n    configurable: true,\n    get: function get() {\n      return this[$mobx].get_(index);\n    },\n    set: function set(value) {\n      this[$mobx].set_(index, value);\n    }\n  };\n}\nfunction createArrayBufferItem(index) {\n  defineProperty(LegacyObservableArray.prototype, \"\" + index, createArrayEntryDescriptor(index));\n}\nfunction reserveArrayBuffer(max) {\n  if (max > OBSERVABLE_ARRAY_BUFFER_SIZE) {\n    for (var index = OBSERVABLE_ARRAY_BUFFER_SIZE; index < max + 100; index++) {\n      createArrayBufferItem(index);\n    }\n    OBSERVABLE_ARRAY_BUFFER_SIZE = max;\n  }\n}\nreserveArrayBuffer(1000);\nfunction createLegacyArray(initialValues, enhancer, name) {\n  return new LegacyObservableArray(initialValues, enhancer, name);\n}\n\nfunction getAtom(thing, property) {\n  if (typeof thing === \"object\" && thing !== null) {\n    if (isObservableArray(thing)) {\n      if (property !== undefined) {\n        die(23);\n      }\n      return thing[$mobx].atom_;\n    }\n    if (isObservableSet(thing)) {\n      return thing.atom_;\n    }\n    if (isObservableMap(thing)) {\n      if (property === undefined) {\n        return thing.keysAtom_;\n      }\n      var observable = thing.data_.get(property) || thing.hasMap_.get(property);\n      if (!observable) {\n        die(25, property, getDebugName(thing));\n      }\n      return observable;\n    }\n    if (isObservableObject(thing)) {\n      if (!property) {\n        return die(26);\n      }\n      var _observable = thing[$mobx].values_.get(property);\n      if (!_observable) {\n        die(27, property, getDebugName(thing));\n      }\n      return _observable;\n    }\n    if (isAtom(thing) || isComputedValue(thing) || isReaction(thing)) {\n      return thing;\n    }\n  } else if (isFunction(thing)) {\n    if (isReaction(thing[$mobx])) {\n      // disposer function\n      return thing[$mobx];\n    }\n  }\n  die(28);\n}\nfunction getAdministration(thing, property) {\n  if (!thing) {\n    die(29);\n  }\n  if (property !== undefined) {\n    return getAdministration(getAtom(thing, property));\n  }\n  if (isAtom(thing) || isComputedValue(thing) || isReaction(thing)) {\n    return thing;\n  }\n  if (isObservableMap(thing) || isObservableSet(thing)) {\n    return thing;\n  }\n  if (thing[$mobx]) {\n    return thing[$mobx];\n  }\n  die(24, thing);\n}\nfunction getDebugName(thing, property) {\n  var named;\n  if (property !== undefined) {\n    named = getAtom(thing, property);\n  } else if (isAction(thing)) {\n    return thing.name;\n  } else if (isObservableObject(thing) || isObservableMap(thing) || isObservableSet(thing)) {\n    named = getAdministration(thing);\n  } else {\n    // valid for arrays as well\n    named = getAtom(thing);\n  }\n  return named.name_;\n}\n/**\n * Helper function for initializing observable structures, it applies:\n * 1. allowStateChanges so we don't violate enforceActions.\n * 2. untracked so we don't accidentaly subscribe to anything observable accessed during init in case the observable is created inside derivation.\n * 3. batch to avoid state version updates\n */\nfunction initObservable(cb) {\n  var derivation = untrackedStart();\n  var allowStateChanges = allowStateChangesStart(true);\n  startBatch();\n  try {\n    return cb();\n  } finally {\n    endBatch();\n    allowStateChangesEnd(allowStateChanges);\n    untrackedEnd(derivation);\n  }\n}\n\nvar toString = objectPrototype.toString;\nfunction deepEqual(a, b, depth) {\n  if (depth === void 0) {\n    depth = -1;\n  }\n  return eq(a, b, depth);\n}\n// Copied from https://github.com/jashkenas/underscore/blob/5c237a7c682fb68fd5378203f0bf22dce1624854/underscore.js#L1186-L1289\n// Internal recursive comparison function for `isEqual`.\nfunction eq(a, b, depth, aStack, bStack) {\n  // Identical objects are equal. `0 === -0`, but they aren't identical.\n  // See the [Harmony `egal` proposal](http://wiki.ecmascript.org/doku.php?id=harmony:egal).\n  if (a === b) {\n    return a !== 0 || 1 / a === 1 / b;\n  }\n  // `null` or `undefined` only equal to itself (strict comparison).\n  if (a == null || b == null) {\n    return false;\n  }\n  // `NaN`s are equivalent, but non-reflexive.\n  if (a !== a) {\n    return b !== b;\n  }\n  // Exhaust primitive checks\n  var type = typeof a;\n  if (type !== \"function\" && type !== \"object\" && typeof b != \"object\") {\n    return false;\n  }\n  // Compare `[[Class]]` names.\n  var className = toString.call(a);\n  if (className !== toString.call(b)) {\n    return false;\n  }\n  switch (className) {\n    // Strings, numbers, regular expressions, dates, and booleans are compared by value.\n    case \"[object RegExp]\":\n    // RegExps are coerced to strings for comparison (Note: '' + /a/i === '/a/i')\n    case \"[object String]\":\n      // Primitives and their corresponding object wrappers are equivalent; thus, `\"5\"` is\n      // equivalent to `new String(\"5\")`.\n      return \"\" + a === \"\" + b;\n    case \"[object Number]\":\n      // `NaN`s are equivalent, but non-reflexive.\n      // Object(NaN) is equivalent to NaN.\n      if (+a !== +a) {\n        return +b !== +b;\n      }\n      // An `egal` comparison is performed for other numeric values.\n      return +a === 0 ? 1 / +a === 1 / b : +a === +b;\n    case \"[object Date]\":\n    case \"[object Boolean]\":\n      // Coerce dates and booleans to numeric primitive values. Dates are compared by their\n      // millisecond representations. Note that invalid dates with millisecond representations\n      // of `NaN` are not equivalent.\n      return +a === +b;\n    case \"[object Symbol]\":\n      return typeof Symbol !== \"undefined\" && Symbol.valueOf.call(a) === Symbol.valueOf.call(b);\n    case \"[object Map]\":\n    case \"[object Set]\":\n      // Maps and Sets are unwrapped to arrays of entry-pairs, adding an incidental level.\n      // Hide this extra level by increasing the depth.\n      if (depth >= 0) {\n        depth++;\n      }\n      break;\n  }\n  // Unwrap any wrapped objects.\n  a = unwrap(a);\n  b = unwrap(b);\n  var areArrays = className === \"[object Array]\";\n  if (!areArrays) {\n    if (typeof a != \"object\" || typeof b != \"object\") {\n      return false;\n    }\n    // Objects with different constructors are not equivalent, but `Object`s or `Array`s\n    // from different frames are.\n    var aCtor = a.constructor,\n      bCtor = b.constructor;\n    if (aCtor !== bCtor && !(isFunction(aCtor) && aCtor instanceof aCtor && isFunction(bCtor) && bCtor instanceof bCtor) && \"constructor\" in a && \"constructor\" in b) {\n      return false;\n    }\n  }\n  if (depth === 0) {\n    return false;\n  } else if (depth < 0) {\n    depth = -1;\n  }\n  // Assume equality for cyclic structures. The algorithm for detecting cyclic\n  // structures is adapted from ES 5.1 section 15.12.3, abstract operation `JO`.\n  // Initializing stack of traversed objects.\n  // It's done here since we only need them for objects and arrays comparison.\n  aStack = aStack || [];\n  bStack = bStack || [];\n  var length = aStack.length;\n  while (length--) {\n    // Linear search. Performance is inversely proportional to the number of\n    // unique nested structures.\n    if (aStack[length] === a) {\n      return bStack[length] === b;\n    }\n  }\n  // Add the first object to the stack of traversed objects.\n  aStack.push(a);\n  bStack.push(b);\n  // Recursively compare objects and arrays.\n  if (areArrays) {\n    // Compare array lengths to determine if a deep comparison is necessary.\n    length = a.length;\n    if (length !== b.length) {\n      return false;\n    }\n    // Deep compare the contents, ignoring non-numeric properties.\n    while (length--) {\n      if (!eq(a[length], b[length], depth - 1, aStack, bStack)) {\n        return false;\n      }\n    }\n  } else {\n    // Deep compare objects.\n    var keys = Object.keys(a);\n    var key;\n    length = keys.length;\n    // Ensure that both objects contain the same number of properties before comparing deep equality.\n    if (Object.keys(b).length !== length) {\n      return false;\n    }\n    while (length--) {\n      // Deep compare each member\n      key = keys[length];\n      if (!(hasProp(b, key) && eq(a[key], b[key], depth - 1, aStack, bStack))) {\n        return false;\n      }\n    }\n  }\n  // Remove the first object from the stack of traversed objects.\n  aStack.pop();\n  bStack.pop();\n  return true;\n}\nfunction unwrap(a) {\n  if (isObservableArray(a)) {\n    return a.slice();\n  }\n  if (isES6Map(a) || isObservableMap(a)) {\n    return Array.from(a.entries());\n  }\n  if (isES6Set(a) || isObservableSet(a)) {\n    return Array.from(a.entries());\n  }\n  return a;\n}\n\nfunction makeIterable(iterator) {\n  iterator[Symbol.iterator] = getSelf;\n  return iterator;\n}\nfunction getSelf() {\n  return this;\n}\n\nfunction isAnnotation(thing) {\n  return (\n    // Can be function\n    thing instanceof Object && typeof thing.annotationType_ === \"string\" && isFunction(thing.make_) && isFunction(thing.extend_)\n  );\n}\n\n/**\n * (c) Michel Weststrate 2015 - 2020\n * MIT Licensed\n *\n * Welcome to the mobx sources! To get a global overview of how MobX internally works,\n * this is a good place to start:\n * https://medium.com/@mweststrate/becoming-fully-reactive-an-in-depth-explanation-of-mobservable-55995262a254#.xvbh6qd74\n *\n * Source folders:\n * ===============\n *\n * - api/     Most of the public static methods exposed by the module can be found here.\n * - core/    Implementation of the MobX algorithm; atoms, derivations, reactions, dependency trees, optimizations. Cool stuff can be found here.\n * - types/   All the magic that is need to have observable objects, arrays and values is in this folder. Including the modifiers like `asFlat`.\n * - utils/   Utility stuff.\n *\n */\n[\"Symbol\", \"Map\", \"Set\"].forEach(function (m) {\n  var g = getGlobal();\n  if (typeof g[m] === \"undefined\") {\n    die(\"MobX requires global '\" + m + \"' to be available or polyfilled\");\n  }\n});\nif (typeof __MOBX_DEVTOOLS_GLOBAL_HOOK__ === \"object\") {\n  // See: https://github.com/andykog/mobx-devtools/\n  __MOBX_DEVTOOLS_GLOBAL_HOOK__.injectMobx({\n    spy: spy,\n    extras: {\n      getDebugName: getDebugName\n    },\n    $mobx: $mobx\n  });\n}\n\nexports.$mobx = $mobx;\nexports.FlowCancellationError = FlowCancellationError;\nexports.ObservableMap = ObservableMap;\nexports.ObservableSet = ObservableSet;\nexports.Reaction = Reaction;\nexports._allowStateChanges = allowStateChanges;\nexports._allowStateChangesInsideComputed = runInAction;\nexports._allowStateReadsEnd = allowStateReadsEnd;\nexports._allowStateReadsStart = allowStateReadsStart;\nexports._autoAction = autoAction;\nexports._endAction = _endAction;\nexports._getAdministration = getAdministration;\nexports._getGlobalState = getGlobalState;\nexports._interceptReads = interceptReads;\nexports._isComputingDerivation = isComputingDerivation;\nexports._resetGlobalState = resetGlobalState;\nexports._startAction = _startAction;\nexports.action = action;\nexports.autorun = autorun;\nexports.comparer = comparer;\nexports.computed = computed;\nexports.configure = configure;\nexports.createAtom = createAtom;\nexports.defineProperty = apiDefineProperty;\nexports.entries = entries;\nexports.extendObservable = extendObservable;\nexports.flow = flow;\nexports.flowResult = flowResult;\nexports.get = get;\nexports.getAtom = getAtom;\nexports.getDebugName = getDebugName;\nexports.getDependencyTree = getDependencyTree;\nexports.getObserverTree = getObserverTree;\nexports.has = has;\nexports.intercept = intercept;\nexports.isAction = isAction;\nexports.isBoxedObservable = isObservableValue;\nexports.isComputed = isComputed;\nexports.isComputedProp = isComputedProp;\nexports.isFlow = isFlow;\nexports.isFlowCancellationError = isFlowCancellationError;\nexports.isObservable = isObservable;\nexports.isObservableArray = isObservableArray;\nexports.isObservableMap = isObservableMap;\nexports.isObservableObject = isObservableObject;\nexports.isObservableProp = isObservableProp;\nexports.isObservableSet = isObservableSet;\nexports.keys = keys;\nexports.makeAutoObservable = makeAutoObservable;\nexports.makeObservable = makeObservable;\nexports.observable = observable;\nexports.observe = observe;\nexports.onBecomeObserved = onBecomeObserved;\nexports.onBecomeUnobserved = onBecomeUnobserved;\nexports.onReactionError = onReactionError;\nexports.override = override;\nexports.ownKeys = apiOwnKeys;\nexports.reaction = reaction;\nexports.remove = remove;\nexports.runInAction = runInAction;\nexports.set = set;\nexports.spy = spy;\nexports.toJS = toJS;\nexports.trace = trace;\nexports.transaction = transaction;\nexports.untracked = untracked;\nexports.values = values;\nexports.when = when;\n//# sourceMappingURL=mobx.cjs.development.js.map\n"]}