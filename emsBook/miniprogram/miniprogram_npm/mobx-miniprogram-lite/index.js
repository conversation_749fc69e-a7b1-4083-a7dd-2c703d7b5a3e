module.exports = (function () {
  var __MODS__ = {};
  var __DEFINE__ = function (modId, func, req) { var m = { exports: {}, _tempexports: {} }; __MODS__[modId] = { status: 0, func: func, req: req, m: m }; };
  var __REQUIRE__ = function (modId, source) { if (!__MODS__[modId]) return require(source); if (!__MODS__[modId].status) { var m = __MODS__[modId].m; m._exports = m._tempexports; var desp = Object.getOwnPropertyDescriptor(m, "exports"); if (desp && desp.configurable) Object.defineProperty(m, "exports", { set: function (val) { if (typeof val === "object" && val !== m._exports) { m._exports.__proto__ = val.__proto__; Object.keys(val).forEach(function (k) { m._exports[k] = val[k]; }); } m._tempexports = val }, get: function () { return m._tempexports; } }); __MODS__[modId].status = 1; __MODS__[modId].func(__MODS__[modId].req, m, m.exports); } return __MODS__[modId].m.exports; };
  var __REQUIRE_WILDCARD__ = function (obj) { if (obj && obj.__esModule) { return obj; } else { var newObj = {}; if (obj != null) { for (var k in obj) { if (Object.prototype.hasOwnProperty.call(obj, k)) newObj[k] = obj[k]; } } newObj.default = obj; return newObj; } };
  var __REQUIRE_DEFAULT__ = function (obj) { return obj && obj.__esModule ? obj.default : obj; };
  __DEFINE__(1707137412199, function (require, module, exports) {
    var oo = Object.create; var Mt = Object.defineProperty, so = Object.defineProperties, ao = Object.getOwnPropertyDescriptor, uo = Object.getOwnPropertyDescriptors, co = Object.getOwnPropertyNames, wr = Object.getOwnPropertySymbols, fo = Object.getPrototypeOf, jr = Object.prototype.hasOwnProperty, lo = Object.prototype.propertyIsEnumerable; var qe = (t, r, e) => r in t ? Mt(t, r, { enumerable: !0, configurable: !0, writable: !0, value: e }) : t[r] = e, B = (t, r) => { for (var e in r || (r = {})) jr.call(r, e) && qe(t, e, r[e]); if (wr) for (var e of wr(r)) lo.call(r, e) && qe(t, e, r[e]); return t }, kt = (t, r) => so(t, uo(r)); var m = (t, r) => () => (r || t((r = { exports: {} }).exports, r), r.exports), ho = (t, r) => { for (var e in r) Mt(t, e, { get: r[e], enumerable: !0 }) }, ue = (t, r, e, n) => { if (r && typeof r == "object" || typeof r == "function") for (let i of co(r)) !jr.call(t, i) && i !== e && Mt(t, i, { get: () => r[i], enumerable: !(n = ao(r, i)) || n.enumerable }); return t }, Q = (t, r, e) => (ue(t, r, "default"), e && ue(e, r, "default")), Nt = (t, r, e) => (e = t != null ? oo(fo(t)) : {}, ue(r || !t || !t.__esModule ? Mt(e, "default", { value: t, enumerable: !0 }) : e, t)), vo = t => ue(Mt({}, "__esModule", { value: !0 }), t); var G = (t, r, e) => (qe(t, typeof r != "symbol" ? r + "" : r, e), e); var Sr = m((Vs, xr) => { xr.exports = function () { return typeof globalThis != "object" || !globalThis ? !1 : globalThis.Array === Array } }); var Ar = m((Ls, _r) => { var Er = function () { if (typeof self == "object" && self) return self; if (typeof window == "object" && window) return window; throw new Error("Unable to resolve global `this`") }; _r.exports = function () { if (this) return this; try { Object.defineProperty(Object.prototype, "__global__", { get: function () { return this }, configurable: !0 }) } catch (t) { return Er() } try { return __global__ || Er() } finally { delete Object.prototype.__global__ } }() }); var Vt = m((qs, Tr) => { Tr.exports = Sr()() ? globalThis : Ar() }); var Pr = m((Bs, Rr) => { var bo = Vt(), Be = { object: !0, symbol: !0 }; Rr.exports = function () { var t = bo.Symbol, r; if (typeof t != "function") return !1; r = t("test symbol"); try { String(r) } catch (e) { return !1 } return !(!Be[typeof t.iterator] || !Be[typeof t.toPrimitive] || !Be[typeof t.toStringTag]) } }); var Ge = m((Gs, Cr) => { var po = void 0; Cr.exports = function (t) { return t !== po && t !== null } }); var Dr = m((Fs, Ir) => { var go = Ge(), yo = { object: !0, function: !0, undefined: !0 }; Ir.exports = function (t) { return go(t) ? hasOwnProperty.call(yo, typeof t) : !1 } }); var kr = m((Ks, Mr) => { var mo = Dr(); Mr.exports = function (t) { if (!mo(t)) return !1; try { return t.constructor ? t.constructor.prototype === t : !1 } catch (r) { return !1 } } }); var Vr = m((Ws, Nr) => { var Oo = kr(); Nr.exports = function (t) { if (typeof t != "function" || !hasOwnProperty.call(t, "length")) return !1; try { if (typeof t.length != "number" || typeof t.call != "function" || typeof t.apply != "function") return !1 } catch (r) { return !1 } return !Oo(t) } }); var qr = m((Us, Lr) => { var wo = Vr(), jo = /^\s*class[\s{/}]/, xo = Function.prototype.toString; Lr.exports = function (t) { return !(!wo(t) || jo.test(xo.call(t))) } }); var Gr = m((zs, Br) => { Br.exports = function () { var t = Object.assign, r; return typeof t != "function" ? !1 : (r = { foo: "raz" }, t(r, { bar: "dwa" }, { trzy: "trzy" }), r.foo + r.bar + r.trzy === "razdwatrzy") } }); var Kr = m((Hs, Fr) => { Fr.exports = function () { try { return Object.keys("primitive"), !0 } catch (t) { return !1 } } }); var Ur = m((Xs, Wr) => { Wr.exports = function () { } }); var ce = m((Js, zr) => { var So = Ur()(); zr.exports = function (t) { return t !== So && t !== null } }); var Xr = m((Ys, Hr) => { var Eo = ce(), _o = Object.keys; Hr.exports = function (t) { return _o(Eo(t) ? Object(t) : t) } }); var Yr = m(($s, Jr) => { Jr.exports = Kr()() ? Object.keys : Xr() }); var Zr = m((Zs, $r) => { var Ao = ce(); $r.exports = function (t) { if (!Ao(t)) throw new TypeError("Cannot use null or undefined"); return t } }); var tn = m((Qs, Qr) => { var To = Yr(), Ro = Zr(), Po = Math.max; Qr.exports = function (t, r) { var e, n, i = Po(arguments.length, 2), o; for (t = Object(Ro(t)), o = function (s) { try { t[s] = r[s] } catch (a) { e || (e = a) } }, n = 1; n < i; ++n)r = arguments[n], To(r).forEach(o); if (e !== void 0) throw e; return t } }); var rn = m((ta, en) => { en.exports = Gr()() ? Object.assign : tn() }); var on = m((ea, nn) => { var Co = ce(), Io = Array.prototype.forEach, Do = Object.create, Mo = function (t, r) { var e; for (e in t) r[e] = t[e] }; nn.exports = function (t) { var r = Do(null); return Io.call(arguments, function (e) { Co(e) && Mo(Object(e), r) }), r } }); var an = m((ra, sn) => { var Fe = "razdwatrzy"; sn.exports = function () { return typeof Fe.contains != "function" ? !1 : Fe.contains("dwa") === !0 && Fe.contains("foo") === !1 } }); var cn = m((na, un) => { var ko = String.prototype.indexOf; un.exports = function (t) { return ko.call(this, t, arguments[1]) > -1 } }); var ln = m((ia, fn) => { fn.exports = an()() ? String.prototype.contains : cn() }); var qt = m((oa, bn) => { var fe = Ge(), hn = qr(), vn = rn(), dn = on(), Lt = ln(), No = bn.exports = function (t, r) { var e, n, i, o, s; return arguments.length < 2 || typeof t != "string" ? (o = r, r = t, t = null) : o = arguments[2], fe(t) ? (e = Lt.call(t, "c"), n = Lt.call(t, "e"), i = Lt.call(t, "w")) : (e = i = !0, n = !1), s = { value: r, configurable: e, enumerable: n, writable: i }, o ? vn(dn(o), s) : s }; No.gs = function (t, r, e) { var n, i, o, s; return typeof t != "string" ? (o = e, e = r, r = t, t = null) : o = arguments[3], fe(r) ? hn(r) ? fe(e) ? hn(e) || (o = e, e = void 0) : e = void 0 : (o = r, r = e = void 0) : r = void 0, fe(t) ? (n = Lt.call(t, "c"), i = Lt.call(t, "e")) : (n = !0, i = !1), s = { get: r, set: e, configurable: n, enumerable: i }, o ? vn(dn(o), s) : s } }); var gn = m((sa, pn) => { pn.exports = function (t) { return t ? typeof t == "symbol" ? !0 : !t.constructor || t.constructor.name !== "Symbol" ? !1 : t[t.constructor.toStringTag] === "Symbol" : !1 } }); var Ke = m((aa, yn) => { var Vo = gn(); yn.exports = function (t) { if (!Vo(t)) throw new TypeError(t + " is not a symbol"); return t } }); var xn = m((ua, jn) => { var mn = qt(), Lo = Object.create, On = Object.defineProperty, qo = Object.prototype, wn = Lo(null); jn.exports = function (t) { for (var r = 0, e, n; wn[t + (r || "")];)++r; return t += r || "", wn[t] = !0, e = "@@" + t, On(qo, e, mn.gs(null, function (i) { n || (n = !0, On(this, e, mn(i)), n = !1) })), e } }); var En = m((ca, Sn) => { var F = qt(), S = Vt().Symbol; Sn.exports = function (t) { return Object.defineProperties(t, { hasInstance: F("", S && S.hasInstance || t("hasInstance")), isConcatSpreadable: F("", S && S.isConcatSpreadable || t("isConcatSpreadable")), iterator: F("", S && S.iterator || t("iterator")), match: F("", S && S.match || t("match")), replace: F("", S && S.replace || t("replace")), search: F("", S && S.search || t("search")), species: F("", S && S.species || t("species")), split: F("", S && S.split || t("split")), toPrimitive: F("", S && S.toPrimitive || t("toPrimitive")), toStringTag: F("", S && S.toStringTag || t("toStringTag")), unscopables: F("", S && S.unscopables || t("unscopables")) }) } }); var Tn = m((fa, An) => { var _n = qt(), Bo = Ke(), Bt = Object.create(null); An.exports = function (t) { return Object.defineProperties(t, { for: _n(function (r) { return Bt[r] ? Bt[r] : Bt[r] = t(String(r)) }), keyFor: _n(function (r) { var e; Bo(r); for (e in Bt) if (Bt[e] === r) return e }) }) } }); var Cn = m((la, Pn) => { var z = qt(), We = Ke(), le = Vt().Symbol, Go = xn(), Fo = En(), Ko = Tn(), Wo = Object.create, Ue = Object.defineProperties, he = Object.defineProperty, I, wt, Rn; if (typeof le == "function") try { String(le()), Rn = !0 } catch (t) { } else le = null; wt = function (r) { if (this instanceof wt) throw new TypeError("Symbol is not a constructor"); return I(r) }; Pn.exports = I = function t(r) { var e; if (this instanceof t) throw new TypeError("Symbol is not a constructor"); return Rn ? le(r) : (e = Wo(wt.prototype), r = r === void 0 ? "" : String(r), Ue(e, { __description__: z("", r), __name__: z("", Go(r)) })) }; Fo(I); Ko(I); Ue(wt.prototype, { constructor: z(I), toString: z("", function () { return this.__name__ }) }); Ue(I.prototype, { toString: z(function () { return "Symbol (" + We(this).__description__ + ")" }), valueOf: z(function () { return We(this) }) }); he(I.prototype, I.toPrimitive, z("", function () { var t = We(this); return typeof t == "symbol" ? t : t.toString() })); he(I.prototype, I.toStringTag, z("c", "Symbol")); he(wt.prototype, I.toStringTag, z("c", I.prototype[I.toStringTag])); he(wt.prototype, I.toPrimitive, z("c", I.prototype[I.toPrimitive])) }); var Dn = m((ha, In) => { In.exports = Pr()() ? Vt().Symbol : Cn() }); var zi = m(l => {
      function p(t) { for (var r = arguments.length, e = new Array(r > 1 ? r - 1 : 0), n = 1; n < r; n++)e[n - 1] = arguments[n]; throw new Error(typeof t == "number" ? "[MobX] minified error nr: " + t + (e.length ? " " + e.map(String).join(",") : "") + ". Find the full error at: https://github.com/mobxjs/mobx/blob/main/packages/mobx/src/errors.js" : "[MobX] " + t) } function Ye() { return typeof globalThis != "undefined" ? globalThis : typeof window != "undefined" ? window : typeof global != "undefined" ? global : typeof self != "undefined" ? self : us } function Zn() { cs || p("Proxy not available") } function Qn(t) { var r = !1; return function () { if (!r) return r = !0, t.apply(this, arguments) } } function V(t) { return typeof t == "function" } function gt(t) { switch (typeof t) { case "string": case "symbol": case "number": return !0 }return !1 } function Re(t) { return t !== null && typeof t == "object" } function it(t) { if (!Re(t)) return !1; var r = Object.getPrototypeOf(t); if (r == null) return !0; var e = Object.hasOwnProperty.call(r, "constructor") && r.constructor; return typeof e == "function" && e.toString() === fs } function ti(t) { var r = t == null ? void 0 : t.constructor; return !!r && (r.name === "GeneratorFunction" || r.displayName === "GeneratorFunction") } function Wt(t, r, e) { J(t, r, { enumerable: !1, writable: !0, configurable: !0, value: e }) } function ei(t, r, e) { J(t, r, { enumerable: !1, writable: !1, configurable: !0, value: e }) } function ct(t, r) { var e = "isMobX" + t; return r.prototype[e] = !0, function (n) { return Re(n) && n[e] === !0 } } function At(t) { return t instanceof Map } function Zt(t) { return t instanceof Set } function ri(t) { return t === null ? null : typeof t == "object" ? "" + t : t } function rt(t, r) { return oe.hasOwnProperty.call(t, r) } function Mn(t, r) { for (var e = 0; e < r.length; e++) { var n = r[e]; n.enumerable = n.enumerable || !1, n.configurable = !0, "value" in n && (n.writable = !0), Object.defineProperty(t, typeof (i = function (o) { if (typeof o != "object" || o === null) return o; var s = o[Symbol.toPrimitive]; if (s !== void 0) { var a = s.call(o, "string"); if (typeof a != "object") return a; throw new TypeError("@@toPrimitive must return a primitive value.") } return String(o) }(n.key)) == "symbol" ? i : String(i), n) } var i } function nr(t, r, e) { return r && Mn(t.prototype, r), e && Mn(t, e), Object.defineProperty(t, "prototype", { writable: !1 }), t } function Pt() { return (Pt = Object.assign ? Object.assign.bind() : function (t) { for (var r = 1; r < arguments.length; r++) { var e = arguments[r]; for (var n in e) Object.prototype.hasOwnProperty.call(e, n) && (t[n] = e[n]) } return t }).apply(this, arguments) } function ni(t, r) { var e, n; t.prototype = Object.create(r.prototype), t.prototype.constructor = t, e = t, n = r, (Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (i, o) { return i.__proto__ = o, i })(e, n) } function ze(t) { if (t === void 0) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return t } function kn(t, r) { (r == null || r > t.length) && (r = t.length); for (var e = 0, n = new Array(r); e < r; e++)n[e] = t[e]; return n } function xt(t, r) {
        var e = typeof Symbol != "undefined" && t[Symbol.iterator] || t["@@iterator"]; if (e) return (e = e.call(t)).next.bind(e); if (Array.isArray(t) || (e = function (i) { if (i) { if (typeof i == "string") return kn(i, void 0); var o = Object.prototype.toString.call(i).slice(8, -1); return o === "Object" && i.constructor && (o = i.constructor.name), o === "Map" || o === "Set" ? Array.from(i) : o === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o) ? kn(i, void 0) : void 0 } }(t)) || r && t && typeof t.length == "number") { e && (t = e); var n = 0; return function () { return n >= t.length ? { done: !0 } : { done: !1, value: t[n++] } } } throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)
      } function K(t) { return Object.assign(function (r, e) { Qt(r, e, t) }, t) } function Qt(t, r, e) { rt(t, X) || Wt(t, X, Pt({}, t[X])), function (n) { return n.t === "override" }(e) || (t[X][r] = e) } function ir(t, r, e) { r === void 0 && (r = St), e === void 0 && (e = St); var n = new se(t); return r !== St && wi(n, r), e !== St && ur(n, e), n } function Ut(t, r, e) { return we(t) ? t : Array.isArray(t) ? T.array(t, { name: e }) : it(t) ? T.object(t, void 0, { name: e }) : At(t) ? T.map(t, { name: e }) : Zt(t) ? T.set(t, { name: e }) : typeof t != "function" || De(t) || zt(t) ? t : ti(t) ? Rt(t) : Tt(e, t) } function Pe(t) { return t } function te(t, r) { return { t, i: r, u: Uo, o: zo } } function Uo(t, r, e, n) { var i; if ((i = this.i) != null && i.bound) return this.o(t, r, e, !1) === null ? 0 : 1; if (n === t.s) return this.o(t, r, e, !1) === null ? 0 : 2; if (De(e.value)) return 1; var o = ii(t, this, r, e, !1); return J(n, r, o), 2 } function zo(t, r, e, n) { var i = ii(t, this, r, e); return t.h(r, i, n) } function ii(t, r, e, n, i) { var o, s, a, u, f, h, d; i === void 0 && (i = c.safeDescriptors); var g, y = n.value; return (o = r.i) != null && o.bound && (y = y.bind((g = t.v) != null ? g : t.s)), { value: yt((s = (a = r.i) == null ? void 0 : a.name) != null ? s : e.toString(), y, (u = (f = r.i) == null ? void 0 : f.autoAction) != null && u, (h = r.i) != null && h.bound ? (d = t.v) != null ? d : t.s : void 0), configurable: !i || t.l, enumerable: !1, writable: !i } } function oi(t, r) { return { t, i: r, u: Ho, o: Xo } } function Ho(t, r, e, n) { var i; if (n === t.s) return this.o(t, r, e, !1) === null ? 0 : 2; if ((i = this.i) != null && i.bound && (!rt(t.s, r) || !zt(t.s[r])) && this.o(t, r, e, !1) === null) return 0; if (zt(e.value)) return 1; var o = si(t, 0, 0, e, !1, !1); return J(n, r, o), 2 } function Xo(t, r, e, n) { var i, o = si(t, 0, 0, e, (i = this.i) == null ? void 0 : i.bound); return t.h(r, o, n) } function si(t, r, e, n, i, o) { o === void 0 && (o = c.safeDescriptors); var s, a = n.value; return zt(a) || (a = Rt(a)), i && ((a = a.bind((s = t.v) != null ? s : t.s)).isMobXFlow = !0), { value: a, configurable: !o || t.l, enumerable: !1, writable: !o } } function or(t, r) { return { t, i: r, u: Jo, o: Yo } } function Jo(t, r, e) { return this.o(t, r, e, !1) === null ? 0 : 1 } function Yo(t, r, e, n) { return t.p(r, Pt({}, this.i, { get: e.get, set: e.set }), n) } function Ce(t, r) { return { t, i: r, u: $o, o: Zo } } function $o(t, r, e) { return this.o(t, r, e, !1) === null ? 0 : 1 } function Zo(t, r, e, n) { var i, o; return t.m(r, e.value, (i = (o = this.i) == null ? void 0 : o.enhancer) != null ? i : Ut, n) } function ai(t) { return { t: "true", i: t, u: Qo, o: ts } } function Qo(t, r, e, n) { var i, o, s, a; if (e.get) return Jt.u(t, r, e, n); if (e.set) { var u = yt(r.toString(), e.set); return n === t.s ? t.h(r, { configurable: !c.safeDescriptors || t.l, set: u }) === null ? 0 : 2 : (J(n, r, { configurable: !0, set: u }), 2) } if (n !== t.s && typeof e.value == "function") return ti(e.value) ? ((a = this.i) != null && a.autoBind ? Rt.bound : Rt).u(t, r, e, n) : ((s = this.i) != null && s.autoBind ? Tt.bound : Tt).u(t, r, e, n); var f, h = ((i = this.i) == null ? void 0 : i.deep) === !1 ? T.ref : T; return typeof e.value == "function" && (o = this.i) != null && o.autoBind && (e.value = e.value.bind((f = t.v) != null ? f : t.s)), h.u(t, r, e, n) } function ts(t, r, e, n) { var i, o, s; return e.get ? Jt.o(t, r, e, n) : e.set ? t.h(r, { configurable: !c.safeDescriptors || t.l, set: yt(r.toString(), e.set) }, n) : (typeof e.value == "function" && (i = this.i) != null && i.autoBind && (e.value = e.value.bind((s = t.v) != null ? s : t.s)), (((o = this.i) == null ? void 0 : o.deep) === !1 ? T.ref : T).o(t, r, e, n)) } function ve(t) { return t || Ci } function de(t) { return t.deep === !0 ? Ut : t.deep === !1 ? Pe : (r = t.defaultDecorator) && (e = (n = r.i) == null ? void 0 : n.enhancer) != null ? e : Ut; var r, e, n } function ui(t, r, e) { if (!gt(r)) return we(t) ? t : it(t) ? T.object(t, r, e) : Array.isArray(t) ? T.array(t, r) : At(t) ? T.map(t, r) : Zt(t) ? T.set(t, r) : typeof t == "object" && t !== null ? t : T.box(t, r); Qt(t, r, Ii) } function yt(t, r, e, n) { function i() { return ci(0, e, r, n || this, arguments) } return e === void 0 && (e = !1), i.isMobxAction = !0, ms && (Xn.value = t, J(i, "name", Xn)), i } function ci(t, r, e, n, i) { var o = fi(0, r); try { return e.apply(n, i) } catch (s) { throw o.j = s, s } finally { li(o) } } function fi(t, r) { var e = c.trackingDerivation, n = !r || !e; D(); var i = c.allowStateChanges; n && (Ct(), i = ee(!0)); var o = { O: n, A: e, g: i, _: Ie(!0), S: !1, M: 0, N: ys++, V: Se }; return Se = o.N, o } function li(t) { Se !== t.N && p(30), Se = t.V, t.j !== void 0 && (c.suppressReactionErrors = !0), re(t.g), Et(t._), M(), t.O && ut(t.A), c.suppressReactionErrors = !1 } function me(t, r) { var e = ee(t); try { return r() } finally { re(e) } } function ee(t) { var r = c.allowStateChanges; return c.allowStateChanges = t, r } function re(t) { c.allowStateChanges = t } function ge(t) { return t instanceof _e } function $e(t) { switch (t.R) { case O.T: return !1; case O.k: case O.K: return !0; case O.L: for (var r = Ie(!0), e = Ct(), n = t.C, i = n.length, o = 0; o < i; o++) { var s = n[o]; if (mt(s)) { if (c.disableErrorBoundaries) s.get(); else try { s.get() } catch (a) { return ut(e), Et(r), !0 } if (t.R === O.K) return ut(e), Et(r), !0 } } return vi(t), ut(e), Et(r), !1 } } function hi(t, r, e) { var n = Ie(!0); vi(t), t.I = new Array(t.C.length + 100), t.P = 0, t.D = ++c.runId; var i, o = c.trackingDerivation; if (c.trackingDerivation = t, c.inBatch++, c.disableErrorBoundaries === !0) i = r.call(e); else try { i = r.call(e) } catch (s) { i = new _e(s) } return c.inBatch--, c.trackingDerivation = o, function (s) { for (var a = s.C, u = s.C = s.I, f = O.T, h = 0, d = s.P, g = 0; g < d; g++) { var y = u[g]; y.B === 0 && (y.B = 1, h !== g && (u[h] = y), h++), y.R > f && (f = y.R) } for (u.length = h, s.I = null, d = a.length; d--;) { var b = a[d]; b.B === 0 && di(b, s), b.B = 0 } for (; h--;) { var w = u[h]; w.B === 1 && (w.B = 0, es(w, s)) } f !== O.T && (s.R = f, s.q()) }(t), Et(n), i } function Ze(t) { var r = t.C; t.C = []; for (var e = r.length; e--;)di(r[e], t); t.R = O.k } function sr(t) { var r = Ct(); try { return t() } finally { ut(r) } } function Ct() { var t = c.trackingDerivation; return c.trackingDerivation = null, t } function ut(t) { c.trackingDerivation = t } function Ie(t) { var r = c.allowStateReads; return c.allowStateReads = t, r } function Et(t) { c.allowStateReads = t } function vi(t) { if (t.R !== O.T) { t.R = O.T; for (var r = t.C, e = r.length; e--;)r[e].G = O.T } } function es(t, r) { t.W.add(r), t.G > r.R && (t.G = r.R) } function di(t, r) { t.W.delete(r), t.W.size === 0 && bi(t) } function bi(t) { t.H === !1 && (t.H = !0, c.pendingUnobservations.push(t)) } function D() { c.inBatch++ } function M() { if (--c.inBatch == 0) { yi(); for (var t = c.pendingUnobservations, r = 0; r < t.length; r++) { var e = t[r]; e.H = !1, e.W.size === 0 && (e.X && (e.X = !1, e.onBUO()), e instanceof Yt && e.U()) } c.pendingUnobservations = [] } } function pi(t) { var r = c.trackingDerivation; return r !== null ? (r.D !== t.F && (t.F = r.D, r.I[r.P++] = t, !t.X && c.trackingContext && (t.X = !0, t.onBO())), t.X) : (t.W.size === 0 && c.inBatch > 0 && bi(t), !1) } function gi(t) { t.G !== O.K && (t.G = O.K, t.W.forEach(function (r) { r.R === O.T && r.q(), r.R = O.K })) } function yi() { c.inBatch > 0 || c.isRunningReactions || rr(rs) } function rs() { c.isRunningReactions = !0; for (var t = c.pendingReactions, r = 0; t.length > 0;) { ++r == 100 && (console.error("[mobx] cycle in reaction: " + t[0]), t.splice(0)); for (var e = t.splice(0), n = 0, i = e.length; n < i; n++)e[n].$() } c.isRunningReactions = !1 } function Nn() { return console.warn("[mobx.spy] Is a no-op in production builds"), function () { } } function mi(t) { return function (r, e) { return V(r) ? yt(r.name || "<unnamed action>", r, t) : V(e) ? yt(r, e, t) : gt(e) ? Qt(r, e, t ? qi : Li) : gt(r) ? K(te(t ? "autoAction" : "action", { name: r, autoAction: t })) : void 0 } } function Vn(t) { return ci(0, !1, t, this, void 0) } function De(t) { return V(t) && t.isMobxAction === !0 } function ar(t, r) { function e() { t(u) } var n, i, o, s, a; r === void 0 && (r = cr); var u, f = (n = (i = r) == null ? void 0 : i.name) != null ? n : "Autorun"; if (r.scheduler || r.delay) { var h = Oi(r), d = !1; u = new $t(f, function () { d || (d = !0, h(function () { d = !1, u.J || u.track(e) })) }, r.onError, r.requiresObservable) } else u = new $t(f, function () { this.track(e) }, r.onError, r.requiresObservable); return (o = r) != null && (s = o.signal) != null && s.aborted || u.Y(), u.Z((a = r) == null ? void 0 : a.signal) } function Oi(t) { return t.scheduler ? t.scheduler : t.delay ? function (r) { return setTimeout(r, t.delay) } : xs } function wi(t, r, e) { return ji("onBO", t, r, e) } function ur(t, r, e) { return ji("onBUO", t, r, e) } function ji(t, r, e, n) { var i = typeof n == "function" ? nt(r, e) : nt(r), o = V(n) ? n : e, s = t + "L"; return i[s] ? i[s].add(o) : i[s] = new Set([o]), function () { var a = i[s]; a && (a.delete(o), a.size === 0 && delete i[s]) } } function Qe(t, r, e, n) { var i = ls(r), o = Ht(t, n)[v]; D(); try { _t(i).forEach(function (s) { o.o(s, i[s], !e || !(s in e) || e[s]) }) } finally { M() } return t } function xi(t) { var r, e = { name: t.tt }; return t.C && t.C.length > 0 && (e.dependencies = (r = t.C, Array.from(new Set(r))).map(xi)), e } function Si(t) { var r = { name: t.tt }; return function (e) { return e.W && e.W.size > 0 }(t) && (r.observers = Array.from(function (e) { return e.W }(t)).map(Si)), r } function Oe() { this.message = "FLOW_CANCELLED" } function Ln(t) { V(t.cancel) && t.cancel() } function zt(t) { return (t == null ? void 0 : t.isMobXFlow) === !0 } function qn(t, r) { if (r === void 0) return mt(t); if (P(t) === !1 || !t[v].nt.has(r)) return !1; var e = nt(t, r); return mt(e) } function Ei(t, r) { return !!t && (r !== void 0 ? !!P(t) && t[v].nt.has(r) : P(t) || !!t[v] || fr(t) || Ae(t) || mt(t)) } function we(t) { return Ei(t) } function Gt(t) { return P(t) ? t[v].it() : C(t) || k(t) ? Array.from(t.keys()) : N(t) ? t.map(function (r, e) { return e }) : void p(5) } function Bn(t, r) { return P(t) ? t[v].rt(r) : C(t) || k(t) ? t.has(r) : N(t) ? r >= 0 && r < t.length : void p(10) } function Gn(t) { if (P(t)) return t[v].et(); p(38) } function be(t, r, e) { return t.set(r, e), e } function H(t, r) { r === void 0 && (r = void 0), D(); try { return t.apply(r) } finally { M() } } function _i(t, r, e) { var n; if (typeof e.timeout == "number") { var i = new Error("WHEN_TIMEOUT"); n = setTimeout(function () { if (!s[v].J) { if (s(), !e.onError) throw i; e.onError(i) } }, e.timeout) } e.name = "When"; var o = yt("When-effect", r), s = ar(function (a) { me(!1, t) && (a.dispose(), n && clearTimeout(n), o()) }, e); return s } function ns(t, r) { var e, n, i; if (r != null && (e = r.signal) != null && e.aborted) return Object.assign(Promise.reject(new Error("WHEN_ABORTED")), { cancel: function () { return null } }); var o = new Promise(function (s, a) { var u, f = _i(t, s, Pt({}, r, { onError: a })); n = function () { f(), a(new Error("WHEN_CANCELLED")) }, i = function () { f(), a(new Error("WHEN_ABORTED")) }, r == null || (u = r.signal) == null || u.addEventListener == null || u.addEventListener("abort", i) }).finally(function () { var s; return r == null || (s = r.signal) == null || s.removeEventListener == null ? void 0 : s.removeEventListener("abort", i) }); return o.cancel = n, o } function jt(t) { return t[v] } function L(t) { return t.ut !== void 0 && t.ut.length > 0 } function ne(t, r) { var e = t.ut || (t.ut = []); return e.push(r), Qn(function () { var n = e.indexOf(r); n !== -1 && e.splice(n, 1) }) } function q(t, r) { var e = Ct(); try { for (var n = [].concat(t.ut || []), i = 0, o = n.length; i < o && ((r = n[i](r)) && !r.type && p(14), r); i++); return r } finally { ut(e) } } function W(t) { return t.ot !== void 0 && t.ot.length > 0 } function ie(t, r) { var e = t.ot || (t.ot = []); return e.push(r), Qn(function () { var n = e.indexOf(r); n !== -1 && e.splice(n, 1) }) } function U(t, r) { var e = Ct(), n = t.ot; if (n) { for (var i = 0, o = (n = n.slice()).length; i < o; i++)n[i](r); ut(e) } } function is(t, r, e, n) { e === void 0 && (e = "ObservableArray"), n === void 0 && (n = !1), Zn(); var i = new lr(e, r, n, !1); ei(i.nt, v, i); var o = new Proxy(i.nt, As); if (i.v = o, t && t.length) { var s = ee(!0); i.st(0, 0, t), re(s) } return o } function A(t, r) { typeof Array.prototype[t] == "function" && (Te[t] = r(t)) } function tt(t) { return function () { var r = this[v]; r.ft.reportObserved(); var e = r.ct(r.nt); return e[t].apply(e, arguments) } } function ot(t) { return function (r, e) { var n = this, i = this[v]; return i.ft.reportObserved(), i.ct(i.nt)[t](function (o, s) { return r.call(e, o, s, n) }) } } function Fn(t) { return function () { var r = this, e = this[v]; e.ft.reportObserved(); var n = e.ct(e.nt), i = arguments[0]; return arguments[0] = function (o, s, a) { return i(o, s, a, r) }, n[t].apply(n, arguments) } } function N(t) { return Re(t) && Ts(t[v]) } function Ht(t, r) { var e; if (rt(t, v)) return t; var n = (e = r == null ? void 0 : r.name) != null ? e : "ObservableObject", i = new Wi(t, new Map, String(n), function (o) { var s; return o ? (s = o.defaultDecorator) != null ? s : ai(o) : void 0 }(r)); return Wt(t, v, i), t } function Kn(t) { return Yn[t] || (Yn[t] = { get: function () { return this[v].at(t) }, set: function (r) { return this[v].ht(t, r) } }) } function P(t) { return !!Re(t) && Cs(t[v]) } function Wn(t, r, e) { var n; (n = t.s[X]) == null || delete n[e] } function Ai(t) { return { enumerable: !1, configurable: !0, get: function () { return this[v].vt(t) }, set: function (r) { this[v].lt(t, r) } } } function os(t) { J(dr.prototype, "" + t, Ai(t)) } function Ti(t) { if (t > Je) { for (var r = Je; r < t + 100; r++)os(r); Je = t } } function ss(t, r, e) { return new dr(t, r, e) } function nt(t, r) { if (typeof t == "object" && t !== null) { if (N(t)) return r !== void 0 && p(23), t[v].ft; if (k(t)) return t.ft; if (C(t)) { if (r === void 0) return t.dt; var e = t.bt.get(r) || t.pt.get(r); return e || p(25, r, je(t)), e } if (P(t)) { if (!r) return p(26); var n = t[v].nt.get(r); return n || p(27, r, je(t)), n } if (fr(t) || mt(t) || Ae(t)) return t } else if (V(t) && Ae(t[v])) return t[v]; p(28) } function et(t, r) { return t || p(29), r !== void 0 ? et(nt(t, r)) : fr(t) || mt(t) || Ae(t) || C(t) || k(t) ? t : t[v] ? t[v] : void p(24, t) } function je(t, r) { var e; if (r !== void 0) e = nt(t, r); else { if (De(t)) return t.name; e = P(t) || C(t) || k(t) ? et(t) : nt(t) } return e.tt } function tr(t, r, e) { return e === void 0 && (e = -1), function n(i, o, s, a, u) { if (i === o) return i !== 0 || 1 / i == 1 / o; if (i == null || o == null) return !1; if (i != i) return o != o; var f = typeof i; if (f !== "function" && f !== "object" && typeof o != "object") return !1; var h = $n.call(i); if (h !== $n.call(o)) return !1; switch (h) { case "[object RegExp]": case "[object String]": return "" + i == "" + o; case "[object Number]": return +i != +i ? +o != +o : +i == 0 ? 1 / +i == 1 / o : +i == +o; case "[object Date]": case "[object Boolean]": return +i == +o; case "[object Symbol]": return typeof Symbol != "undefined" && Symbol.valueOf.call(i) === Symbol.valueOf.call(o); case "[object Map]": case "[object Set]": s >= 0 && s++ }i = Un(i), o = Un(o); var d = h === "[object Array]"; if (!d) { if (typeof i != "object" || typeof o != "object") return !1; var g = i.constructor, y = o.constructor; if (g !== y && !(V(g) && g instanceof g && V(y) && y instanceof y) && "constructor" in i && "constructor" in o) return !1 } if (s === 0) return !1; s < 0 && (s = -1), u = u || []; for (var b = (a = a || []).length; b--;)if (a[b] === i) return u[b] === o; if (a.push(i), u.push(o), d) { if ((b = i.length) !== o.length) return !1; for (; b--;)if (!n(i[b], o[b], s - 1, a, u)) return !1 } else { var w, j = Object.keys(i); if (b = j.length, Object.keys(o).length !== b) return !1; for (; b--;)if (!rt(o, w = j[b]) || !n(i[w], o[w], s - 1, a, u)) return !1 } return a.pop(), u.pop(), !0 }(t, r, e) } function Un(t) { return N(t) ? t.slice() : At(t) || C(t) || Zt(t) || k(t) ? Array.from(t.entries()) : t } function Xt(t) { return t[Symbol.iterator] = as, t } function as() { return this } Object.defineProperty(l, "__esModule", { value: !0 }); var us = {}, Ri = Object.assign, xe = Object.getOwnPropertyDescriptor, J = Object.defineProperty, oe = Object.prototype, er = []; Object.freeze(er); var cr = {}; Object.freeze(cr); var cs = typeof Proxy != "undefined", fs = Object.toString(), St = function () { }, Pi = Object.getOwnPropertySymbols !== void 0, _t = typeof Reflect != "undefined" && Reflect.ownKeys ? Reflect.ownKeys : Pi ? function (t) { return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t)) } : Object.getOwnPropertyNames, ls = Object.getOwnPropertyDescriptors || function (t) { var r = {}; return _t(t).forEach(function (e) { r[e] = xe(t, e) }), r }, X = Symbol("mobx-stored-annotations"), v = Symbol("mobx administration"), se = function () { function t(e) { e === void 0 && (e = "Atom"), this.tt = void 0, this.H = !1, this.X = !1, this.W = new Set, this.B = 0, this.F = 0, this.G = O.k, this.onBOL = void 0, this.onBUOL = void 0, this.tt = e } var r = t.prototype; return r.onBO = function () { this.onBOL && this.onBOL.forEach(function (e) { return e() }) }, r.onBUO = function () { this.onBUOL && this.onBUOL.forEach(function (e) { return e() }) }, r.reportObserved = function () { return pi(this) }, r.reportChanged = function () { D(), gi(this), c.stateVersion = c.stateVersion < Number.MAX_SAFE_INTEGER ? c.stateVersion + 1 : Number.MIN_SAFE_INTEGER, M() }, r.toString = function () { return this.tt }, t }(), fr = ct("Atom", se), bt = { identity: function (t, r) { return t === r }, structural: function (t, r) { return tr(t, r) }, default: function (t, r) { return Object.is ? Object.is(t, r) : t === r ? t !== 0 || 1 / t == 1 / r : t != t && r != r }, shallow: function (t, r) { return tr(t, r, 1) } }, hs = K({ t: "override", u: function () { return 0 }, o: function () { p("'" + this.t + "' can only be used with 'makeObservable'") } }), vs = ai(), Ci = { deep: !0, name: void 0, defaultDecorator: void 0, proxy: !0 }; Object.freeze(Ci); var Ii = Ce("observable"), ds = Ce("observable.ref", { enhancer: Pe }), bs = Ce("observable.shallow", { enhancer: function (t, r, e) { return t == null || P(t) || N(t) || C(t) || k(t) ? t : Array.isArray(t) ? T.array(t, { name: e, deep: !1 }) : it(t) ? T.object(t, void 0, { name: e, deep: !1 }) : At(t) ? T.map(t, { name: e, deep: !1 }) : Zt(t) ? T.set(t, { name: e, deep: !1 }) : void 0 } }), ps = Ce("observable.struct", { enhancer: function (t, r) { return tr(t, r) ? r : t } }), Di = K(Ii); Ri(ui, Di); var zn, Hn, T = Ri(ui, { box: function (t, r) { var e = ve(r); return new pt(t, de(e), e.name, !0, e.equals) }, array: function (t, r) { var e = ve(r); return (c.useProxies === !1 || e.proxy === !1 ? ss : is)(t, de(e), e.name) }, map: function (t, r) { var e = ve(r); return new hr(t, de(e), e.name) }, set: function (t, r) { var e = ve(r); return new vr(t, de(e), e.name) }, object: function (t, r, e) { return Qe(c.useProxies === !1 || (e == null ? void 0 : e.proxy) === !1 ? Ht({}, e) : function (n, i) { var o, s; return Zn(), (s = (o = (n = Ht(n, i))[v]).v) != null ? s : o.v = new Proxy(n, _s) }({}, e), t, r) }, ref: K(ds), shallow: K(bs), deep: Di, struct: K(ps) }), Mi = or("computed"), gs = or("computed.struct", { equals: bt.structural }), Jt = function (t, r) { if (gt(r)) return Qt(t, r, Mi); if (it(t)) return K(or("computed", t)); var e = it(r) ? r : {}; return e.get = t, e.name || (e.name = t.name || ""), new Yt(e) }; Object.assign(Jt, Mi), Jt.struct = K(gs); var ki, Se = 0, ys = 1, ms = (zn = (Hn = xe(function () { }, "name")) == null ? void 0 : Hn.configurable) != null && zn, Xn = { value: "action", configurable: !0, writable: !1, enumerable: !1 }; ki = Symbol.toPrimitive; var Ni, pt = function (t) { function r(n, i, o, s, a) { var u; return o === void 0 && (o = "ObservableValue"), a === void 0 && (a = bt.default), (u = t.call(this, o) || this).enhancer = void 0, u.tt = void 0, u.equals = void 0, u.yt = !1, u.ut = void 0, u.ot = void 0, u.wt = void 0, u.dehancer = void 0, u.enhancer = i, u.tt = o, u.equals = a, u.wt = i(n, void 0, o), u } ni(r, t); var e = r.prototype; return e.dehanceValue = function (n) { return this.dehancer !== void 0 ? this.dehancer(n) : n }, e.set = function (n) { (n = this.jt(n)) !== c.UNCHANGED && this.Ot(n) }, e.jt = function (n) { if (L(this)) { var i = q(this, { object: this, type: Y, newValue: n }); if (!i) return c.UNCHANGED; n = i.newValue } return n = this.enhancer(n, this.wt, this.tt), this.equals(this.wt, n) ? c.UNCHANGED : n }, e.Ot = function (n) { var i = this.wt; this.wt = n, this.reportChanged(), W(this) && U(this, { type: Y, object: this, newValue: n, oldValue: i }) }, e.get = function () { return this.reportObserved(), this.dehanceValue(this.wt) }, e.xt = function (n) { return ne(this, n) }, e.At = function (n, i) { return i && n({ observableKind: "value", debugObjectName: this.tt, object: this, type: Y, newValue: this.wt, oldValue: void 0 }), ie(this, n) }, e.raw = function () { return this.wt }, e.toJSON = function () { return this.get() }, e.toString = function () { return this.tt + "[" + this.wt + "]" }, e.valueOf = function () { return ri(this.get()) }, e[ki] = function () { return this.valueOf() }, r }(se), He = ct("ObservableValue", pt); Ni = Symbol.toPrimitive; var O, Ee, Yt = function () { function t(e) { this.R = O.k, this.C = [], this.I = null, this.X = !1, this.H = !1, this.W = new Set, this.B = 0, this.D = 0, this.F = 0, this.G = O.T, this.P = 0, this.wt = new _e(null), this.tt = void 0, this.gt = void 0, this._t = !1, this.St = !1, this.derivation = void 0, this.Mt = void 0, this.Nt = Ee.NONE, this.Et = void 0, this.Vt = void 0, this.Rt = void 0, this.Tt = void 0, this.onBOL = void 0, this.onBUOL = void 0, e.get || p(31), this.derivation = e.get, this.tt = e.name || "ComputedValue", e.set && (this.Mt = yt("ComputedValue-setter", e.set)), this.Vt = e.equals || (e.compareStructural || e.struct ? bt.structural : bt.default), this.Et = e.context, this.Rt = e.requiresReaction, this.Tt = !!e.keepAlive } var r = t.prototype; return r.q = function () { (function (e) { e.G === O.T && (e.G = O.L, e.W.forEach(function (n) { n.R === O.T && (n.R = O.L, n.q()) })) })(this) }, r.onBO = function () { this.onBOL && this.onBOL.forEach(function (e) { return e() }) }, r.onBUO = function () { this.onBUOL && this.onBUOL.forEach(function (e) { return e() }) }, r.get = function () { if (this._t && p(32, this.tt, this.derivation), c.inBatch !== 0 || this.W.size !== 0 || this.Tt) { if (pi(this), $e(this)) { var e = c.trackingContext; this.Tt && !e && (c.trackingContext = this), this.trackAndCompute() && function (i) { i.G !== O.K && (i.G = O.K, i.W.forEach(function (o) { o.R === O.L ? o.R = O.K : o.R === O.T && (i.G = O.T) })) }(this), c.trackingContext = e } } else $e(this) && (this.kt(), D(), this.wt = this.Kt(!1), M()); var n = this.wt; if (ge(n)) throw n.cause; return n }, r.set = function (e) { if (this.Mt) { this.St && p(33, this.tt), this.St = !0; try { this.Mt.call(this.Et, e) } finally { this.St = !1 } } else p(34, this.tt) }, r.trackAndCompute = function () { var e = this.wt, n = this.R === O.k, i = this.Kt(!0), o = n || ge(e) || ge(i) || !this.Vt(e, i); return o && (this.wt = i), o }, r.Kt = function (e) { this._t = !0; var n, i = ee(!1); if (e) n = hi(this, this.derivation, this.Et); else if (c.disableErrorBoundaries === !0) n = this.derivation.call(this.Et); else try { n = this.derivation.call(this.Et) } catch (o) { n = new _e(o) } return re(i), this._t = !1, n }, r.U = function () { this.Tt || (Ze(this), this.wt = void 0) }, r.At = function (e, n) { var i = this, o = !0, s = void 0; return ar(function () { var a = i.get(); if (!o || n) { var u = Ct(); e({ observableKind: "computed", debugObjectName: i.tt, type: Y, object: i, newValue: a, oldValue: s }), ut(u) } o = !1, s = a }) }, r.kt = function () { }, r.toString = function () { return this.tt + "[" + this.derivation.toString() + "]" }, r.valueOf = function () { return ri(this.get()) }, r[Ni] = function () { return this.valueOf() }, t }(), mt = ct("ComputedValue", Yt); (function (t) { t[t.k = -1] = "NOT_TRACKING_", t[t.T = 0] = "UP_TO_DATE_", t[t.L = 1] = "POSSIBLY_STALE_", t[t.K = 2] = "STALE_" })(O || (O = {})), function (t) { t[t.NONE = 0] = "NONE", t[t.LOG = 1] = "LOG", t[t.BREAK = 2] = "BREAK" }(Ee || (Ee = {})); var _e = function (t) { this.cause = void 0, this.cause = t }, Os = ["mobxGuid", "spyListeners", "enforceActions", "computedRequiresReaction", "reactionRequiresObservable", "observableRequiresReaction", "allowStateReads", "disableErrorBoundaries", "runId", "UNCHANGED", "useProxies"], Kt = function () { this.version = 6, this.UNCHANGED = {}, this.trackingDerivation = null, this.trackingContext = null, this.runId = 0, this.mobxGuid = 0, this.inBatch = 0, this.pendingUnobservations = [], this.pendingReactions = [], this.isRunningReactions = !1, this.allowStateChanges = !1, this.allowStateReads = !0, this.enforceActions = !0, this.spyListeners = [], this.globalReactionErrorHandlers = [], this.computedRequiresReaction = !1, this.reactionRequiresObservable = !1, this.observableRequiresReaction = !1, this.disableErrorBoundaries = !1, this.suppressReactionErrors = !1, this.useProxies = !0, this.verifyProxies = !1, this.safeDescriptors = !0, this.stateVersion = Number.MIN_SAFE_INTEGER }, ye = !0, Vi = !1, c = function () { var t = Ye(); return t.__mobxInstanceCount > 0 && !t.__mobxGlobals && (ye = !1), t.__mobxGlobals && t.__mobxGlobals.version !== new Kt().version && (ye = !1), ye ? t.__mobxGlobals ? (t.__mobxInstanceCount += 1, t.__mobxGlobals.UNCHANGED || (t.__mobxGlobals.UNCHANGED = {}), t.__mobxGlobals) : (t.__mobxInstanceCount = 1, t.__mobxGlobals = new Kt) : (setTimeout(function () { Vi || p(35) }, 1), new Kt) }(), $t = function () { function t(e, n, i, o) { e === void 0 && (e = "Reaction"), this.tt = void 0, this.Lt = void 0, this.Ct = void 0, this.It = void 0, this.C = [], this.I = [], this.R = O.k, this.B = 0, this.D = 0, this.P = 0, this.J = !1, this.Pt = !1, this.Dt = !1, this.Bt = !1, this.Nt = Ee.NONE, this.tt = e, this.Lt = n, this.Ct = i, this.It = o } var r = t.prototype; return r.q = function () { this.Y() }, r.Y = function () { this.Pt || (this.Pt = !0, c.pendingReactions.push(this), yi()) }, r.isScheduled = function () { return this.Pt }, r.$ = function () { if (!this.J) { D(), this.Pt = !1; var e = c.trackingContext; if (c.trackingContext = this, $e(this)) { this.Dt = !0; try { this.Lt() } catch (n) { this.qt(n) } } c.trackingContext = e, M() } }, r.track = function (e) { if (!this.J) { D(), this.Bt = !0; var n = c.trackingContext; c.trackingContext = this; var i = hi(this, e, void 0); c.trackingContext = n, this.Bt = !1, this.Dt = !1, this.J && Ze(this), ge(i) && this.qt(i.cause), M() } }, r.qt = function (e) { var n = this; if (this.Ct) this.Ct(e, this); else { if (c.disableErrorBoundaries) throw e; c.suppressReactionErrors || console.error("[mobx] uncaught error in '" + this + "'", e), c.globalReactionErrorHandlers.forEach(function (i) { return i(e, n) }) } }, r.dispose = function () { this.J || (this.J = !0, this.Bt || (D(), Ze(this), M())) }, r.Z = function (e) { var n = this, i = function o() { n.dispose(), e == null || e.removeEventListener == null || e.removeEventListener("abort", o) }; return e == null || e.addEventListener == null || e.addEventListener("abort", i), i[v] = this, i }, r.toString = function () { return "Reaction[" + this.tt + "]" }, r.trace = function () { }, t }(), rr = function (t) { return t() }, Ae = ct("Reaction", $t), Li = te("action"), ws = te("action.bound", { bound: !0 }), qi = te("autoAction", { autoAction: !0 }), js = te("autoAction.bound", { autoAction: !0, bound: !0 }), st = mi(!1); Object.assign(st, Li); var Tt = mi(!0); Object.assign(Tt, qi), st.bound = K(ws), Tt.bound = K(js); var xs = function (t) { return t() }, Ss = 0; Oe.prototype = Object.create(Error.prototype); var Jn = oi("flow"), Es = oi("flow.bound", { bound: !0 }), Rt = Object.assign(function (t, r) { if (gt(r)) return Qt(t, r, Jn); var e = t, n = e.name || "<unnamed flow>", i = function () { var o, s = this, a = arguments, u = ++Ss, f = st(n + " - runid: " + u + " - init", e).apply(s, a), h = void 0, d = new Promise(function (g, y) { function b(E) { var x; h = void 0; try { x = st(n + " - runid: " + u + " - yield " + _++, f.next).call(f, E) } catch (R) { return y(R) } j(x) } function w(E) { var x; h = void 0; try { x = st(n + " - runid: " + u + " - yield " + _++, f.throw).call(f, E) } catch (R) { return y(R) } j(x) } function j(E) { if (!V(E == null ? void 0 : E.then)) return E.done ? g(E.value) : (h = Promise.resolve(E.value)).then(b, w); E.then(j, y) } var _ = 0; o = y, b(void 0) }); return d.cancel = st(n + " - runid: " + u + " - cancel", function () { try { h && Ln(h); var g = f.return(void 0), y = Promise.resolve(g.value); y.then(St, St), Ln(y), o(new Oe) } catch (b) { o(b) } }), d }; return i.isMobXFlow = !0, i }, Jn); Rt.bound = K(Es); var _s = { has: function (t, r) { return jt(t).rt(r) }, get: function (t, r) { return jt(t).vt(r) }, set: function (t, r, e) { var n; return !!gt(r) && ((n = jt(t).lt(r, e, !0)) == null || n) }, deleteProperty: function (t, r) { var e; return !!gt(r) && ((e = jt(t).Gt(r, !0)) == null || e) }, defineProperty: function (t, r, e) { var n; return (n = jt(t).h(r, e)) == null || n }, ownKeys: function (t) { return jt(t).et() }, preventExtensions: function () { p(13) } }, Xe = Symbol("mobx-keys"), Y = "update", As = { get: function (t, r) { var e = t[v]; return r === v ? e : r === "length" ? e.Wt() : typeof r != "string" || isNaN(r) ? rt(Te, r) ? Te[r] : t[r] : e.vt(parseInt(r)) }, set: function (t, r, e) { var n = t[v]; return r === "length" && n.Ht(e), typeof r == "symbol" || isNaN(r) ? t[r] = e : n.lt(parseInt(r), e), !0 }, preventExtensions: function () { p(15) } }, lr = function () { function t(e, n, i, o) { e === void 0 && (e = "ObservableArray"), this.Xt = void 0, this.Ut = void 0, this.ft = void 0, this.nt = [], this.ut = void 0, this.ot = void 0, this.Ft = void 0, this.dehancer = void 0, this.v = void 0, this.zt = 0, this.Xt = i, this.Ut = o, this.ft = new se(e), this.Ft = function (s, a) { return n(s, a, "ObservableArray[..]") } } var r = t.prototype; return r.$t = function (e) { return this.dehancer !== void 0 ? this.dehancer(e) : e }, r.ct = function (e) { return this.dehancer !== void 0 && e.length > 0 ? e.map(this.dehancer) : e }, r.xt = function (e) { return ne(this, e) }, r.At = function (e, n) { return n === void 0 && (n = !1), n && e({ observableKind: "array", object: this.v, debugObjectName: this.ft.tt, type: "splice", index: 0, added: this.nt.slice(), addedCount: this.nt.length, removed: [], removedCount: 0 }), ie(this, e) }, r.Wt = function () { return this.ft.reportObserved(), this.nt.length }, r.Ht = function (e) { (typeof e != "number" || isNaN(e) || e < 0) && p("Out of range: " + e); var n = this.nt.length; if (e !== n) if (e > n) { for (var i = new Array(e - n), o = 0; o < e - n; o++)i[o] = void 0; this.st(n, 0, i) } else this.st(e, n - e) }, r.Jt = function (e, n) { e !== this.zt && p(16), this.zt += n, this.Ut && n > 0 && Ti(e + n + 1) }, r.st = function (e, n, i) { var o = this, s = this.nt.length; if (e === void 0 ? e = 0 : e > s ? e = s : e < 0 && (e = Math.max(0, s + e)), n = arguments.length === 1 ? s - e : n == null ? 0 : Math.max(0, Math.min(n, s - e)), i === void 0 && (i = er), L(this)) { var a = q(this, { object: this.v, type: "splice", index: e, removedCount: n, added: i }); if (!a) return er; n = a.removedCount, i = a.added } if (i = i.length === 0 ? i : i.map(function (h) { return o.Ft(h, void 0) }), this.Ut) { var u = i.length - n; this.Jt(s, u) } var f = this.Yt(e, n, i); return n === 0 && i.length === 0 || this.Qt(e, i, f), this.ct(f) }, r.Yt = function (e, n, i) { var o; if (i.length < 1e4) return (o = this.nt).splice.apply(o, [e, n].concat(i)); var s = this.nt.slice(e, e + n), a = this.nt.slice(e + n); this.nt.length += i.length - n; for (var u = 0; u < i.length; u++)this.nt[e + u] = i[u]; for (var f = 0; f < a.length; f++)this.nt[e + i.length + f] = a[f]; return s }, r.Zt = function (e, n, i) { var o = !this.Xt && !1, s = W(this), a = s || o ? { observableKind: "array", object: this.v, type: Y, debugObjectName: this.ft.tt, index: e, newValue: n, oldValue: i } : null; this.ft.reportChanged(), s && U(this, a) }, r.Qt = function (e, n, i) { var o = !this.Xt && !1, s = W(this), a = s || o ? { observableKind: "array", object: this.v, debugObjectName: this.ft.tt, type: "splice", index: e, removed: i, added: n, removedCount: i.length, addedCount: n.length } : null; this.ft.reportChanged(), s && U(this, a) }, r.vt = function (e) { if (!(this.Ut && e >= this.nt.length)) return this.ft.reportObserved(), this.$t(this.nt[e]); console.warn("[mobx] Out of bounds read: " + e) }, r.lt = function (e, n) { var i = this.nt; if (this.Ut && e > i.length && p(17, e, i.length), e < i.length) { var o = i[e]; if (L(this)) { var s = q(this, { type: Y, object: this.v, index: e, newValue: n }); if (!s) return; n = s.newValue } (n = this.Ft(n, o)) !== o && (i[e] = n, this.Zt(e, n, o)) } else { for (var a = new Array(e + 1 - i.length), u = 0; u < a.length - 1; u++)a[u] = void 0; a[a.length - 1] = n, this.st(i.length, 0, a) } }, t }(), Te = { clear: function () { return this.splice(0) }, replace: function (t) { var r = this[v]; return r.st(0, r.nt.length, t) }, toJSON: function () { return this.slice() }, splice: function (t, r) { for (var e = arguments.length, n = new Array(e > 2 ? e - 2 : 0), i = 2; i < e; i++)n[i - 2] = arguments[i]; var o = this[v]; switch (arguments.length) { case 0: return []; case 1: return o.st(t); case 2: return o.st(t, r) }return o.st(t, r, n) }, spliceWithArray: function (t, r, e) { return this[v].st(t, r, e) }, push: function () { for (var t = this[v], r = arguments.length, e = new Array(r), n = 0; n < r; n++)e[n] = arguments[n]; return t.st(t.nt.length, 0, e), t.nt.length }, pop: function () { return this.splice(Math.max(this[v].nt.length - 1, 0), 1)[0] }, shift: function () { return this.splice(0, 1)[0] }, unshift: function () { for (var t = this[v], r = arguments.length, e = new Array(r), n = 0; n < r; n++)e[n] = arguments[n]; return t.st(0, 0, e), t.nt.length }, reverse: function () { return c.trackingDerivation && p(37, "reverse"), this.replace(this.slice().reverse()), this }, sort: function () { c.trackingDerivation && p(37, "sort"); var t = this.slice(); return t.sort.apply(t, arguments), this.replace(t), this }, remove: function (t) { var r = this[v], e = r.ct(r.nt).indexOf(t); return e > -1 && (this.splice(e, 1), !0) } }; A("concat", tt), A("flat", tt), A("includes", tt), A("indexOf", tt), A("join", tt), A("lastIndexOf", tt), A("slice", tt), A("toString", tt), A("toLocaleString", tt), A("every", ot), A("filter", ot), A("find", ot), A("findIndex", ot), A("flatMap", ot), A("forEach", ot), A("map", ot), A("some", ot), A("reduce", Fn), A("reduceRight", Fn); var Bi, Gi, Ts = ct("ObservableArrayAdministration", lr), Rs = {}, at = "add"; Bi = Symbol.iterator, Gi = Symbol.toStringTag; var Fi, Ki, hr = function () { function t(e, n, i) { var o = this; n === void 0 && (n = Ut), i === void 0 && (i = "ObservableMap"), this.Ft = void 0, this.tt = void 0, this[v] = Rs, this.bt = void 0, this.pt = void 0, this.dt = void 0, this.ut = void 0, this.ot = void 0, this.dehancer = void 0, this.Ft = n, this.tt = i, V(Map) || p(18), this.dt = ir("ObservableMap.keys()"), this.bt = new Map, this.pt = new Map, me(!0, function () { o.merge(e) }) } var r = t.prototype; return r.rt = function (e) { return this.bt.has(e) }, r.has = function (e) { var n = this; if (!c.trackingDerivation) return this.rt(e); var i = this.pt.get(e); if (!i) { var o = i = new pt(this.rt(e), Pe, "ObservableMap.key?", !1); this.pt.set(e, o), ur(o, function () { return n.pt.delete(e) }) } return i.get() }, r.set = function (e, n) { var i = this.rt(e); if (L(this)) { var o = q(this, { type: i ? Y : at, object: this, newValue: n, name: e }); if (!o) return this; n = o.newValue } return i ? this.tn(e, n) : this.nn(e, n), this }, r.delete = function (e) { var n = this; if (L(this) && !q(this, { type: "delete", object: this, name: e })) return !1; if (this.rt(e)) { var i = W(this), o = i ? { observableKind: "map", debugObjectName: this.tt, type: "delete", object: this, oldValue: this.bt.get(e).wt, name: e } : null; return H(function () { var s; n.dt.reportChanged(), (s = n.pt.get(e)) == null || s.Ot(!1), n.bt.get(e).Ot(void 0), n.bt.delete(e) }), i && U(this, o), !0 } return !1 }, r.tn = function (e, n) { var i = this.bt.get(e); if ((n = i.jt(n)) !== c.UNCHANGED) { var o = W(this), s = o ? { observableKind: "map", debugObjectName: this.tt, type: Y, object: this, oldValue: i.wt, name: e, newValue: n } : null; i.Ot(n), o && U(this, s) } }, r.nn = function (e, n) { var i = this; H(function () { var s, a = new pt(n, i.Ft, "ObservableMap.key", !1); i.bt.set(e, a), n = a.wt, (s = i.pt.get(e)) == null || s.Ot(!0), i.dt.reportChanged() }); var o = W(this); o && U(this, o ? { observableKind: "map", debugObjectName: this.tt, type: at, object: this, name: e, newValue: n } : null) }, r.get = function (e) { return this.has(e) ? this.$t(this.bt.get(e).get()) : this.$t(void 0) }, r.$t = function (e) { return this.dehancer !== void 0 ? this.dehancer(e) : e }, r.keys = function () { return this.dt.reportObserved(), this.bt.keys() }, r.values = function () { var e = this, n = this.keys(); return Xt({ next: function () { var i = n.next(), o = i.done; return { done: o, value: o ? void 0 : e.get(i.value) } } }) }, r.entries = function () { var e = this, n = this.keys(); return Xt({ next: function () { var i = n.next(), o = i.done, s = i.value; return { done: o, value: o ? void 0 : [s, e.get(s)] } } }) }, r[Bi] = function () { return this.entries() }, r.forEach = function (e, n) { for (var i, o = xt(this); !(i = o()).done;) { var s = i.value; e.call(n, s[1], s[0], this) } }, r.merge = function (e) { var n = this; return C(e) && (e = new Map(e)), H(function () { it(e) ? function (i) { var o = Object.keys(i); if (!Pi) return o; var s = Object.getOwnPropertySymbols(i); return s.length ? [].concat(o, s.filter(function (a) { return oe.propertyIsEnumerable.call(i, a) })) : o }(e).forEach(function (i) { return n.set(i, e[i]) }) : Array.isArray(e) ? e.forEach(function (i) { return n.set(i[0], i[1]) }) : At(e) ? (e.constructor !== Map && p(19, e), e.forEach(function (i, o) { return n.set(o, i) })) : e != null && p(20, e) }), this }, r.clear = function () { var e = this; H(function () { sr(function () { for (var n, i = xt(e.keys()); !(n = i()).done;)e.delete(n.value) }) }) }, r.replace = function (e) { var n = this; return H(function () { for (var i, o = function (Z) { if (At(Z) || C(Z)) return Z; if (Array.isArray(Z)) return new Map(Z); if (it(Z)) { var mr = new Map; for (var Or in Z) mr.set(Or, Z[Or]); return mr } return p(21, Z) }(e), s = new Map, a = !1, u = xt(n.bt.keys()); !(i = u()).done;) { var f = i.value; if (!o.has(f)) if (n.delete(f)) a = !0; else { var h = n.bt.get(f); s.set(f, h) } } for (var d, g = xt(o.entries()); !(d = g()).done;) { var y = d.value, b = y[0], w = y[1], j = n.bt.has(b); if (n.set(b, w), n.bt.has(b)) { var _ = n.bt.get(b); s.set(b, _), j || (a = !0) } } if (!a) if (n.bt.size !== s.size) n.dt.reportChanged(); else for (var E = n.bt.keys(), x = s.keys(), R = E.next(), dt = x.next(); !R.done;) { if (R.value !== dt.value) { n.dt.reportChanged(); break } R = E.next(), dt = x.next() } n.bt = s }), this }, r.toString = function () { return "[object ObservableMap]" }, r.toJSON = function () { return Array.from(this) }, r.At = function (e) { return ie(this, e) }, r.xt = function (e) { return ne(this, e) }, nr(t, [{ key: "size", get: function () { return this.dt.reportObserved(), this.bt.size } }, { key: Gi, get: function () { return "Map" } }]), t }(), C = ct("ObservableMap", hr), Ps = {}; Fi = Symbol.iterator, Ki = Symbol.toStringTag; var Ft, pe, vr = function () { function t(e, n, i) { n === void 0 && (n = Ut), i === void 0 && (i = "ObservableSet"), this.tt = void 0, this[v] = Ps, this.bt = new Set, this.ft = void 0, this.ot = void 0, this.ut = void 0, this.dehancer = void 0, this.Ft = void 0, this.tt = i, V(Set) || p(22), this.ft = ir(this.tt), this.Ft = function (o, s) { return n(o, s, i) }, e && this.replace(e) } var r = t.prototype; return r.$t = function (e) { return this.dehancer !== void 0 ? this.dehancer(e) : e }, r.clear = function () { var e = this; H(function () { sr(function () { for (var n, i = xt(e.bt.values()); !(n = i()).done;)e.delete(n.value) }) }) }, r.forEach = function (e, n) { for (var i, o = xt(this); !(i = o()).done;) { var s = i.value; e.call(n, s, s, this) } }, r.add = function (e) { var n = this; if (L(this) && !q(this, { type: at, object: this, newValue: e })) return this; if (!this.has(e)) { H(function () { n.bt.add(n.Ft(e, void 0)), n.ft.reportChanged() }); var i = W(this); i && U(this, i ? { observableKind: "set", debugObjectName: this.tt, type: at, object: this, newValue: e } : null) } return this }, r.delete = function (e) { var n = this; if (L(this) && !q(this, { type: "delete", object: this, oldValue: e })) return !1; if (this.has(e)) { var i = W(this), o = i ? { observableKind: "set", debugObjectName: this.tt, type: "delete", object: this, oldValue: e } : null; return H(function () { n.ft.reportChanged(), n.bt.delete(e) }), i && U(this, o), !0 } return !1 }, r.has = function (e) { return this.ft.reportObserved(), this.bt.has(this.$t(e)) }, r.entries = function () { var e = 0, n = Array.from(this.keys()), i = Array.from(this.values()); return Xt({ next: function () { var o = e; return e += 1, o < i.length ? { value: [n[o], i[o]], done: !1 } : { done: !0 } } }) }, r.keys = function () { return this.values() }, r.values = function () { this.ft.reportObserved(); var e = this, n = 0, i = Array.from(this.bt.values()); return Xt({ next: function () { return n < i.length ? { value: e.$t(i[n++]), done: !1 } : { done: !0 } } }) }, r.replace = function (e) { var n = this; return k(e) && (e = new Set(e)), H(function () { Array.isArray(e) || Zt(e) ? (n.clear(), e.forEach(function (i) { return n.add(i) })) : e != null && p("Cannot initialize set from " + e) }), this }, r.At = function (e) { return ie(this, e) }, r.xt = function (e) { return ne(this, e) }, r.toJSON = function () { return Array.from(this) }, r.toString = function () { return "[object ObservableSet]" }, r[Fi] = function () { return this.values() }, nr(t, [{ key: "size", get: function () { return this.ft.reportObserved(), this.bt.size } }, { key: Ki, get: function () { return "Set" } }]), t }(), k = ct("ObservableSet", vr), Yn = Object.create(null), Wi = function () { function t(e, n, i, o) { n === void 0 && (n = new Map), o === void 0 && (o = vs), this.s = void 0, this.nt = void 0, this.tt = void 0, this.in = void 0, this.dt = void 0, this.ot = void 0, this.ut = void 0, this.v = void 0, this.l = void 0, this.rn = void 0, this.en = void 0, this.s = e, this.nt = n, this.tt = i, this.in = o, this.dt = new se("ObservableObject.keys"), this.l = it(this.s) } var r = t.prototype; return r.at = function (e) { return this.nt.get(e).get() }, r.ht = function (e, n) { var i = this.nt.get(e); if (i instanceof Yt) return i.set(n), !0; if (L(this)) { var o = q(this, { type: Y, object: this.v || this.s, name: e, newValue: n }); if (!o) return null; n = o.newValue } if ((n = i.jt(n)) !== c.UNCHANGED) { var s = W(this), a = s ? { type: Y, observableKind: "object", debugObjectName: this.tt, object: this.v || this.s, oldValue: i.wt, name: e, newValue: n } : null; i.Ot(n), s && U(this, a) } return !0 }, r.vt = function (e) { return c.trackingDerivation && !rt(this.s, e) && this.rt(e), this.s[e] }, r.lt = function (e, n, i) { return i === void 0 && (i = !1), rt(this.s, e) ? this.nt.has(e) ? this.ht(e, n) : i ? Reflect.set(this.s, e, n) : (this.s[e] = n, !0) : this.o(e, { value: n, enumerable: !0, writable: !0, configurable: !0 }, this.in, i) }, r.rt = function (e) { if (!c.trackingDerivation) return e in this.s; this.en || (this.en = new Map); var n = this.en.get(e); return n || (n = new pt(e in this.s, Pe, "ObservableObject.key?", !1), this.en.set(e, n)), n.get() }, r.u = function (e, n) { if (n === !0 && (n = this.in), n !== !1) { if (!(e in this.s)) { var i; if ((i = this.s[X]) != null && i[e]) return; p(1, n.t, this.tt + "." + e.toString()) } for (var o = this.s; o && o !== oe;) { var s = xe(o, e); if (s) { var a = n.u(this, e, s, o); if (a === 0) return; if (a === 1) break } o = Object.getPrototypeOf(o) } Wn(this, 0, e) } }, r.o = function (e, n, i, o) { if (o === void 0 && (o = !1), i === !0 && (i = this.in), i === !1) return this.h(e, n, o); var s = i.o(this, e, n, o); return s && Wn(this, 0, e), s }, r.h = function (e, n, i) { i === void 0 && (i = !1); try { D(); var o = this.Gt(e); if (!o) return o; if (L(this)) { var s = q(this, { object: this.v || this.s, name: e, type: at, newValue: n.value }); if (!s) return null; var a = s.newValue; n.value !== a && (n = Pt({}, n, { value: a })) } if (i) { if (!Reflect.defineProperty(this.s, e, n)) return !1 } else J(this.s, e, n); this.un(e, n.value) } finally { M() } return !0 }, r.m = function (e, n, i, o) { o === void 0 && (o = !1); try { D(); var s = this.Gt(e); if (!s) return s; if (L(this)) { var a = q(this, { object: this.v || this.s, name: e, type: at, newValue: n }); if (!a) return null; n = a.newValue } var u = Kn(e), f = { configurable: !c.safeDescriptors || this.l, enumerable: !0, get: u.get, set: u.set }; if (o) { if (!Reflect.defineProperty(this.s, e, f)) return !1 } else J(this.s, e, f); var h = new pt(n, i, "ObservableObject.key", !1); this.nt.set(e, h), this.un(e, h.wt) } finally { M() } return !0 }, r.p = function (e, n, i) { i === void 0 && (i = !1); try { D(); var o = this.Gt(e); if (!o) return o; if (L(this) && !q(this, { object: this.v || this.s, name: e, type: at, newValue: void 0 })) return null; n.name || (n.name = "ObservableObject.key"), n.context = this.v || this.s; var s = Kn(e), a = { configurable: !c.safeDescriptors || this.l, enumerable: !1, get: s.get, set: s.set }; if (i) { if (!Reflect.defineProperty(this.s, e, a)) return !1 } else J(this.s, e, a); this.nt.set(e, new Yt(n)), this.un(e, void 0) } finally { M() } return !0 }, r.Gt = function (e, n) { if (n === void 0 && (n = !1), !rt(this.s, e)) return !0; if (L(this) && !q(this, { object: this.v || this.s, name: e, type: "remove" })) return null; try { var i, o; D(); var s, a = W(this), u = this.nt.get(e), f = void 0; if (!u && a && (f = (s = xe(this.s, e)) == null ? void 0 : s.value), n) { if (!Reflect.deleteProperty(this.s, e)) return !1 } else delete this.s[e]; u && (this.nt.delete(e), u instanceof pt && (f = u.wt), gi(u)), this.dt.reportChanged(), (i = this.en) == null || (o = i.get(e)) == null || o.set(e in this.s), a && a && U(this, { type: "remove", observableKind: "object", object: this.v || this.s, debugObjectName: this.tt, oldValue: f, name: e }) } finally { M() } return !0 }, r.At = function (e) { return ie(this, e) }, r.xt = function (e) { return ne(this, e) }, r.un = function (e, n) { var i, o, s = W(this); s && s && U(this, s ? { type: at, observableKind: "object", debugObjectName: this.tt, object: this.v || this.s, name: e, newValue: n } : null), (i = this.en) == null || (o = i.get(e)) == null || o.set(!0), this.dt.reportChanged() }, r.et = function () { return this.dt.reportObserved(), _t(this.s) }, r.it = function () { return this.dt.reportObserved(), Object.keys(this.s) }, t }(), Cs = ct("ObservableObjectAdministration", Wi), Is = Ai(0), Je = 0, Ui = function () { }; Ft = Ui, pe = Array.prototype, Object.setPrototypeOf ? Object.setPrototypeOf(Ft.prototype, pe) : Ft.prototype.__proto__ !== void 0 ? Ft.prototype.__proto__ = pe : Ft.prototype = pe; var dr = function (t, r, e) { function n(o, s, a, u) { var f; a === void 0 && (a = "ObservableArray"), u === void 0 && (u = !1), f = t.call(this) || this; var h = new lr(a, s, u, !0); if (h.v = ze(f), ei(ze(f), v, h), o && o.length) { var d = ee(!0); f.spliceWithArray(0, 0, o), re(d) } return Object.defineProperty(ze(f), "0", Is), f } ni(n, t); var i = n.prototype; return i.concat = function () { this[v].ft.reportObserved(); for (var o = arguments.length, s = new Array(o), a = 0; a < o; a++)s[a] = arguments[a]; return Array.prototype.concat.apply(this.slice(), s.map(function (u) { return N(u) ? u.slice() : u })) }, i[e] = function () { var o = this, s = 0; return Xt({ next: function () { return s < o.length ? { value: o[s++], done: !1 } : { done: !0, value: void 0 } } }) }, nr(n, [{ key: "length", get: function () { return this[v].Wt() }, set: function (o) { this[v].Ht(o) } }, { key: r, get: function () { return "Array" } }]), n }(Ui, Symbol.toStringTag, Symbol.iterator); Object.entries(Te).forEach(function (t) { var r = t[0]; r !== "concat" && Wt(dr.prototype, r, t[1]) }), Ti(1e3); var $n = oe.toString;["Symbol", "Map", "Set"].forEach(function (t) { Ye()[t] === void 0 && p("MobX requires global '" + t + "' to be available or polyfilled") }), typeof __MOBX_DEVTOOLS_GLOBAL_HOOK__ == "object" && __MOBX_DEVTOOLS_GLOBAL_HOOK__.injectMobx({ spy: Nn, extras: { getDebugName: je }, $mobx: v }), l.$mobx = v, l.FlowCancellationError = Oe, l.ObservableMap = hr, l.ObservableSet = vr, l.Reaction = $t, l._allowStateChanges = me, l._allowStateChangesInsideComputed = Vn, l._allowStateReadsEnd = Et, l._allowStateReadsStart = Ie, l._autoAction = Tt, l._endAction = li, l._getAdministration = et, l._getGlobalState = function () { return c }, l._interceptReads = function (t, r, e) { var n; return C(t) || N(t) || He(t) ? n = et(t) : P(t) && (n = et(t, r)), n.dehancer = typeof r == "function" ? r : e, function () { n.dehancer = void 0 } }, l._isComputingDerivation = function () { return c.trackingDerivation !== null }, l._resetGlobalState = function () { var t = new Kt; for (var r in t) Os.indexOf(r) === -1 && (c[r] = t[r]); c.allowStateChanges = !c.enforceActions }, l._startAction = fi, l.action = st, l.autorun = ar, l.comparer = bt, l.computed = Jt, l.configure = function (t) { t.isolateGlobalState === !0 && function () { if ((c.pendingReactions.length || c.inBatch || c.isRunningReactions) && p(36), Vi = !0, ye) { var s = Ye(); --s.__mobxInstanceCount == 0 && (s.__mobxGlobals = void 0), c = new Kt } }(); var r, e, n = t.useProxies, i = t.enforceActions; if (n !== void 0 && (c.useProxies = n === "always" || n !== "never" && typeof Proxy != "undefined"), n === "ifavailable" && (c.verifyProxies = !0), i !== void 0) { var o = i === "always" ? "always" : i === "observed"; c.enforceActions = o, c.allowStateChanges = o !== !0 && o !== "always" } ["computedRequiresReaction", "reactionRequiresObservable", "observableRequiresReaction", "disableErrorBoundaries", "safeDescriptors"].forEach(function (s) { s in t && (c[s] = !!t[s]) }), c.allowStateReads = !c.observableRequiresReaction, t.reactionScheduler && (r = t.reactionScheduler, e = rr, rr = function (s) { return r(function () { return e(s) }) }) }, l.createAtom = ir, l.defineProperty = function (t, r, e) { if (P(t)) return t[v].h(r, e); p(39) }, l.entries = function (t) { return P(t) ? Gt(t).map(function (r) { return [r, t[r]] }) : C(t) ? Gt(t).map(function (r) { return [r, t.get(r)] }) : k(t) ? Array.from(t.entries()) : N(t) ? t.map(function (r, e) { return [e, r] }) : void p(7) }, l.extendObservable = Qe, l.flow = Rt, l.flowResult = function (t) { return t }, l.get = function (t, r) { if (Bn(t, r)) return P(t) ? t[v].vt(r) : C(t) ? t.get(r) : N(t) ? t[r] : void p(11) }, l.getAtom = nt, l.getDebugName = je, l.getDependencyTree = function (t, r) { return xi(nt(t, r)) }, l.getObserverTree = function (t, r) { return Si(nt(t, r)) }, l.has = Bn, l.intercept = function (t, r, e) { return V(e) ? function (n, i, o) { return et(n, i).xt(o) }(t, r, e) : function (n, i) { return et(n).xt(i) }(t, r) }, l.isAction = De, l.isBoxedObservable = He, l.isComputed = function (t) { return qn(t) }, l.isComputedProp = function (t, r) { return qn(t, r) }, l.isFlow = zt, l.isFlowCancellationError = function (t) { return t instanceof Oe }, l.isObservable = we, l.isObservableArray = N, l.isObservableMap = C, l.isObservableObject = P, l.isObservableProp = function (t, r) { return Ei(t, r) }, l.isObservableSet = k, l.keys = Gt, l.makeAutoObservable = function (t, r, e) { if (it(t)) return Qe(t, t, r, e); var n = Ht(t, e)[v]; if (!t[Xe]) { var i = Object.getPrototypeOf(t), o = new Set([].concat(_t(t), _t(i))); o.delete("constructor"), o.delete(v), Wt(i, Xe, o) } D(); try { t[Xe].forEach(function (s) { return n.u(s, !r || !(s in r) || r[s]) }) } finally { M() } return t }, l.makeObservable = function (t, r, e) { var n = Ht(t, e)[v]; D(); try { r != null || (r = function (i) { return rt(i, X) || Wt(i, X, Pt({}, i[X])), i[X] }(t)), _t(r).forEach(function (i) { return n.u(i, r[i]) }) } finally { M() } return t }, l.observable = T, l.observe = function (t, r, e, n) { return V(e) ? function (i, o, s, a) { return et(i, o).At(s, a) }(t, r, e, n) : function (i, o, s) { return et(i).At(o, s) }(t, r, e) }, l.onBecomeObserved = wi, l.onBecomeUnobserved = ur, l.onReactionError = function (t) { return c.globalReactionErrorHandlers.push(t), function () { var r = c.globalReactionErrorHandlers.indexOf(t); r >= 0 && c.globalReactionErrorHandlers.splice(r, 1) } }, l.override = hs, l.ownKeys = Gn, l.reaction = function (t, r, e) { function n() { if (_ = !1, !x.J) { var R = !1; x.track(function () { var dt = me(!1, function () { return t(x) }); R = j || !E(h, dt), d = h, h = dt }), (j && e.fireImmediately || !j && R) && y(h, d, x), j = !1 } } var i, o, s, a; e === void 0 && (e = cr); var u, f, h, d, g = (i = e.name) != null ? i : "Reaction", y = st(g, e.onError ? (u = e.onError, f = r, function () { try { return f.apply(this, arguments) } catch (R) { u.call(this, R) } }) : r), b = !e.scheduler && !e.delay, w = Oi(e), j = !0, _ = !1, E = e.compareStructural ? bt.structural : e.equals || bt.default, x = new $t(g, function () { j || b ? n() : _ || (_ = !0, w(n)) }, e.onError, e.requiresObservable); return (o = e) != null && (s = o.signal) != null && s.aborted || x.Y(), x.Z((a = e) == null ? void 0 : a.signal) }, l.remove = function (t, r) { P(t) ? t[v].Gt(r) : C(t) || k(t) ? t.delete(r) : N(t) ? (typeof r != "number" && (r = parseInt(r, 10)), t.splice(r, 1)) : p(9) }, l.runInAction = Vn, l.set = function t(r, e, n) { if (arguments.length !== 2 || k(r)) P(r) ? r[v].lt(e, n) : C(r) ? r.set(e, n) : k(r) ? r.add(e) : N(r) ? (typeof e != "number" && (e = parseInt(e, 10)), e < 0 && p("Invalid index: '" + e + "'"), D(), e >= r.length && (r.length = e + 1), r[e] = n, M()) : p(8); else { D(); var i = e; try { for (var o in i) t(r, o, i[o]) } finally { M() } } }, l.spy = Nn, l.toJS = function (t) { return function r(e, n) { if (e == null || typeof e != "object" || e instanceof Date || !we(e)) return e; if (He(e) || mt(e)) return r(e.get(), n); if (n.has(e)) return n.get(e); if (N(e)) { var i = be(n, e, new Array(e.length)); return e.forEach(function (u, f) { i[f] = r(u, n) }), i } if (k(e)) { var o = be(n, e, new Set); return e.forEach(function (u) { o.add(r(u, n)) }), o } if (C(e)) { var s = be(n, e, new Map); return e.forEach(function (u, f) { s.set(f, r(u, n)) }), s } var a = be(n, e, {}); return Gn(e).forEach(function (u) { oe.propertyIsEnumerable.call(e, u) && (a[u] = r(e[u], n)) }), a }(t, new Map) }, l.trace = function () { }, l.transaction = H, l.untracked = sr, l.values = function (t) { return P(t) ? Gt(t).map(function (r) { return t[r] }) : C(t) ? Gt(t).map(function (r) { return t.get(r) }) : k(t) ? Array.from(t.values()) : N(t) ? t.slice() : void p(6) }, l.when = function (t, r, e) { return arguments.length === 1 || r && typeof r == "object" ? ns(t, r) : _i(t, r, e || {}) }
    }); var ae = m((da, Hi) => { Hi.exports = zi() }); var $ = {}; ho($, { configureLog: () => to, connectComponent: () => ro, connectPage: () => no }); module.exports = vo($); var qa = Nt(Dn()), io = Nt(ae()); var eo = Nt(ae()); var Xi = t => Object.prototype.toString.call(t); function Ji(t) { return !!(t && typeof t == "object") } var Yi = "[object RegExp]", ft = "[object Object]", Me = "[object Function]", $i = "[object Date]", lt = "[object Array]"; var Zi = Nt(ae()); function ke(t) { if (!t || typeof t == "string" || typeof t == "boolean" || typeof t == "number") return t; let r = Xi(t); if ([Yi, Me, $i].includes(r)) return t; if (Array.isArray(t) || (0, Zi.isObservableArray)(t)) { let n = []; for (let i = 0; i < t.length; i++)n.push(ke(t[i])); return n } if (!Ji(t)) return t; let e = {}; return br(t).forEach(n => { e[n] = ke(t[n]) }), e } function br(t) { let r = [], e = [], n = [], i = Object.getOwnPropertyDescriptors(t); for (let o in i) typeof t[o] == "function" ? r.push(o) : i[o].get ? n.push(o) : e.push(o); return n.concat(e) } function Qi(t, r) { let e = {}; return pr(t, r), Ne(t, r, "", e), e } function pr(t, r) { if (t === r) return; let e = Ot(t), n = Ot(r); if (e == ft && n == ft) for (let i in r) { let o = t[i]; o === void 0 ? t[i] = null : pr(o, r[i]) } else e == lt && n == lt && t.length >= r.length && r.forEach((i, o) => { pr(t[o], i) }) } function Ne(t, r, e, n) { if (t === r) return; let i = Ot(t), o = Ot(r); if (i == ft) if (o != ft || Object.keys(t).length < Object.keys(r).length && e !== "") ht(n, e, t); else for (let s in t) { let a = t[s], u = r[s], f = Ot(a), h = Ot(u); if (f != lt && f != ft) a !== r[s] && ht(n, It(e, s), a); else if (f == lt) h != lt || a.length < u.length ? ht(n, It(e, s), a) : a.forEach((d, g) => { Ne(d, u[g], It(e, s) + "[" + g + "]", n) }); else if (f == ft) if (h != ft || Object.keys(a).length < Object.keys(u).length) ht(n, It(e, s), a); else for (let d in a) { let g = It(e, s) + (d.includes(".") ? `["${d}"]` : `.${d}`); Ne(a[d], u[d], g, n) } } else i == lt ? o != lt || t.length < r.length ? ht(n, e, t) : t.forEach((s, a) => { Ne(s, r[a], e + "[" + a + "]", n) }) : ht(n, e, t) } function It(t, r) { return r.includes(".") ? t + `["${r}"]` : (t == "" ? "" : t + ".") + r } function ht(t, r, e) { Ot(e) != Me && (t[r] = e) } function Ot(t) { return Object.prototype.toString.call(t) } var Le = class Le { constructor() { G(this, "loggable", !1) } configureLog(r) { (r == null ? void 0 : r.enable) === "devtools" && (this.loggable = this.enableLogInDevtools()), typeof (r == null ? void 0 : r.enable) == "boolean" && (this.loggable = r.enable) } enableLogInDevtools() { return wx != null && wx.canIUse("getSystemInfoSync") ? wx.getSystemInfoSync().platform === "devtools" : !1 } log({ name: r, color: e = "orange", value: n }) { return this.loggable ? (console.log(`%c${r}`, `background-color:${Le.ColorMap[e]};padding:2px;color:#FFF;`, n), !0) : !1 } }; G(Le, "ColorMap", { orange: "#d95220", blue: "#6cb6ff", green: "#67de7b" }); var gr = Le, Ve = new gr, to = (...t) => { Ve.configureLog.apply(Ve, t) }; var Dt = (() => { let t = [], r = []; return e => { typeof e != "object" && (console.error("[mobx-miniprogram-lite] store is supposed to be an object, some error may occur!"), console.error("store: " + e)); for (let o = 0; o < t.length; o++)if (t[o] === e) return r[o]; t.push(e); let n = t.length - 1, i = new yr({ id: n, model: e }); return r.push(i), i } })(), yr = class { constructor(r) { G(this, "id"); G(this, "model"); G(this, "listeners", []); G(this, "expose", null); G(this, "latestData", {}); G(this, "bindListener", r => { this.listeners.push(r), r.length === 1 && !this.expose && this.active() }); G(this, "unbindListener", r => { this.listeners = this.listeners.filter(e => e !== r), this.listeners.length === 0 && this.inactive() }); this.id = r.id, this.model = r.model, this.active() } createReaction(r) { let e = br(r), n = []; return e.forEach(i => { let o = (0, eo.reaction)(() => { let s = ke(r[i]); return this.latestData[i] = s, s }, (s, a) => { let u = Qi(s, a), f = Object.entries(u).reduce((h, [d, g]) => { let y = d.startsWith("[") || !d ? "" : "."; return h[i + y + d] = g, h }, {}); Ve.log({ name: "diff", color: "orange", value: f }), this.listeners.forEach(h => h.call(null, f)) }); n.push(o) }), () => { n.forEach(i => i == null ? void 0 : i.call(null)) } } active() { this.expose = this.createReaction(this.model) } inactive() { var r; (r = this.expose) == null || r.call(this), this.expose = null } }; function Ds() { return {} } function Ms(t, r) { let e = { listeners: [] }; return t[r] = e, e } var ks = (() => { let t = 0; return () => t++ + "" })(), vt = { createViewInstanceMap: Ds, createViewInstance: Ms, createViewId: ks }; var ro = t => { var d, g, y; let r = (d = t.lifetimes) == null ? void 0 : d.attached, e = (g = t.lifetimes) == null ? void 0 : g.detached, n = (y = t.lifetimes) == null ? void 0 : y.created, i = t.store; function o(b, w) { if (!Object.keys(w).length) return; let j = Object.entries(w).reduce((_, [E, x]) => (_[b + E] = x, _), {}); wx.nextTick(() => { this.setData(j) }) } let s = vt.createViewInstanceMap(), a = function () { let b = vt.createViewId(), w = vt.createViewInstance(s, b); this.__VIEW_ID__ = b; let j = {}; Object.entries(i).forEach(([_, E]) => { let x = o.bind(this, _ + "."), R = Dt(E); R.bindListener(x), w.listeners.push(x), j[_] = R.latestData }), this.setData(B({}, j)) }; function u() { a.call(this), r == null || r.call(this) } function f() { var j, _; let b = this.__VIEW_ID__, w = (_ = (j = s[b]) == null ? void 0 : j.listeners) != null ? _ : []; w.length && Object.entries(i).forEach(([E, x]) => { let R = Dt(x); w.forEach(dt => R.unbindListener(dt)) }), s[b] && delete s[b], e == null || e.call(this) } function h() { this.store = i, n == null || n.call(this) } return t.lifetimes = kt(B({}, t.lifetimes), { attached: u, detached: f, created: h }), delete t.store, Component(kt(B({}, t), { data: B(B({}, t.data), i) })) }; var no = t => { let r = t.onLoad, e = t.onUnload, n = t.store; function i(f, h) { if (!Object.keys(h).length) return; let d = Object.entries(h).reduce((g, [y, b]) => (g[f + y] = b, g), {}); wx.nextTick(() => { this.setData(d) }) } let o = vt.createViewInstanceMap(), s = function () { let f = vt.createViewId(), h = vt.createViewInstance(o, f); this.__VIEW_ID__ = f; let d = {}; Object.entries(n).forEach(([g, y]) => { let b = i.bind(this, g + "."), w = Dt(y); w.bindListener(b), h.listeners.push(b), d[g] = w.latestData }), this.setData(B({}, d)) }; function a(...f) { this.store = n, s.call(this), r == null || r.apply(this, f) } function u(...f) { var g, y; let h = this.__VIEW_ID__, d = (y = (g = o[h]) == null ? void 0 : g.listeners) != null ? y : []; d.length && Object.entries(n).forEach(([b, w]) => { let j = Dt(w); d.forEach(_ => j.unbindListener(_)) }), o[h] && delete o[h], e == null || e.apply(this, f) } return delete t.store, Page(kt(B({}, t), { onLoad: a, onUnload: u, data: B(B({}, t.data), n) })) }; Q($, Nt(ae()), module.exports); (0, io.configure)({ useProxies: "never" }); 0 && (module.exports = { configureLog, connectComponent, connectPage });
    //# sourceMappingURL=index.common.js.map

  }, function (modId) { var map = {}; return __REQUIRE__(map[modId], modId); })
  return __REQUIRE__(1707137412199);
})()
//miniprogram-npm-outsideDeps=[]
//# sourceMappingURL=index.js.map