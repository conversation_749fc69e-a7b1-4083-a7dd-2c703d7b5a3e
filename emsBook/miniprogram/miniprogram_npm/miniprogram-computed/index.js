var E=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports);var H=E(M=>{"use strict";Object.defineProperty(M,"__esModule",{value:!0}),function(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}(M,{parseMultiDataPaths:function(){return it},getDataOnPath:function(){return ut}});var N=/^\s/,C=function(t,e){throw Error('Parsing data path "'+t+'" failed at char "'+t[e]+'" (index '+e+")")},rt=function(t,e){for(var r=e.index;e.index<e.length;){var i=t[e.index];if(/^[0-9]/.test(i)){e.index++;continue}break}return r===e.index&&C(t,e.index),parseInt(t.slice(r,e.index),10)},$=function(t,e){var r=e.index,i=t[r];if(/^[_a-zA-Z$]/.test(i))for(e.index++;e.index<e.length;){var f=t[e.index];if(/^[_a-zA-Z0-9$]/.test(f)){e.index++;continue}break}else C(t,e.index);return t.slice(r,e.index)},q=function(t,e){for(var r=[$(t,e)],i={deepCmp:!1};e.index<e.length;){var f=t[e.index];if(f==="[")e.index++,r.push(""+rt(t,e)),t[e.index]!=="]"&&C(t,e.index),e.index++;else if(f==="."){if(e.index++,t[e.index]==="*"){if(e.index++,t[e.index]==="*"){e.index++,i.deepCmp=!0;break}C(t,e.index)}r.push($(t,e))}else break}return{path:r,options:i}},nt=function(t,e){for(;N.test(t[e.index]);)e.index++;for(var r=[q(t,e)],i=!1;e.index<e.length;){var f=t[e.index];N.test(f)?e.index++:f===","?(i=!0,e.index++):i?(i=!1,r.push(q(t,e))):C(t,e.index)}return r},ot=function(t,e){e.index<e.length&&C(t,e.index)},it=function(t){var e={length:t.length,index:0},r=nt(t,e);return ot(t,e),r},ut=function(t,e){var r=t;return e.forEach(function(i){r=typeof r!="object"||r===null?void 0:r[i]}),r}});var z=E((mt,U)=>{U.exports=function(){let e=null,r;function i(n){return n?typeof n=="object"||typeof n=="function":!1}function f(n){if(n!==null&&!i(n))throw new TypeError("Object prototype may only be an Object or null: "+n)}let a=Object,u=Boolean(a.create)||!({__proto__:null}instanceof a),d=a.create||(u?function(o){return f(o),{__proto__:o}}:function(o){if(f(o),o===null)throw new SyntaxError("Native Object.create is required to create objects with null prototype");var h=function(){};return h.prototype=o,new h}),m=function(){return null},p=a.getPrototypeOf||([].__proto__===Array.prototype?function(o){let h=o.__proto__;return i(h)?h:null}:m);return r=function(n,o){if((this&&this instanceof r?this.constructor:void 0)===void 0)throw new TypeError("Constructor Proxy requires 'new'");if(!i(n)||!i(o))throw new TypeError("Cannot create proxy with a non-object as target or handler");let y=function(){};e=function(){n=null,y=function(c){throw new TypeError(`Cannot perform '${c}' on a proxy that has been revoked`)}},setTimeout(function(){e=null},0);let v=o;o={get:null,set:null,apply:null,construct:null};for(let c in v){if(!(c in o))throw new TypeError(`Proxy polyfill does not support trap '${c}'`);o[c]=v[c]}typeof v=="function"&&(o.apply=v.apply.bind(v));let P=p(n),s,l=!1,b=!1;typeof n=="function"?(s=function(){let _=this&&this.constructor===s,x=Array.prototype.slice.call(arguments);if(y(_?"construct":"apply"),_&&o.construct)return o.construct.call(this,n,x);if(!_&&o.apply)return o.apply(n,this,x);if(_){x.unshift(n);let j=n.bind.apply(n,x);return new j}return n.apply(this,x)},l=!0):n instanceof Array?(s=[],b=!0):s=u||P!==null?d(P):{};let g=o.get?function(c){return y("get"),o.get(this,c,s)}:function(c){return y("get"),this[c]},w=o.set?function(c,_){y("set");let x=o.set(this,c,_,s)}:function(c,_){y("set"),this[c]=_},A=a.getOwnPropertyNames(n),D={};A.forEach(function(c){if((l||b)&&c in s)return;let _=a.getOwnPropertyDescriptor(n,c),x={enumerable:Boolean(_.enumerable),get:g.bind(n,c),set:w.bind(n,c)};a.defineProperty(s,c,x),D[c]=!0});let k=!0;if(l||b){let c=a.setPrototypeOf||([].__proto__===Array.prototype?function(x,j){return f(j),x.__proto__=j,x}:m);P&&c(s,P)||(k=!1)}if(o.get||!k)for(let c in n)D[c]||a.defineProperty(s,c,{get:g.bind(n,c)});return a.seal(n),a.seal(s),s},r.revocable=function(n,o){return{proxy:new r(n,o),revoke:e}},r}});var S=E(I=>{"use strict";Object.defineProperty(I,"__esModule",{value:!0}),function(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}(I,{create:function(){return at},unwrap:function(){return W}});var ct=(0,function(t){return t&&t.__esModule?t:{default:t}}(z()).default)(),Z=function(t,e,r){if(typeof t!="object"||t===null)return t;var i={get:function(f,a){if(a==="__rawObject__")return f;var u=null,d=r.concat(a),m=f[a];return e.push({path:d,value:m}),Z(m,e,d)}};try{return new Proxy(t,i)}catch(f){return new ct(t,i)}};function at(t,e){return Z(t,e,[])}function W(t){if(t!==null&&typeof t=="object"&&typeof t.__rawObject__!="object"){if(Array.isArray(t))return t.map(function(r){return W(r)});var e={};return Object.keys(t).forEach(function(r){e[r]=W(t[r])}),e}return typeof t!="object"||t===null||typeof t.__rawObject__!="object"?t:t.__rawObject__}});var Y=E(F=>{"use strict";Object.defineProperty(F,"__esModule",{value:!0}),Object.defineProperty(F,"behavior",{enumerable:!0,get:function(){return lt}});var G,ft=J(require("rfdc")),st=J(require("fast-deep-equal")),O=L(H()),T=L(S());function J(t){return t&&t.__esModule?t:{default:t}}function K(t){if(typeof WeakMap!="function")return null;var e=new WeakMap,r=new WeakMap;return(K=function(i){return i?r:e})(t)}function L(t,e){if(!e&&t&&t.__esModule)return t;if(t===null||typeof t!="object"&&typeof t!="function")return{default:t};var r=K(e);if(r&&r.has(t))return r.get(t);var i={},f=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in t)if(a!=="default"&&Object.prototype.hasOwnProperty.call(t,a)){var u=f?Object.getOwnPropertyDescriptor(t,a):null;u&&(u.get||u.set)?Object.defineProperty(i,a,u):i[a]=t[a]}return i.default=t,r&&r.set(t,i),i}var Q=(0,ft.default)({proto:!0});(function(t){t[t.CREATED=0]="CREATED",t[t.ATTACHED=1]="ATTACHED"})(G||(G={}));var pt=0;function X(t,e){return t===e||t!=t&&e!=e}var lt=Behavior({lifetimes:{attached:function(){this.setData({_computedWatchInit:1})},created:function(){this.setData({_computedWatchInit:0})}},definitionFilter:function(t){var e,r=t.computed,i=t.watch,f=[],a=pt++;f.push({fields:"_computedWatchInit",observer:function(){var u=this,d=this.data._computedWatchInit;if(d===0){var m={computedUpdaters:[],computedRelatedPathValues:{},watchCurVal:{},_triggerFromComputedAttached:{}};this._computedWatchInfo||(this._computedWatchInfo={}),this._computedWatchInfo[a]=m,i&&Object.keys(i).forEach(function(n){var o=O.parseMultiDataPaths(n).map(function(h){var y=h.path,v=h.options,P=O.getDataOnPath(u.data,y);return v.deepCmp?Q(P):P});m.watchCurVal[n]=o})}else if(d===1){var p=this._computedWatchInfo[a];r&&Object.keys(r).forEach(function(n){var o,h=r[n],y=[],v=h(T.create(u.data,y)),P=y.map(function(l){var b=l.path;return{path:b,value:O.getDataOnPath(u.data,b)}});u.setData(((o={})[n]=T.unwrap(v),o)),p._triggerFromComputedAttached[n]=!0,p.computedRelatedPathValues[n]=P;var s=function(){for(var l,b=p.computedRelatedPathValues[n],g=!1,w=0;w<b.length;w++){var A=b[w],D=A.path,k=A.value,c=O.getDataOnPath(u.data,D);if(!X(k,c)){g=!0;break}}if(!g)return!1;var _=[],x=h(T.create(u.data,_));u.setData(((l={})[n]=T.unwrap(x),l));var j=_.map(function(et){var B=et.path;return{path:B,value:O.getDataOnPath(u.data,B)}});return p.computedRelatedPathValues[n]=j,!0};p.computedUpdaters.push(s)})}}}),r&&f.push({fields:"**",observer:function(){var u,d=this;if(this._computedWatchInfo){var m=this._computedWatchInfo[a];if(m)do u=m.computedUpdaters.some(function(p){return p.call(d)});while(u)}}}),i&&Object.keys(i).forEach(function(u){var d=O.parseMultiDataPaths(u);f.push({fields:u,observer:function(){var m=this;if(this._computedWatchInfo){var p=this._computedWatchInfo[a];if(p){if(Object.keys(p._triggerFromComputedAttached).length){var n={};for(var o in d.forEach(function(l){return n[l.path[0]]=!0}),p._triggerFromComputedAttached)if(p._triggerFromComputedAttached.hasOwnProperty(o)&&n[o]&&p._triggerFromComputedAttached[o]){p._triggerFromComputedAttached[o]=!1;return}}var h=p.watchCurVal[u],y=d.map(function(l){var b=l.path,g=l.options,w=O.getDataOnPath(m.data,b);return{val:w,options:g}}),v=y.map(function(l){var b=l.val;return l.options.deepCmp?Q(b):b});p.watchCurVal[u]=v;for(var P=!1,s=0;s<v.length;s++)if(d[s].options.deepCmp?!(0,st.default)(h[s],v[s]):!X(h[s],v[s])){P=!0;break}P&&i[u].apply(this,y.map(function(l){return l.val}))}}}})}),typeof t.observers!="object"&&(t.observers={}),Array.isArray(t.observers)?(e=t.observers).push.apply(e,f):f.forEach(function(u){var d=t.observers[u.fields];d?t.observers[u.fields]=function(){u.observer.call(this),d.call(this)}:t.observers[u.fields]=u.observer})}})});"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),function(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}(exports,{DataTracerMode:function(){return R},behavior:function(){return V.behavior},ComponentWithComputed:function(){return dt},BehaviorWithComputed:function(){return ht},getCurrentDataTracerMode:function(){return yt},setCurrentDataTracerMode:function(){return vt}});var R,V=Y();function dt(t){return Array.isArray(t.behaviors)||(t.behaviors=[]),t.behaviors.unshift(V.behavior),Component(t)}function ht(t){return Array.isArray(t.behaviors)||(t.behaviors=[]),t.behaviors.unshift(V.behavior),Behavior(t)}(function(t){t[t.Auto=0]="Auto",t[t.Proxy=1]="Proxy",t[t.DefineProperty=2]="DefineProperty"})(R||(R={}));var tt=0,yt=function(){return tt},vt=function(t){tt=t};
