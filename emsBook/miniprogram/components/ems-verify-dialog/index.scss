.container {
  height: 100%;
  background-color: white;
  box-sizing: border-box;
  transition: all 0.6s;
  padding: calc(24rpx + env(safe-area-inset-bottom)) 24px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .app_info {
    display: flex;
    flex-direction: column;
    align-items: center;

    .title {
      display: flex;
      flex-direction: column;
      align-items: center;

      &_img {
        width: 400rpx;
        height: 400rpx;
      }

      &_text {
        margin-top: 32rpx;
        font-size: 40rpx;
        font-weight: 500;
      }
    }

    .subTitle {
      margin-top: 20rpx;
      font-size: 34rpx;
      color: gray;
    }
  }

  .actions {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    margin-top: 20rpx;

    .btn {
      min-width: 140rpx;
      margin: 0;
      border: none;
      box-shadow: none;
      font-size: 32rpx;

      &::after {
        border: none;
        box-shadow: none;
      }
    }

    .actions_cancel {
      color: #f40;
      &::after {
        border: none;
        box-shadow: none;
      }
    }

    .actions_confirm {
      margin-left: 20rpx;
      // background-color: #f40;
      color: #ffffff;

      &::after {
        border: none;
        box-shadow: none;
      }
    }
  }
}
