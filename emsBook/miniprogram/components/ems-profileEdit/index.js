import { connectComponent } from "mobx-miniprogram-lite";
import { rootState } from "../../store/index";
import { createInvoice, wxUpdateUserInfo } from "../../utils/api";
connectComponent({
  store: {
    ...rootState.user
  },

  properties: {
    userProfile: {
      type: String,
      value: "{}",
    },
    showAvatar: {
      type: Boolean,
      value: false,
    },
  },

  data: {
    formData: {
      avatar: "",
      nickname: "",
      phone: "",
      date: "",
      clinic: "1号诊所",
    },
  },
  /**           
   * 组件的方法列表
   */
  lifetimes: {
    attached() {
      this.updateForm()
    },
  },
  methods: {
    updateForm() {
      this.setData({
        formData: {
          ...this.data.formData,
          ...JSON.parse(this.data.userProfile),
          ...this.data.profile,
        },
      });
    },
    async onSubmit(e) {
      try {
        if (this.data.showAvatar) {
          // 等待Promise完成并获取结果
          const result = await this.store.profile.updateProfile(e.detail.value);
          // 更新成功后关闭当前页面，返回上一页
          wx.navigateBack({
            delta: 1,
          });
        } else {
          // 处理非用户信息更新的情况
          const result = await createInvoice({});
          wx.showToast({
            title: result.resultMsg,
            icon: result.isSuccess ? "success" : "fail",
            duration: 3000,
          });
        }
      } catch (error) {
        console.error("提交表单失败:", error);
        wx.showToast({
          title: "操作失败，请重试",
          icon: "none",
          duration: 2000,
        });
      }
    },
    confirm(e) { },
    onChooseAvatar(e) {
      console.log(e);
    },
  },
});
