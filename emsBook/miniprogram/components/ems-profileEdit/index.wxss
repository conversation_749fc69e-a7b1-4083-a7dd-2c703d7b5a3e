.avatar-wrapper {
  border-radius: 50%;
  overflow: hidden;
  width: 120rpx;
  height: 120rpx;
  padding: 0;
  border: none;
}

.avatar-wrapper::after {
  content: "";
  border: none;
}

.avatar-wrapper .avatar {
  display: block;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
}

.form-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
  max-height: 240rpx;
  min-height: 80rpx;
  padding: 20rpx;
}
.form-item text {
  width: 200rpx;
}
.form-item text::after {
  content: " : ";
  font-size: 30rpx;
  font-weight: 400;
}
.form-item input {
  flex: 1;
  height: 100%;
  line-height: 100%;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 12rpx 12rpx;
  align-items: center;
}
