.calendar {
  width: 100%;
  background-color: #fff;
  padding: 40rpx 20rpx;
  margin: 0 auto;
  border-radius: 24rpx;
}
.controller {
  display: flex;
  flex-direction: row;
}
.control-item {
  font-size: 26rpx;
  padding: 0 14px;
}
.date-display {
  font-size: 28rpx;
  padding-left: 22rpx;
}
.date {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin: 0 0 20rpx;
}
.divide {
  width: 91%;
  height: 4rpx;
  background-color: #eceef0;
  margin: 28rpx auto;
}
.week {
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.days {
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
}
.day {
  width: 60rpx;
  height: 60rpx;
  text-align: center;
  padding: 8rpx;
  margin: 6rpx 12rpx;
  font-size: 28rpx;
  line-height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.pre-month,
.next-month {
  opacity: 0.4;
}
.weekend {
  color: #f75676;
}
.icon {
  width: 32rpx;
  height: 32rpx;
  margin-top: 10rpx;
}

.today {
  border-bottom: 3rpx solid #0075ff;
  margin: 6rpx 12rpx 3rpx;
}
.selected-date,
.range-pick {
  color: #fff;
  background-color: #0075ff;
  border-radius: 50%;
  border: none;
}
.inrange {
  background-color: #e6f2ff;
}
