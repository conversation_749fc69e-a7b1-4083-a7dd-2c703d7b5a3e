<wxs module="rangeStyle">
  var handle = function (day, dateRange) {
    if (dateRange.indexOf(day.date) !== -1) return 'range-pick';
  };

  module.exports.handle = handle;
</wxs>
<view style="display: flex;justify-content: space-between;align-items: center; margin: 40rpx 20rpx;">
  <text>当前选择</text>
  <text>{{selectedDate}}</text>
</view>
<view class="calendar">
  <view wx:if="{{!weekMode}}" class="header">
    <view class="date">
      <picker mode="date" fields="month" value="{{pickDate}}" bindchange="bindPickDateChange">
        <text class="date-display">{{pickDateDisplay}}</text>
      </picker>
      <view class="controller">
        <view class="control-item" bindtap="control" data-mode="pre">
          <image src="https://images.vrm.cn/2019/08/29/left-arrow.png" class="icon"></image>
        </view>
        <view class="control-item" bindtap="control" data-mode="reset">
          <image src="https://images.vrm.cn/2019/08/29/rotate.png" class="icon"></image>
        </view>
        <view class="control-item" bindtap="control" data-mode="next">
          <image src="https://images.vrm.cn/2019/08/29/right-arrow.png" class="icon"></image>
        </view>
      </view>
    </view>
    <view class="divide"></view>
  </view>
  <view class="days">
    <view wx:for="{{weekHeader}}" class="day {{(weekend[0]==index||weekend[1]==index)&&'weekend'}}">{{item}}</view>
    <view class="day {{item.position}} {{item.week === 6 || item.week === 0 ? 'weekend' : ''}} {{selectedDate === item.date ? 'selected-date' : ''}} {{today === item.date && showToday ? 'today' : ''}} {{item.inRange ? 'inrange' : ''}} {{rangeStyle.handle(item, dateRange)}}" wx:for="{{allDays}}" bindtap="onPickDay" data-day="{{item}}">
      {{item.dateNumber}}
    </view>
  </view>
</view>