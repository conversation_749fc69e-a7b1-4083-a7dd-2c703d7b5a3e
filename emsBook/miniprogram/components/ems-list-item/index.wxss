.item {
  border-radius: 16rpx;
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 16rpx;
  box-sizing: border-box;
  background-color: white;
}
.item_header {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.item_header_img {
  width: 120rpx;
}

.item_header_intro {
  flex: 1;
  margin: 0 10rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.item_header_intro_title {
  font-size: 32rpx;
  font-weight: bolder;
  line-height: 64rpx;
}

.item_header_intro_time {
  font-size: 24rpx;
  line-height: 48rpx;
}

.item_header_action {
  padding: 16rpx;
  border-radius: 50%;
  width: 100rpx;
  height: 100rpx;
  margin: 0;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f40;
  color: white;
  font-size: 60rpx;
  font-weight: 600;
  box-shadow: 0rpx 2rpx 20rpx -6rpx rgba(193, 211, 228, 1);
}
.item_header_action::after {
  content: "";
  border: none;
  box-shadow: none;
}

.item_desc {
  margin-top: 24rpx;
  border-top: solid 1px #f5f5f5;
  padding-top: 24rpx;
  font-size: 30rpx;
  font-weight: 300;
  line-height: 50rpx;
}
