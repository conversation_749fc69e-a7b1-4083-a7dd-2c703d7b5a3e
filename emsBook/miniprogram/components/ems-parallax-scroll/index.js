const { shared } = wx.worklet;
const app = getApp();
const { windowHeight } = app.globalData.sysInfo;
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    items: {
      type: Array,
      value: [],
    },
    parallaxSpeed: {
      type: Number,
      value: 0.5,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    windowHeight,
  },
  lifetimes: {
    attached() {
      this.setData({
        list: this.data.items
      })
    },
    ready() {
      for (let index = 0; index < this.data.items.length; index++) {
        this.bindAnimated(index)
      }
    },
    created() {
      this.offset = shared(0);
    }
  },
  /**
   * 组件的方法列表
   */
  methods: {
    updateList(current) {
      // const prev = current - 1
      // const next = current + 1 <= this.data.items.length ? current + 1 : 0
    },

    pageScroll: function (e) {
      "worklet";
      this.offset.value = e.detail.scrollTop;
    },
    bindAnimated(index) {
      this.applyAnimatedStyle(
        `#section${index}`,
        () => {
          "worklet";
          return {
            backgroundPositionY: `${(this.offset.value + index * -this.data.windowHeight) *
              this.data.parallaxSpeed
              }px`,
          }
        },
        {
          immediate: false,
          flush: 'async'
        },
        (res) => {
        }
      )
    },
  },
});
