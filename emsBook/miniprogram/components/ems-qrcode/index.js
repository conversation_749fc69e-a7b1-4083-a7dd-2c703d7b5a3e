// components/ems-qrcode/ems-qrcode.js
import { Dom } from "../../utils/common/dom";
import drawQrcode from "./qrcode.js";
Component({
  /**
   * 页面的初始数据
   */
  data: {
    url: "",
    text: "",
    loading: true,
    canvas: null,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  properties: {
    url: String,
    text: String,
    title: String,
  },
  pageLifetimes: {},
  lifetimes: {
    attached() {
      const { url } = this.data;
      if (url) {
        this.setData({
          url,
        });
      }
    },
    ready() {
      if (this.data.text) {
        new Dom({
          ins: this,
          node: "#myQrcode",
          opts: {
            node: true,
            size: false,
            rect: false,
          },
        }).then((res) => {
          const canvas = res.getNode(300, 300);
          this.data.canvas = canvas;
          this.refresh();
        });
      }
    },
  },
  methods: {
    refresh() {
      this.setData({ loading: true });
      this.data.canvas.requestAnimationFrame(() => {
        drawQrcode({
          canvas: this.data.canvas,
          canvasId: "myQrcode",
          width: 300,
          padding: 30,
          background: "#ffffff",
          foreground: "#000000",
          text: this.data.text,
        })?.then(() => {
          this.setData({
            loading: false,
          });
        });
      });
    },
  },
});
