$uni-bg-color-grey: #f5f5f5;

.empty-content {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background: $uni-bg-color-grey;

  &-image {
    width: 200rpx;
    height: 200rpx;
    // mix-blend-mode: multiply;
    // isolation: isolate;
  }

  .subc {
    margin-top: 16rpx;
    font-size: 34rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(144, 147, 153, 1);
    line-height: 48rpx;
    margin-bottom: 40rpx;
  }

  .main {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 28upx;

    .navigator {
      width: 143rpx;
      height: 46rpx;
      background: linear-gradient(12deg, #26b30a, #15b71c);
      box-shadow: 2rpx 2rpx 38rpx 5rpx rgba(22, 183, 27, 0.34);
      border-radius: 23rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #ffffff;
      font-size: 24rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
      line-height: 33rpx;
    }
  }
}
