.social-interaction {
  width: 100%;
}

/* 互动按钮区域 */
.interaction-buttons {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
}

.interaction-button {
  display: flex;
  align-items: center;
  margin-right: 40rpx;
}

.button-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.button-icon image {
  width: 100%;
  height: 100%;
}

.button-text {
  font-size: 26rpx;
  color: #666666;
}

.button-icon.active,
.button-text.active {
  color: #07c160;
}

/* 评论面板 */
.comment-panel-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  align-items: flex-end;
}

.comment-panel {
  width: 100%;
  height: 70vh;
  background-color: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.panel-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.panel-close {
  font-size: 40rpx;
  color: #999999;
  padding: 0 20rpx;
}

/* 评论列表 */
.comment-list {
  flex: 1;
  padding: 0 30rpx;
  overflow-y: auto;
}

.comment-item {
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.comment-user {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 28rpx;
  color: #333333;
  font-weight: bold;
  margin-bottom: 6rpx;
}

.comment-time {
  font-size: 24rpx;
  color: #999999;
}

.comment-content {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.5;
  margin-bottom: 20rpx;
  padding-left: 100rpx;
}

.reply-to {
  color: #07c160;
  margin-right: 10rpx;
}

.comment-actions {
  display: flex;
  justify-content: flex-end;
}

.action-button {
  font-size: 24rpx;
  color: #666666;
  margin-left: 30rpx;
}

.action-button.delete {
  color: #ff4d4f;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

/* 加载更多 */
.loading-more {
  text-align: center;
  padding: 20rpx 0;
  color: #999999;
  font-size: 24rpx;
}

/* 评论输入区域 */
.comment-input-area {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #ffffff;
}

.comment-input {
  flex: 1;
  height: 72rpx;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}

.send-button {
  width: 120rpx;
  height: 72rpx;
  margin-left: 20rpx;
  background-color: #07c160;
  color: #ffffff;
  font-size: 28rpx;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.send-button[disabled] {
  background-color: #cccccc;
  color: #ffffff;
}

/* 分享面板 */
.share-panel-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  align-items: flex-end;
}

.share-panel {
  width: 100%;
  background-color: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  padding-bottom: 40rpx;
}

.share-options {
  display: flex;
  justify-content: space-around;
  padding: 40rpx 30rpx;
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: transparent;
  padding: 0;
  line-height: normal;
  border: none;
  width: auto;
  margin: 0;
}

.share-option::after {
  border: none;
}

.share-option image {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
}

.share-option text {
  font-size: 28rpx;
  color: #333333;
}

/* 海报预览 */
.poster-preview-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.poster-preview {
  width: 80%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.poster-image {
  width: 100%;
  height: 900rpx;
  margin-bottom: 40rpx;
}

.save-poster-button {
  width: 300rpx;
  height: 80rpx;
  background-color: #07c160;
  color: #ffffff;
  font-size: 28rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}