<view class="social-interaction">
    <!-- 互动按钮区域 -->
    <view class="interaction-buttons">
        <!-- 点赞按钮 -->
        <view class="interaction-button" bindtap="handleLike">
            <view class="button-icon {{isLiked ? 'active' : ''}}">
                <image src="/static/icons/{{isLiked ? 'like_filled' : 'like'}}.png"></image>
            </view>
            <text class="button-text {{isLiked ? 'active' : ''}}">{{likeCount || '点赞'}}</text>
        </view>
        <!-- 收藏按钮 -->
        <view class="interaction-button" bindtap="handleFavorite">
            <view class="button-icon {{isFavorited ? 'active' : ''}}">
                <image src="/static/icons/{{isFavorited ? 'star_filled' : 'star'}}.png"></image>
            </view>
            <text class="button-text {{isFavorited ? 'active' : ''}}">
                {{favoriteCount || '收藏'}}
            </text>
        </view>
        <!-- 评论按钮 -->
        <view class="interaction-button" bindtap="showCommentPanel">
            <view class="button-icon">
                <image src="/static/icons/comment.png"></image>
            </view>
            <text class="button-text">{{commentCount || '评论'}}</text>
        </view>
        <!-- 分享按钮 -->
        <view class="interaction-button" bindtap="handleShare">
            <view class="button-icon">
                <image src="/static/icons/share.png"></image>
            </view>
            <text class="button-text">分享</text>
        </view>
    </view>
    <!-- 评论面板 -->
    <view class="comment-panel-mask" wx:if="{{showComments}}" bindtap="hideCommentPanel" catchtouchmove="preventTouchMove">
        <view class="comment-panel" catchtap="stopPropagation">
            <view class="panel-header">
                <text class="panel-title">评论 ({{commentCount || 0}})</text>
                <text class="panel-close" bindtap="hideCommentPanel">×</text>
            </view>
            <!-- 评论列表 -->
            <scroll-view class="comment-list" scroll-y="true" bindscrolltolower="loadMoreComments">
                <block wx:if="{{comments.length > 0}}">
                    <view class="comment-item" wx:for="{{comments}}" wx:key="id">
                        <view class="comment-user">
                            <image class="user-avatar" src="{{item.userAvatar}}"></image>
                            <view class="user-info">
                                <text class="user-name">{{item.userName}}</text>
                                <text class="comment-time">{{item.createTime}}</text>
                            </view>
                        </view>
                        <view class="comment-content">
                            <text class="reply-to" wx:if="{{item.replyToUserName}}">
                                @{{item.replyToUserName}}
                            </text>
                            <text>{{item.content}}</text>
                        </view>
                        <view class="comment-actions">
                            <text class="action-button" bindtap="replyComment" data-id="{{item.id}}" data-username="{{item.userName}}">
                                回复
                            </text>
                            <text class="action-button delete" wx:if="{{item.isOwner}}" bindtap="deleteComment" data-id="{{item.id}}">
                                删除
                            </text>
                        </view>
                    </view>
                </block>
                <!-- 空状态 -->
                <view class="empty-state" wx:if="{{comments.length === 0}}">
                    <ems-empty text="暂无评论，快来发表第一条评论吧"></ems-empty>
                </view>
                <!-- 加载更多 -->
                <view class="loading-more" wx:if="{{isLoading && comments.length > 0}}">
                    <text>加载中...</text>
                </view>
            </scroll-view>
            <!-- 评论输入框 -->
            <view class="comment-input-area">
                <input class="comment-input" placeholder="{{replyTo ? '回复 ' + replyTo : '说点什么...'}}" value="{{commentText}}" bindinput="onCommentInput" confirm-type="send" bindconfirm="submitComment" focus="{{inputFocus}}" />
                <button class="send-button" bindtap="submitComment" disabled="{{!commentText}}">
                    发送
                </button>
            </view>
        </view>
    </view>
    <!-- 分享面板 -->
    <view class="share-panel-mask" wx:if="{{showSharePanel}}" bindtap="hideSharePanel" catchtouchmove="preventTouchMove">
        <view class="share-panel" catchtap="stopPropagation">
            <view class="panel-header">
                <text class="panel-title">分享</text>
                <text class="panel-close" bindtap="hideSharePanel">×</text>
            </view>
            <view class="share-options">
                <button class="share-option" open-type="share">
                    <image src="/static/icons/wechat.png"></image>
                    <text>分享给好友</text>
                </button>
                <view class="share-option" bindtap="generatePoster">
                    <image src="/static/icons/poster.png"></image>
                    <text>生成海报</text>
                </view>
            </view>
        </view>
    </view>
    <!-- 海报预览 -->
    <view class="poster-preview-mask" wx:if="{{showPosterPreview}}" bindtap="hidePosterPreview" catchtouchmove="preventTouchMove">
        <view class="poster-preview" catchtap="stopPropagation">
            <image class="poster-image" src="{{posterUrl}}" mode="aspectFit"></image>
            <button class="save-poster-button" bindtap="savePoster">保存到相册</button>
        </view>
    </view>
</view>