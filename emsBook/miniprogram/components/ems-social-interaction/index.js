// components/ems-social-interaction/index.js
import { socialStore } from "../../store/social/index";
import { getShareParams, generatePoster, recordShare } from "../../utils/share";

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 互动项目ID
    itemId: {
      type: String,
      value: "",
    },
    // 互动项目类型：book(图书)、order(订单)
    itemType: {
      type: String,
      value: "book",
    },
    // 点赞数量
    likeCount: {
      type: Number,
      value: 0,
    },
    // 收藏数量
    favoriteCount: {
      type: Number,
      value: 0,
    },
    // 评论数量
    commentCount: {
      type: Number,
      value: 0,
    },
    // 是否已点赞
    isLiked: {
      type: Boolean,
      value: false,
    },
    // 是否已收藏
    isFavorited: {
      type: Boolean,
      value: false,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    showComments: false, // 是否显示评论面板
    showSharePanel: false, // 是否显示分享面板
    showPosterPreview: false, // 是否显示海报预览
    comments: [], // 评论列表
    commentText: "", // 评论文本
    replyTo: "", // 回复对象
    replyToId: "", // 回复评论ID
    inputFocus: false, // 输入框焦点
    inputDisabled: false, // 输入框禁用状态
    isLoading: false, // 加载状态
    currentPage: 1, // 当前页码
    pageSize: 15, // 每页数量，增加以减少加载次数
    hasMore: true, // 是否有更多评论
    posterUrl: "", // 海报URL
    lastInteractionTime: 0, // 上次交互时间，用于防抖
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 处理点赞
     */
    handleLike() {
      // 使用防抖处理，避免短时间内多次点击
      if (!this.debounceAction('like', 500)) return;
      
      const { itemId, itemType } = this.properties;
      
      // 立即更新UI状态，提高响应速度
      const newIsLiked = !this.properties.isLiked;
      const newLikeCount = newIsLiked
        ? this.properties.likeCount + 1
        : Math.max(0, this.properties.likeCount - 1);
      
      this.setData({
        isLiked: newIsLiked,
        likeCount: newLikeCount,
      });
      
      // 触发事件通知父组件
      this.triggerEvent("likeChange", {
        isLiked: newIsLiked,
        count: newLikeCount,
      });

      // 调用社交状态管理中的点赞方法
      wx.nextTick(() => {
        socialStore
          .toggleLike({
            itemId,
            itemType,
          })
          .catch((error) => {
            console.error('点赞操作失败', error);
            // 操作失败时恢复原状态
            this.setData({
              isLiked: !newIsLiked,
              likeCount: !newIsLiked
                ? this.properties.likeCount + 1
                : Math.max(0, this.properties.likeCount - 1),
            });
          });
      });
    }
    },

    /**
     * 处理收藏
     */
    handleFavorite() {
      // 使用防抖处理，避免短时间内多次点击
      if (!this.debounceAction('favorite', 500)) return;
      
      const { itemId, itemType } = this.properties;

      // 立即更新UI状态，提高响应速度
      const newIsFavorited = !this.properties.isFavorited;
      const newFavoriteCount = newIsFavorited
        ? this.properties.favoriteCount + 1
        : Math.max(0, this.properties.favoriteCount - 1);
      
      this.setData({
        isFavorited: newIsFavorited,
        favoriteCount: newFavoriteCount,
      });
      
      // 触发事件通知父组件
      this.triggerEvent("favoriteChange", {
        isFavorited: newIsFavorited,
        count: newFavoriteCount,
      });

      // 调用社交状态管理中的收藏方法
      wx.nextTick(() => {
        socialStore
          .toggleFavorite({
            itemId,
            itemType,
          })
          .catch((error) => {
            console.error('收藏操作失败', error);
            // 操作失败时恢复原状态
            this.setData({
              isFavorited: !newIsFavorited,
              favoriteCount: !newIsFavorited
                ? this.properties.favoriteCount + 1
                : Math.max(0, this.properties.favoriteCount - 1),
            });
          });
      });
    }
    },

    /**
     * 显示评论面板
     */
    showCommentPanel() {
      // 使用防抖处理，避免短时间内多次点击
      if (!this.debounceAction('showComment', 300)) return;
      
      this.setData({
        showComments: true,
        currentPage: 1,
        hasMore: true,
        inputDisabled: true // 初始禁用输入框，等评论加载完成后启用
      });

      // 如果有预加载的评论数据，直接使用
      if (this._cachedComments && this._cachedComments.length > 0) {
        this.setData({
          comments: this._cachedComments,
          commentCount: this._cachedCommentTotal || this.properties.commentCount,
          isLoading: false,
          inputDisabled: false
        });
        // 清除缓存
        this._cachedComments = null;
        this._cachedCommentTotal = null;
      } else {
        // 否则加载评论列表
        this.loadComments();
      }
    },

    /**
     * 隐藏评论面板
     */
    hideCommentPanel() {
      this.setData({
        showComments: false,
        commentText: "",
        replyTo: "",
        replyToId: "",
      });
    },

    /**
     * 加载评论列表
     */
    loadComments() {
      const { itemId, itemType } = this.properties;
      const { currentPage, pageSize } = this.data;

      this.setData({ isLoading: true });

      // 调用社交状态管理中的获取评论列表方法
      socialStore
        .getCommentsList({
          itemId,
          itemType,
          page: currentPage,
          pageSize,
        })
        .then(() => {
          // 批量更新本地状态，减少setData调用次数
          const newData = {
            comments: socialStore.comments,
            isLoading: false,
            hasMore: socialStore.comments.length < socialStore.commentTotal,
            inputDisabled: false // 启用输入框
          };
          
          // 只有当评论数量变化时才更新评论数量
          if (this.properties.commentCount !== socialStore.commentTotal) {
            newData.commentCount = socialStore.commentTotal;
            
            // 触发事件通知父组件
            wx.nextTick(() => {
              this.triggerEvent("commentChange", {
                count: socialStore.commentTotal,
              });
            });
          }
          
          this.setData(newData);
        })
        .catch(error => {
          console.error('加载评论失败', error);
          this.setData({
            isLoading: false,
            inputDisabled: false
          });
        });
    },

    /**
     * 加载更多评论
     */
    loadMoreComments() {
      // 检查是否有更多评论和是否正在加载
      if (!this.data.hasMore || this.data.isLoading) return;
      
      // 使用防抖处理，避免短时间内多次触发
      if (!this.debounceAction('loadMore', 300)) return;

      // 显示加载状态
      this.setData({
        isLoading: true,
        currentPage: this.data.currentPage + 1,
      });

      // 延迟加载，避免UI卡顿
      wx.nextTick(() => {
        this.loadComments();
      });
    },

    /**
     * 回复评论
     */
    replyComment(e) {
      const { id, username } = e.currentTarget.dataset;

      this.setData({
        replyTo: username,
        replyToId: id,
        inputFocus: true,
      });
    },

    /**
     * 删除评论
     */
    deleteComment(e) {
      const { id } = e.currentTarget.dataset;
      const { itemId, itemType } = this.properties;

      wx.showModal({
        title: "提示",
        content: "确定要删除这条评论吗？",
        success: (res) => {
          if (res.confirm) {
            // 调用社交状态管理中的删除评论方法
            socialStore
              .deleteComment(id, {
                itemId,
                itemType,
                page: 1,
                pageSize: this.data.pageSize,
              })
              .then((success) => {
                if (success) {
                  wx.showToast({
                    title: "删除成功",
                    icon: "success",
                  });

                  // 重置页码并重新加载评论
                  this.setData({
                    currentPage: 1,
                    hasMore: true,
                  });
                  this.loadComments();
                }
              });
          }
        },
      });
    },

    /**
     * 评论输入事件
     */
    onCommentInput(e) {
      this.setData({
        commentText: e.detail.value,
      });
    },

    /**
     * 提交评论
     */
    submitComment() {
      // 检查评论内容是否为空
      const trimmedComment = this.data.commentText.trim();
      if (!trimmedComment) return;
      
      // 使用防抖处理，避免短时间内多次提交
      if (!this.debounceAction('submitComment', 800)) return;
      
      // 禁用输入框，防止重复提交
      this.setData({
        inputDisabled: true
      });

      const { itemId, itemType } = this.properties;
      const { commentText, replyToId } = this.data;

      // 调用社交状态管理中的添加评论方法
      socialStore
        .addComment({
          itemId,
          itemType,
          content: commentText,
          parentId: replyToId || null,
        })
        .then((success) => {
          if (success) {
            wx.showToast({
              title: "评论成功",
              icon: "success",
            });

            // 清空输入框并重置回复状态
            this.setData({
              commentText: "",
              replyTo: "",
              replyToId: "",
            });

            // 重置页码并重新加载评论
            this.setData({
              currentPage: 1,
              hasMore: true,
            });
            this.loadComments();
          }
        });
    },

    /**
     * 处理分享
     */
    handleShare() {
      this.setData({
        showSharePanel: true,
      });
    },

    /**
     * 隐藏分享面板
     */
    hideSharePanel() {
      this.setData({
        showSharePanel: false,
      });
    },

    /**
     * 生成分享海报
     */
    generatePoster() {
      const { itemId, itemType } = this.properties;

      wx.showLoading({
        title: "生成海报中",
      });

      // 调用分享工具中的生成海报方法
      generatePoster({
        itemId,
        itemType,
      })
        .then((res) => {
          wx.hideLoading();

          if (res.success) {
            this.setData({
              posterUrl: res.data.posterUrl,
              showPosterPreview: true,
              showSharePanel: false,
            });

            // 记录分享行为
            recordShare({
              itemId,
              itemType,
              shareType: "poster",
            });
          } else {
            wx.showToast({
              title: "生成海报失败",
              icon: "none",
            });
          }
        })
        .catch(() => {
          wx.hideLoading();
          wx.showToast({
            title: "生成海报失败",
            icon: "none",
          });
        });
    },

    /**
     * 隐藏海报预览
     */
    hidePosterPreview() {
      this.setData({
        showPosterPreview: false,
      });
    },

    /**
     * 保存海报到相册
     */
    savePoster() {
      const { posterUrl } = this.data;

      if (!posterUrl) return;

      wx.showLoading({
        title: "保存中",
      });

      wx.getSetting({
        success: (res) => {
          if (!res.authSetting["scope.writePhotosAlbum"]) {
            wx.authorize({
              scope: "scope.writePhotosAlbum",
              success: () => {
                this.saveImageToAlbum(posterUrl);
              },
              fail: () => {
                wx.hideLoading();
                wx.showModal({
                  title: "提示",
                  content: "需要您授权保存图片到相册",
                  confirmText: "去授权",
                  success: (res) => {
                    if (res.confirm) {
                      wx.openSetting();
                    }
                  },
                });
              },
            });
          } else {
            this.saveImageToAlbum(posterUrl);
          }
        },
      });
    },

    /**
     * 保存图片到相册
     */
    saveImageToAlbum(filePath) {
      wx.saveImageToPhotosAlbum({
        filePath,
        success: () => {
          wx.hideLoading();
          wx.showToast({
            title: "保存成功",
            icon: "success",
          });
        },
        fail: () => {
          wx.hideLoading();
          wx.showToast({
            title: "保存失败",
            icon: "none",
          });
        },
      });
    },

    /**
     * 阻止滚动穿透
     */
    preventTouchMove() {
      return false;
    },

    /**
     * 阻止事件冒泡
     */
    stopPropagation() {
      return false;
    },

    /**
     * 预加载评论数据
     * 在后台预加载第一页评论，提高用户体验
     */
    preloadComments() {
      const { itemId, itemType } = this.properties;

      // 使用低优先级请求加载评论
      setTimeout(() => {
        socialStore
          .getCommentsList({
            itemId,
            itemType,
            page: 1,
            pageSize: this.data.pageSize,
          })
          .then(() => {
            // 缓存评论数据，但不更新UI
            this._cachedComments = socialStore.comments;
            this._cachedCommentTotal = socialStore.commentTotal;
          })
          .catch((err) => {
            console.error("预加载评论失败", err);
          });
      }, 500);
    },

    /**
     * 防抖处理函数
     * 防止用户短时间内多次点击按钮
     */
    debounceAction(action, delay = 300) {
      const now = Date.now();
      if (now - this.data.lastInteractionTime < delay) {
        return false;
      }

      this.setData({
        lastInteractionTime: now,
      });

      return true;
    },
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 检查当前用户是否已点赞和收藏
      const { itemId, itemType } = this.properties;

      // 使用Promise.all并行请求点赞和收藏数据，提高加载速度
      Promise.all([socialStore.getLikesList(), socialStore.getFavoritesList()])
        .then(() => {
          // 批量更新状态，减少setData调用次数
          const isLiked = socialStore.likes.some(
            (item) => item.itemId === itemId && item.itemType === itemType
          );

          const isFavorited = socialStore.favorites.some(
            (item) => item.itemId === itemId && item.itemType === itemType
          );

          // 只有当状态与属性不一致时才更新
          if (
            isLiked !== this.properties.isLiked ||
            isFavorited !== this.properties.isFavorited
          ) {
            this.setData({
              isLiked:
                isLiked !== this.properties.isLiked
                  ? isLiked
                  : this.properties.isLiked,
              isFavorited:
                isFavorited !== this.properties.isFavorited
                  ? isFavorited
                  : this.properties.isFavorited,
            });
          }
        })
        .catch((err) => {
          console.error("加载社交状态失败", err);
        });

      // 预加载评论数据，提高用户体验
      if (this.properties.commentCount > 0) {
        this.preloadComments();
      }
    },

    detached() {
      // 组件销毁时清理缓存数据
      this.setData({
        comments: [],
        currentPage: 1,
        hasMore: true,
      });
    },
  },

  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    // 页面被展示
    show() {
      const { itemId, itemType } = this.properties;

      // 页面显示时，如果评论面板打开，则刷新评论列表
      if (this.data.showComments) {
        this.loadComments();
      }

      // 页面显示时，异步刷新点赞和收藏状态
      setTimeout(() => {
        Promise.all([
          socialStore.getLikesList(),
          socialStore.getFavoritesList(),
        ])
          .then(() => {
            const isLiked = socialStore.likes.some(
              (item) => item.itemId === itemId && item.itemType === itemType
            );

            const isFavorited = socialStore.favorites.some(
              (item) => item.itemId === itemId && item.itemType === itemType
            );

            // 只有当状态与当前不一致时才更新
            if (
              isLiked !== this.properties.isLiked ||
              isFavorited !== this.properties.isFavorited
            ) {
              this.setData({
                isLiked: isLiked,
                isFavorited: isFavorited,
              });
            }
          })
          .catch((err) => {
            console.error("刷新社交状态失败", err);
          });
      }, 300);
    },

    // 页面隐藏
    hide() {
      // 页面隐藏时，如果评论面板打开，则关闭评论面板
      if (this.data.showComments) {
        this.hideCommentPanel();
      }

      // 页面隐藏时，如果分享面板打开，则关闭分享面板
      if (this.data.showSharePanel) {
        this.hideSharePanel();
      }

      // 页面隐藏时，如果海报预览打开，则关闭海报预览
      if (this.data.showPosterPreview) {
        this.hidePosterPreview();
      }
    },
  },
});
