<view class="card" style="background-color: {{bgColor}};" catch:tap="navigateTo">
  <share-element key="se-key{{index}}" rect-tween-type="cubic-bezier(0.4, 0.0, 0.2, 1.0)" worklet:onframe="handleFrame" transition-on-gesture style="width: 100%; height: 100%;">
    <view style="position: relative;">
      <view class="card_wrap card_body">
        <view class="info">
          <view class="row">
            <image class="info-img" src="{{item.img}}" mode="aspectFill" />
            <view class="column" style="margin-left: 20rpx;">
              <text class="n-1">{{item.name}}</text>
              <text class="mini-txt">{{item.description}}</text>
            </view>
          </view>
          <view class="follow-btn" catch:tap="preOrder" data-id="{{item.id}}"> 预约 </view>
        </view>
        <view class="card_desc {{visible}}" catch:tap="true" hover-class="active" hover-stay-time="1050">
          <image class="card_img" src="{{item.img}}" mode="aspectFill" />
          <view capture-bind:animationend="animationend" capture-bind:animationstart="animationstart" class=" card_over {{removeBody}} bottom-intro column">
            <text>门店出品,全是精品</text>
            <text class="mini-txt">{{item.price}}{{item.currency}}</text>
          </view>
        </view>
        <view class="row between card-footer" style="margin: 20rpx;">
          <text class="mini-txt">{{item.interval_count}}{{item.interval}}</text>
          <view class="operation row">
            <image class="opt-img" src="/static/images/good.png" mode="aspectFill" />
            <image class="opt-img" src="/static/images/more.png" mode="aspectFill" />
          </view>
        </view>
      </view>
    </view>
  </share-element>
</view>