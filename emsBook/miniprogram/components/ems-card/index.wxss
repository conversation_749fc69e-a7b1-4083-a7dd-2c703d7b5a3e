.card {
  overflow: hidden;
  flex-shrink: 0;

  width: 100%;
  padding: 20rpx;
}

.card_wrap {
  width: 100%;
  border-radius: 10px;
  box-sizing: border-box;
  transform-origin: 0 0;
  display: flex;
  flex-direction: column;
}

.info {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.row {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.between {
  justify-content: space-between;
}

.column {
  display: flex;
  flex-direction: column;
}

.center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.follow-btn {
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: 400;
  background-color: rgb(249, 224, 223);
}

.info-img {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.n-1 {
  font-weight: 400;
}

.card_img {
  width: 100%;
  height: 300px;
  border-radius: 20px;
}

.mini-txt {
  font-size: 13px;
  color: rgb(119, 108, 108);
}

.opt-img {
  width: 24px;
  height: 24px;
  margin-left: 20px;
  display: flex;
  align-content: center;
}

.bottom-intro {
  border-radius: 15px;
  padding: 15px;
  background-color: rgb(241, 233, 233);
}

.card_over {
  position: absolute;
  bottom: -160rpx;
  left: 40rpx;
  right: 40rpx;
  height: 160rpx;
  border-radius: 20rpx;
  box-shadow: 0px 0px 6px 2px rgba(0, 0, 0, 0.12);
  opacity: 0;
  transition: opacity 2.7s ease-in-out;
}

.card_desc {
  overflow: hidden;
}
.visible {
  overflow: visible;
  z-index: 2;
}
.removeBody {
  animation: remove-body 1s forwards;
}

.active .card_over {
  animation: show-body 1s forwards;
  opacity: 1;
  transition: opacity 0.3s;
}

@keyframes show-body {
  50% {
    transform: translateY(-165rpx);
  }
  100% {
    transform: translateY(-120rpx);
  }
}

@keyframes remove-body {
  0% {
    transform: translateY(-120rpx);
  }
  50% {
    transform: translateY(-165rpx);
  }
  100% {
    transform: translateY(40rpx);
  }
}
