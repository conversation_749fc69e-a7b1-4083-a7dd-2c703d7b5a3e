import { preOrder } from '../../utils/useFunc/usePreOrder'

const { shared } = wx.worklet

const FlightDirection = {
  PUSH: 0,
  POP: 1,
}

Component({
  options: {
    virtualHost: true,
  },
  properties: {
    index: {
      type: Number,
      value: -1,
    },
    item: {
      type: Object,
      value: {},
    },
    bgColor: {
      type: String,
      value: "transparent"
    }
  },
  data: {
    visible: "",
    removeBody: "",
    hover: ""
  },
  lifetimes: {
    created() {
      this.scale = shared(1)
      this.opacity = shared(0)
      this.direction = shared(0)
      this.srcWidth = shared('100%')
      this.radius = shared(5)

      const beginRect = shared(undefined)
      const endRect = shared(undefined)
      wx.worklet.runOnUI(() => {
        'worklet'
        globalThis['RouteCardSrcRect'] = beginRect
        globalThis['RouteCardDestRect'] = endRect
      })()

      if (this.renderer !== 'skyline') {
        this.applyAnimatedStyle = () => { }
      }
    },
    attached() {
      this.applyAnimatedStyle(
        '.card_wrap',
        () => {
          'worklet'
          return {
            width: this.srcWidth.value,
            transform: `scale(${this.scale.value})`,
          }
        },
        {
          immediate: false,
          flush: 'sync'
        },
        () => { },
      )

      this.applyAnimatedStyle(
        '.card_img',
        () => {
          'worklet'
          return {
            borderTopRightRadius: this.radius.value, // 不带单位默认是 px
            borderTopLeftRadius: this.radius.value,
          }
        },
        {
          immediate: true,
          flush: 'sync'
        },
        () => { },
      )

      this.applyAnimatedStyle(
        '.card_body',
        () => {
          'worklet'
          return {
            opacity: this.opacity.value,
          }
        },
        {
          immediate: false,
          flush: 'sync'
        },
        () => { },
      )
    },
  },
  pageLifetimes: {
    routeDone() {
      this.setData({
        visible: "",
        removeBody: ""
      })
    },

  },

  methods: {
    setActive(e) {
      this.setData({
        hover: 'active'
      })
    },
    noActive(e) {
      this.setData({
        hover: ''
      })
    },
    animationend(e) {
      if (e.detail.animationName == 'remove-body') {
        this.setData({
          removeBody: ""
        })
      } else {
        this.setData({
          removeBody: 'removeBody',
        })
      }
    },
    animationstart(e) {
      if (e.detail.animationName == 'show-body') {
        setTimeout(() => {
          this.setData({
            visible: 'visible'
          })
        }, 500)
      } else {
        setTimeout(() => {
          this.setData({
            visible: '',
          })
        }, 330)
      }


    },
    transtionend(e) {
      // console.log(e)
    },
    preOrder,
    navigateTo(e) {
      const { index, item } = this.data
      const urlContent = `/views/goods/detail/index?index=${index}&id=${item.id}&url=${encodeURIComponent(item.img)}&content=${item.content}&ratio=${1}&nickname=${item.nickname}`
      wx.navigateTo({
        url: urlContent,
        routeType: 'CardScaleTransition',
      })
    },
    handleFrame(data) {
      'worklet'
      // console.log('handleFrame', data)
      this.direction.value = data.direction
      if (data.direction === FlightDirection.PUSH) { // 进入
        // 飞跃过程中，卡片从 100% 改为固定宽度，通过 scale 手动控制缩放
        this.srcWidth.value = `${data.begin.width}px`
        this.scale.value = data.current.width / data.begin.width
        this.opacity.value = 1 - data.progress
        this.radius.value = 0
        // this.shareImgHeight.value = data.begin.height

      } else if (data.direction === FlightDirection.POP) { // 返回
        this.scale.value = data.current.width / data.end.width
        this.opacity.value = data.progress
        this.radius.value = 5
      }

      // globalThis 是 UI 线程的全局变量，将 share-element 初始和目标尺寸保存起来，用于下一页面的缩放动画的计算
      // TODO: 后续计划优化这里的接口设计
      if (globalThis['RouteCardSrcRect'] && globalThis['RouteCardSrcRect'].value == undefined) {
        globalThis['RouteCardSrcRect'].value = data.begin
      }
      if (globalThis['RouteCardDestRect'] && globalThis['RouteCardDestRect'].value == undefined) {
        globalThis['RouteCardDestRect'].value = data.end
      }
    },
  },
})
