<vertical-drag-gesture-handler worklet:ongesture="onGesture" worklet:should-accept-gesture='shouldAcceptGesture' worklet:should-response-on-move='shouldResponseOnMove'>
  <view class="container {{direction}}" bindtransitionend="transitionend">
    <view class="section prev ">
      <image lazy-load="{{true}}" class="bgImg" src="{{items[prev].bg}}" mode="scaleToFill" />
      <!-- <view class="card_title">
        {{items[prev].title}}
      </view> -->
      
    </view>
    <view class="section current" hover-class="hover">
      <image lazy-load="{{true}}" class="bgImg" src="{{items[current].bg}}" mode="scaleToFill" />
      <view class="card_body card_left">{{items[current].title}}</view>
      <view class="card_body card_right">{{items[next].title}}</view>
    </view>
    <view class="section next">
      <image lazy-load="{{true}}" class="bgImg" src="{{items[next].bg}}" mode="scaleToFill" />
      <!-- <view class="card_title">{{items[next].title}}</view> -->
    </view>
  </view>
</vertical-drag-gesture-handler>