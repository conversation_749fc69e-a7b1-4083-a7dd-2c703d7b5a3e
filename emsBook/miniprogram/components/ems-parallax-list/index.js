// components/ems-parallax-list/index.js
const { shared, runOnJS } = wx.worklet;
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    items: {
      type: Array,
      value: [],
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    direction: "",
    prev: 0,
    current: 0,
    next: 0,
    selected: 2
  },
  lifetimes: {
    created() {
      this.isAnimated = shared(false);
      this.current = shared(0);
    },
    attached() {
      this.resetItem(this.data.current);
    },
  },
  /**
   * 组件的方法列表
   */
  methods: {
    updateDirection(value = "") {
      this.isAnimated.value = !!value
      this.setData({
        direction: value,
      });
    },
    calcIndex(current, lens) {
      "worklet";
      const prev = current - 1 < 0 ? lens - 1 : current - 1;
      const next = current + 1 > lens - 1 ? 0 : current + 1;
      return {
        prev,
        current,
        next,
      };
    },
    resetItem(current) {
      const itemsLen = this.data.items.length;
      const newIndexes = this.calcIndex(current, itemsLen);
      
      // 检查是否需要更新，避免不必要的DOM操作
      if (this.data.prev !== newIndexes.prev || 
          this.data.current !== newIndexes.current || 
          this.data.next !== newIndexes.next) {
        this.setData(newIndexes);
      }
    },
    transitionend(e) {
      this.isAnimated.value = false;
      this.updateDirection();
      
      // 使用requestAnimationFrame延迟执行resetItem，减少闪烁
      wx.nextTick(() => {
        this.resetItem(this.current.value);
      });
    },
    shouldAcceptGesture(e) {
      "worklet"
      return !this.isAnimated.value
    },
    shouldResponseOnMove(e) {
      "worklet"
      return !this.isAnimated.value
    },
    activeCard(state) {
      this.setData({
        active: state == 0 ? 'hover' : "not-hover"
      })
    },

    onGesture(e) {
      "worklet";
      // runOnJS(this.activeCard.bind(this))(e.state)
      let direction = "";
      if (e.deltaY == 0) {
        return;
      }
      if (e.deltaY > 5) {
        direction = "down";
        this.current.value = this.calcIndex(
          this.current.value,
          this.data.items.length
        ).prev;

      } else if (e.deltaY < -5) {
        direction = "up";
        this.current.value = this.calcIndex(
          this.current.value,
          this.data.items.length
        ).next;
      }
      runOnJS(this.updateDirection.bind(this))(direction);
    },
  },
});
