.container {
  height: 100%;
}
.section {
  width: 100vw;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ffffff;
  font-size: 40rpx;
  font-weight: 600;
  overflow: hidden;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100vw 100vh;
  will-change: transform, height; /* 添加硬件加速 */
  transform: translateZ(0);
  /* transition: all 0.8s; */
}
.bgImg {
  position: absolute;
  width: 100vw;
  height: 100vh;
  z-index: -1;
  transition: transform 0.8s;
  will-change: transform;
}
.current {
  height: 100%;
}
.prev,
.next {
  z-index: 1;
  height: 0;
}
.prev {
  top: 0;
}
.next {
  bottom: 0;
}
.next .bgImg {
  bottom: 0;
  transform: translateY(20%);
}
.up .current {
  transform: translateY(-20%);
}
.up .next {
  height: 100%;
}
.up .next .bgImg {
  transform: translateY(0);
}

.down .current {
  transform: translateY(20%);
}
.down .prev {
  height: 100%;
  transform: translateZ(0);
}

.up .section {
  /* transition: transform 0.8s cubic-bezier(0.25, 0.8, 0.25, 1),
    height 0.8s cubic-bezier(0.25, 0.8, 0.25, 1); */
  transition: all 0.8s;
}
.down .section {
  /* transition: transform 0.8s cubic-bezier(0.25, 0.8, 0.25, 1),
    height 0.8s cubic-bezier(0.25, 0.8, 0.25, 1); */
  transition: all 0.8s;
}

.card_body {
  position: absolute;
  width: 0;
  height: 88rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  transition: all 0.8s ease-in-out;
  flex-wrap: nowrap;
  white-space: nowrap;
  opacity: 0;
  overflow: hidden;
  background: transparent;
  backdrop-filter: blur(30px);
  transform: translateZ(0);
  will-change: width, opacity;
}
.card_body::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #00000099;
  filter: blur(30px);
  margin: -30px;
  z-index: -1;
}
.card_left {
  top: 30%;
  left: 0;
  border-radius: 0 88rpx 88rpx 0;
}
.card_right {
  right: 0;
  bottom: 30%;

  border-radius: 88rpx 0 0 88rpx;
}

.hover .card_body {
  width: 50%;
  opacity: 1;
  transition: all 0.8s ease-in-out;
}
.hover .card_left {
  transform: translateZ(0);
}
.hover .card_right {
  transform: translateZ(0);
}
/* @keyframes show-body {
  50% {
    transform: translateY(-160rpx);
  }
  100% {
    transform: translateY(-120rpx);
  }
}

@keyframes remove-body {
  0% {
    transform: translateY(-120rpx);
  }
  50% {
    transform: translateY(-160rpx);
  }
  100% {
    transform: translateY(40rpx);
  }
}

@keyframes show-hidden {
  0% {
    overflow: visible;
    pointer-events: none;
  }
  50% {
    overflow: hidden;
  }
}

@keyframes remove-hidden {
  to {
    overflow: visible;

  }
}
.card_body {
  animation: show-hidden 2s forwards;
}

.hover {
  animation: remove-hidden 2s forwards;
}

.hover .card_body {
  animation: show-body 1s forwards;

  opacity: 1;
  transition: opacity 0.3s;
} */
