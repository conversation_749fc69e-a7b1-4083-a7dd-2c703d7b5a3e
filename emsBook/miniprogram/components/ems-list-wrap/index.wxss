.container {
  height: 100%;
  width: 100%;
  padding-bottom: env(safe-area-inset-bottom);
  display: flex;
  flex-direction: column;
}
.container .scroll {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 100%;
  /* overflow: auto; */
}

.noSafeBottom {
  padding-bottom: 0;
}

.footer {
  height: calc(60px + env(safe-area-inset-bottom));
}

.default-navbar {
  display: none;
}
.navbar:empty + default-navbar {
  display: block;
}
