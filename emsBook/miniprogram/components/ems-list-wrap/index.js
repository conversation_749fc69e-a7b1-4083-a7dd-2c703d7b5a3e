
import { navBack } from "../../utils/common/tools";
Component({
  /**
   * 组件的属性列表
   */
  options: {
    multipleSlots: true,
  },
  properties: {
    title: {
      type: String,
      value: "列表",
    },
    noSafeBottom: {
      type: Boolean,
      value: false,
    },
    noFooter: {
      type: Boolean,
      value: false,
    },
    leftIcon: {
      type: Boolean,
      value: true,
    },
    padding: {
      type: String,
      value: "20rpx",
    },
    bgColor: {
      type: String,
      value: "transparent",
    },
    listType: {
      type: String,
      value: "custom",
    },
    associativeContainer: {
      type: String,
      value: "",
    },
    isEmpty: {
      type: Boolean,
      value: true,
    },
    loadingStatus: {
      type: Boolean,
      value: false,
    },
    hasMore: {
      type: Boolean,
      value: true,
    },
  },
  lifetimes: {
    attached() { },
    ready() { },
  },
  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的方法列表
   */
  methods: {
    scroll(e) {
      this.triggerEvent("scroll", e, {});
    },

    clickLeft(e) {
      if (this.data.leftIcon) {
        navBack();
      } else {
        this.triggerEvent("clickLeft", e, {});
      }
    },

    reachBottom(e) {
      this.triggerEvent("reachBottom", e, {});
    },
  },
});
