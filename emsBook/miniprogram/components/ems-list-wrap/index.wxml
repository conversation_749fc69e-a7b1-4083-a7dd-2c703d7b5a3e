<view class="container {{noSafeBottom&&'noSafeBottom'}}">
  <view class="navbar">
    <slot name='navbar'>
    </slot>
  </view>
  <navbar class='default-navbar' title='{{title}}' bindback='clickLeft' back='{{leftIcon}}' homeButton='{{!leftIcon}}'></navbar>
  <slot name="extend">
  </slot>
  <scroll-view associative-container="{{associativeContainer}}" bounces="{{true}}" refresher-enabled='{{false}}' bindrefresherrefresh="refresh" show-scrollbar="{{false}}" scroll-y type='{{listType}}' bindscrolltolower="reachBottom" bind:scroll="scroll" class="scroll" style="padding: {{padding}};background-color: {{bgColor}};">
    <sticky-section>
      <sticky-header>
        <slot name="header"></slot>
      </sticky-header>
      <view wx:if="{{isEmpty}}" style="height: 100%;">
        <!-- <ems-empty content="暂无数据"></ems-empty> -->
      </view>
      <block wx:else="">
        <slot></slot>
        <view wx:if="{{loadingStatus}}" style="text-align: center; padding: 10px; color: #999;">
          加载中...
        </view>
        <view wx:if="{{!hasMore && !isEmpty}}" style="text-align: center; padding: 10px; color: #999;">
          没有更多了
        </view>
      </block>
      <view wx:if="{{!noFooter}}" class="footer" style="background-color: {{bgColor}};"></view>
    </sticky-section>
  </scroll-view>
</view>