$bgColor: #f4ece3;
$actionColor: #ebba6c;
.container {
  min-height: 160rpx;
  width: 100%;
  display: flex;
  flex-direction: row;
  margin-top: 60rpx;
  background-color: $bgColor;
  border-top-right-radius: 32rpx;
  .left {
    width: 200rpx;
    height: 160rpx;
    background: linear-gradient(
      to right,
      #eee 0,
      #eee 50%,
      $bgColor 50%,
      $bgColor 100%
    );
    margin-right: 16rpx;
    .img {
      position: absolute;
      left: 0rpx;
      top: -60rpx;
      width: 100%;
    }
  }
  .right {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    .title {
      font-size: 24rpx;
      font-weight: 300;
      color: gray;
    }
    .subTitle {
      font-size: 30rpx;
      font-weight: 500;
    }
    .price {
      font-size: 22rpx;
      font-weight: 300;
      color: rgb(184, 141, 129);
    }

    .action {
      height: 80rpx;
      width: 80rpx;
      position: absolute;
      bottom: 0;
      right: 0;
      background: linear-gradient(
        135deg,
        transparent 0,
        transparent 50%,
        $actionColor 50%,
        $actionColor 100%
      );

      display: flex;
      justify-content: flex-end;
      align-items: flex-end;
      padding: 12rpx;
    }
  }
}
