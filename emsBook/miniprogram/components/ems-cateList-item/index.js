// components/ems-cateList-item/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    item: {
      type: Object,
      value: {},
    },
  },

  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的方法列表
   */
  methods: {
    toDetail(e) {
      console.log(this.data.item.id);
      const { id } = e.currentTarget.dataset;
      wx.navigateTo({
        url: `/views/goods/cateDetail/index?id=${id}`,
      });
    },
    addCart(e) {},
  },
});
