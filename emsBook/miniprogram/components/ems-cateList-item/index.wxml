<view class="container" catch:tap="toDetail" data-id="{{item.id}}">
  <view class="left">
    <share-element key="item-{{item.id}}" shuttle-on-push="to" rect-tween-type="fastOutSlowIn" worklet:onframe="handleFrame" transition-on-gesture style="width: 100%; height: 100%;">
      <image class="img" src="{{item.cover_image_url}}" mode="widthFix" />
    </share-element>
  </view>
  <view class="right">
    <text class="title">{{item.name || "测试标题"}}</text>
    <text class="subTitle">{{item.description ||'测试子标题'}}</text>
    <text class="price">￥{{item.market_price || 9999}}</text>
    <share-element key="addCart">
      <view class="action" catch:tap="addCart">+</view>
    </share-element>
  </view>
</view>