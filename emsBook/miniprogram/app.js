// app.js
import { rootState } from "./store/index";

import { urlParse } from "./utils/util";
App({
  globalData: {
    shareuser: "",
    sysInfo: wx.getWindowInfo(),
    menuRect: wx.getMenuButtonBoundingClientRect(),
    themeData: {
      unSelectedColor: "#b4b6bc",
      selectedColor: "#e35048",
      primaryColor: "#e35048",
    },
  },
  onLaunch(option) {
    // appConfigStore.refreshConfig();
    // 登录
    this.globalData.shareuser = option.query.code || "0";
    if (urlParse(option.query.scene)) {
      this.globalData.shareuser = urlParse(option.query.scene).code || "0";
    }
    console.log(this.globalData.shareuser);
    rootState.auth.mpLogin();
    if (option.scene == 1154) return;
    const updateManager = wx.getUpdateManager();

    updateManager.onCheckForUpdate(function (res) {
      // 请求完新版本信息的回调
      console.log(res.hasUpdate);
    });
    updateManager.onUpdateReady(function () {
      wx.showModal({
        title: "更新提示",
        content: "新版本已经准备好，是否重启应用？",
        success(res) {
          if (res.confirm) {
            // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
            updateManager.applyUpdate();
          }
        },
        fail(err) {
          console.log(err);
        },
      });
    });
    updateManager.onUpdateFailed(function (res) {
      console.log(res);
    });
  },
});
