{"name": "miniprogram", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "miniprogram", "version": "1.0.0", "license": "ISC", "dependencies": {"miniprogram-computed": "^4.4.0", "mobx-miniprogram-lite": "^0.0.6"}}, "node_modules/fast-deep-equal": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-2.0.1.tgz", "integrity": "sha512-bCK/2Z4zLidyB4ReuIsvALH6w31YfAQDmXMqMx6FyfHqvBxtjC0eRumeSu4Bs3XtXwpyIywtSTrVT99BxY1f9w=="}, "node_modules/miniprogram-computed": {"version": "4.4.0", "resolved": "https://registry.npmjs.org/miniprogram-computed/-/miniprogram-computed-4.4.0.tgz", "integrity": "sha512-th9y1Ua7H6rC47Vs20/QFC6zlq/A/92zbKrCxkJNlXf7xwNdg86BRRFvccR8yIwVXRXjMYUrcsVDq6bwzbE0Cg==", "dependencies": {"fast-deep-equal": "^2.0.1", "rfdc": "^1.1.4"}}, "node_modules/mobx": {"version": "6.12.0", "resolved": "https://registry.npmjs.org/mobx/-/mobx-6.12.0.tgz", "integrity": "sha512-Mn6CN6meXEnMa0a5u6a5+RKrqRedHBhZGd15AWLk9O6uFY4KYHzImdt8JI8WODo1bjTSRnwXhJox+FCUZhCKCQ==", "funding": {"type": "opencollective", "url": "https://opencollective.com/mobx"}}, "node_modules/mobx-miniprogram-lite": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/mobx-miniprogram-lite/-/mobx-miniprogram-lite-0.0.6.tgz", "integrity": "sha512-GohzGs7Xdyyf9oXvMKd6eg/5wTDD9qMPl/j3j78OBeQicoIFh1vJ7K0KZbvYnz7WbkF5BvRvUIJhuJwMvCwMKw==", "dependencies": {"mobx": "^6.10.0"}}, "node_modules/rfdc": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/rfdc/-/rfdc-1.3.1.tgz", "integrity": "sha512-r5a3l5HzYlIC68TpmYKlxWjmOP6wiPJ1vWv2HeLhNsRZMrCkxeqxiHlQ21oXmQ4F3SiryXBHhAD7JZqvOJjFmg=="}}}