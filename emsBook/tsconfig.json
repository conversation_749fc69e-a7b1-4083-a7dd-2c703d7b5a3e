{"compilerOptions": {"strictNullChecks": true, "noImplicitAny": true, "module": "CommonJS", "target": "ES2020", "allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "noImplicitThis": true, "noImplicitReturns": true, "alwaysStrict": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "strict": true, "strictPropertyInitialization": true, "lib": ["ES2020"], "typeRoots": ["./typings"]}, "include": ["./**/*.js", "miniprogram/pages/map/index.js", "miniprogram/pages/map/index.js", "miniprogram/pages/ucenter/widgets/wallet/wallet.js", "miniprogram/pages/ucenter/widgets/profile/profile.js", "miniprogram/pages/home/<USER>", "miniprogram/components/ems-parallax-scroll/index.js", "miniprogram/components/ems-parallax-list/index.js"], "exclude": ["node_modules"]}