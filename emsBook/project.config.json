{"projectname": "booking", "description": "项目配置文件", "miniprogramRoot": "miniprogram", "compileType": "miniprogram", "setting": {"useCompilerPlugins": ["sass"], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "coverView": false, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": false, "packNpmRelationList": [], "ignoreUploadUnusedFiles": true, "compileHotReLoad": false, "skylineRenderEnable": true, "condition": true, "es6": true, "compileWorklet": true, "uglifyFileName": true, "swc": true, "minifyWXML": true}, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "srcMiniprogramRoot": "miniprogram/", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "libVersion": "2.32.3", "packOptions": {"ignore": [], "include": []}, "appid": "wx7c7e559acf0a4316"}