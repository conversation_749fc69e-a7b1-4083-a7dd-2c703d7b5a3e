# BookMaker 小程序

一个使用 MobX 状态管理的微信图书管理小程序。

## 项目结构

```
miniprogram/
├── app.js            - 小程序入口
├── app.json          - 全局配置
├── app.scss          - 全局样式
├── behavior/         - 共享行为
├── components/       - 可复用组件
│   ├── ems-calendar/
│   ├── ems-card/
│   ├── ems-profileEdit/
│   └── ...
├── custom-tab-bar/   - 自定义标签栏
├── pages/            - 页面组件
│   ├── home/         - 首页
│   ├── map/          - 地图页面
│   └── ucenter/      - 用户中心
├── store/            - MobX 状态管理
│   ├── index.js      - 主存储
│   ├── user/         - 用户存储
│   └── ...
└── utils/            - 工具函数
```

## 主要特点

- **状态管理**：使用 `mobx-miniprogram-lite` 实现响应式状态管理
- **计算属性**：使用 `miniprogram-computed` 实现派生数据
- **自定义组件**：包含多种可复用组件，如日历、卡片、个人资料编辑器等
- **主题支持**：支持主题行为以保持一致的样式

## 依赖项

- `mobx-miniprogram-lite`：轻量级小程序 MobX 实现
- `miniprogram-computed`：计算属性实现

## 开发指南

1. 安装依赖：

```bash
npm install
```

2. 在微信开发者工具中打开项目

3. 开发命令：

```bash
npm run dev
```

## 配置说明

- `project.config.json`：微信开发者工具的项目配置
- `app.json`：小程序全局配置
